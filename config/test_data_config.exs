# 测试数据管理配置文件

# 在你的 config/dev.exs 或 config/test.exs 中添加以下配置

# 测试数据管理基本配置
config :cypridina, :test_data_management,
  # 启用测试数据管理功能（仅在开发和测试环境）
  enabled: true,
  
  # 备份相关配置
  backup_dir: "./tmp/test_data_backups",
  backup_retention_days: 7,
  auto_backup: true,
  
  # 操作限制
  max_batch_size: 100,
  max_time_advance_days: 30,
  
  # 安全配置
  require_confirmation: true,
  log_all_operations: true,
  
  # 权限配置
  required_permissions: ["test_data_management"],
  dangerous_operation_permissions: ["test_data_management_advanced"]

# 在生产环境 config/prod.exs 中确保禁用
# config :cypridina, :test_data_management,
#   enabled: false

# 日志配置
config :logger,
  level: :info,
  compile_time_purge_matching: [
    [level_lower_than: :info]
  ]

# 添加测试数据管理的日志记录
config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id, :test_data_operation]