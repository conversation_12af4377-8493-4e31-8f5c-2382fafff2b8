# MasterPay88支付系统重置与配置指南

## 概述

本指南提供了完整的MasterPay88支付系统重置和重新配置的步骤，包括数据清理、新配置部署和验证测试。

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保应用正在运行
mix phx.server

# 检查数据库连接
mix ecto.gen.migration test_connection
```

### 2. 一键重置和重新配置

```bash
# 完整重置（推荐）
mix payment.reset --backup --force
mix run priv/repo/seeds/masterpay88_seeds.exs
mix payment.verify --connectivity
```

## 📋 详细步骤

### 第一步：备份现有数据

```bash
# 创建备份
mix payment.reset --backup --dry-run

# 查看备份内容
ls -la priv/payment_backups/
```

**预期输出：**
```
🗄️  创建数据备份: priv/payment_backups/payment_backup_20240115T120000Z.sql
📁 备份文件:
  - payment_backup_20240115T120000Z.sql
  - payment_backup_20240115T120000Z.sql.dump
```

### 第二步：重置支付配置

```bash
# 预览重置计划
mix payment.reset --dry-run

# 执行重置（保留订单历史）
mix payment.reset --keep-orders --force

# 或完全重置
mix payment.reset --force
```

**预期输出：**
```
🔄 开始重置支付系统...
🗑️  删除配置数据...
  ✅ 删除支付配置: 15 条
  ✅ 删除兑换配置: 3 条
  ✅ 删除提现配置: 4 条
  ✅ 删除网关配置: 2 条
  ✅ 删除支付网关: 3 条
✅ 数据重置完成
```

### 第三步：配置环境变量

创建或更新 `.env` 文件：

```bash
# MasterPay88配置
export MASTERPAY88_MERCHANT_ID="10236"
export MASTERPAY88_MERCHANT_KEY="ZNH2GYTGVP54ZMM9WSXOLVCKKS9EOBWM"
export MASTERPAY88_GATEWAY_URL="https://api.masterpay88.in/app-api"
export MASTERPAY88_TEST_MODE="false"

# 回调配置
export CALLBACK_DOMAIN="https://api.cypridina.com"
export MASTERPAY88_CALLBACK_IPS="*************"

# 金额限制
export MASTERPAY88_MIN_AMOUNT="10000"    # 100.00 INR
export MASTERPAY88_MAX_AMOUNT="5000000"  # 50,000.00 INR
```

**加载环境变量：**
```bash
source .env
```

### 第四步：运行MasterPay88种子文件

```bash
# 运行新的种子文件
mix run priv/repo/seeds/masterpay88_seeds.exs
```

**预期输出：**
```
🚀 开始初始化 MasterPay88 支付系统...
📋 配置信息:
  • 商户ID: 10236
  • 网关URL: https://api.masterpay88.in/app-api
  • 回调域名: https://api.cypridina.com
  • 测试模式: false

🏗️ 创建MasterPay88支付网关...
✅ MasterPay88充值网关创建成功 (ID: uuid-here)
✅ MasterPay88提现网关创建成功 (ID: uuid-here)

⚙️ 创建MasterPay88网关配置...
✅ MasterPay88网关配置创建成功

💳 创建支付方式配置...
✅ 创建支付配置: UPI支付
✅ 创建支付配置: UPI扫码支付
✅ 创建支付配置: 网银支付
✅ 创建支付配置: 电子钱包
✅ 创建支付配置: 借记卡
✅ 创建支付配置: 信用卡

💱 创建兑换配置...
✅ 创建兑换配置: 标准游戏币兑换
✅ 创建兑换配置: VIP优享兑换
✅ 创建兑换配置: 超级VIP兑换

🏦 创建提现配置...
✅ 创建提现配置: UPI快速提现
✅ 创建提现配置: UPI大额提现
✅ 创建提现配置: 银行转账标准
✅ 创建提现配置: 银行转账大额

🏛️ 创建银行配置...
✅ 创建银行配置: State Bank of India
✅ 创建银行配置: HDFC Bank
✅ 创建银行配置: ICICI Bank
✅ 创建银行配置: Axis Bank
✅ 创建银行配置: Punjab National Bank

📊 MasterPay88支付系统配置完成统计:
  • MasterPay88网关: 2 个
  • 支付配置: 6 个 (激活: 6)
  • 兑换配置: 3 个
  • 提现配置: 4 个 (激活: 4)

🎉 MasterPay88支付系统初始化完成！
```

### 第五步：验证配置

```bash
# 完整验证
mix payment.verify

# 仅验证网关
mix payment.verify --gateway-only

# 包含连通性测试
mix payment.verify --connectivity

# 自动修复问题
mix payment.verify --fix
```

**预期输出：**
```
🔍 开始验证支付系统配置...
🏗️ 验证支付网关配置...
✅ 找到 1 个启用的充值网关
✅ 找到 1 个启用的提现网关
✅ 找到 2 个MasterPay88网关

⚙️ 验证网关配置...
✅ 找到 1 个激活的网关配置
✅ 找到MasterPay88网关配置

💳 验证支付配置...
✅ 找到 6 个激活的支付配置
✅ 找到 2 个UPI支付配置

💱 验证兑换配置...
✅ 找到 3 个激活的兑换配置

🏦 验证提现配置...
✅ 找到 4 个激活的提现配置

🌐 测试网关连通性...
✅ MasterPay88网关连通性正常

📋 验证结果总结:
  ✅ 支付网关
  ✅ 网关配置
  ✅ 支付配置
  ✅ 兑换配置
  ✅ 提现配置
  ✅ 连通性测试

🎉 所有检查项通过！支付系统配置正常。
```

## 🧪 功能测试

### 1. 后台管理测试

访问管理后台验证配置：

```bash
# 启动服务器
mix phx.server
```

访问以下URL：
- 支付网关管理: `http://localhost:4000/admin/payment-gateways`
- 支付配置管理: `http://localhost:4000/admin/payment-configs`
- 兑换配置管理: `http://localhost:4000/admin/exchange-configs`
- 提现配置管理: `http://localhost:4000/admin/withdrawal-configs`

### 2. API接口测试

#### 获取支付方式列表

```bash
curl -X GET "http://localhost:4000/api/payment/methods" \
  -H "Content-Type: application/json"
```

**预期响应：**
```json
{
  "success": true,
  "data": {
    "payment_methods": [
      {
        "type": "upi",
        "name": "UPI支付",
        "icon": "/images/payment/upi.png",
        "min_amount": 100.00,
        "max_amount": 50000.00,
        "fee_rate": 1.8,
        "recommended": true
      },
      // ... 其他支付方式
    ]
  }
}
```

#### 创建支付订单

```bash
curl -X POST "http://localhost:4000/api/payment/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "payment_type": "upi",
    "amount": 10000,
    "currency": "INR",
    "return_url": "https://yourapp.com/success"
  }'
```

**预期响应：**
```json
{
  "success": true,
  "data": {
    "order_id": "CP20240115120001",
    "payment_url": "https://api.masterpay88.in/...",
    "qr_code": "data:image/png;base64,...",
    "expires_at": "2024-01-15T12:30:00Z"
  }
}
```

### 3. 回调测试

模拟MasterPay88回调：

```bash
curl -X POST "http://localhost:4000/api/payment/callback/masterpay88" \
  -H "Content-Type: application/json" \
  -H "X-Forwarded-For: *************" \
  -d '{
    "order_id": "CP20240115120001",
    "status": "success",
    "amount": "100.00",
    "currency": "INR",
    "transaction_id": "TXN123456789",
    "signature": "calculated-signature"
  }'
```

### 4. 数据库验证

```sql
-- 检查网关配置
SELECT name, gateway_type, enabled, priority FROM payment_gateways WHERE name LIKE '%MasterPay88%';

-- 检查支付配置
SELECT payment_type_name, min_amount, max_amount, fee_rate, status FROM payment_configs WHERE status = 1;

-- 检查兑换配置
SELECT config_name, exchange_rate, fee_rate, status FROM exchange_configs WHERE status = 1;

-- 检查提现配置
SELECT config_name, payment_method, min_amount, max_amount, fee_rate FROM withdrawal_configs WHERE status = 1;
```

## 🔧 故障排除

### 常见问题

#### 1. 种子文件运行失败

**错误：** `** (Ash.Error.Invalid) Input Invalid`

**解决：** 检查必填字段和数据格式
```bash
# 查看详细错误
mix run priv/repo/seeds/masterpay88_seeds.exs --verbose
```

#### 2. 网关连通性测试失败

**错误：** `❌ MasterPay88网关连通性测试失败: timeout`

**解决：**
1. 检查网络连接
2. 验证网关URL
3. 检查防火墙设置

```bash
# 手动测试连通性
curl -I https://api.masterpay88.in/app-api
```

#### 3. 环境变量未加载

**错误：** 使用默认配置而非环境变量

**解决：**
```bash
# 检查环境变量
echo $MASTERPAY88_MERCHANT_ID

# 重新加载配置
source .env
mix compile --force
```

#### 4. 权限错误

**错误：** `permission denied` 或 `access denied`

**解决：**
```bash
# 检查数据库权限
mix ecto.gen.migration test_permissions

# 检查文件权限
chmod +x lib/mix/tasks/payment.*.ex
```

### 调试工具

#### 1. 开启详细日志

在 `config/dev.exs` 中：
```elixir
config :logger, level: :debug

config :cypridina, :masterpay88,
  log_requests: true,
  log_responses: true
```

#### 2. 数据库查询调试

```elixir
# 在 iex 中
iex> Teen.PaymentSystem.PaymentGateway.read()
iex> Teen.PaymentSystem.PaymentGatewayConfig.list_active()
```

#### 3. 手动测试函数

```elixir
# 测试支付网关
iex> alias Teen.PaymentSystem.Gateways.Masterpay88Gateway
iex> Masterpay88Gateway.test_connection()
```

## 📊 监控和维护

### 1. 定期验证

设置定时任务：
```bash
# 添加到 crontab
0 9 * * * cd /app/cypridina && mix payment.verify --connectivity
```

### 2. 性能监控

监控关键指标：
- 支付成功率
- 平均响应时间
- 错误率
- 网关可用性

### 3. 安全检查

定期检查：
- 商户密钥是否泄露
- IP白名单是否更新
- SSL证书是否有效
- 签名验证是否正常

## 🔒 安全建议

1. **环境变量管理**
   - 使用 `.env` 文件管理敏感信息
   - 生产环境使用系统环境变量
   - 定期轮换密钥

2. **网络安全**
   - 配置正确的IP白名单
   - 使用HTTPS进行所有通信
   - 实施适当的防火墙规则

3. **数据保护**
   - 加密敏感数据
   - 定期备份配置
   - 实施访问控制

4. **审计日志**
   - 记录所有配置变更
   - 监控异常访问
   - 保留足够的日志历史

## 📞 支持联系

如遇到问题，请：

1. 检查日志文件: `log/dev.log` 或 `log/prod.log`
2. 运行诊断命令: `mix payment.verify --verbose`
3. 查看错误追踪: `http://localhost:4000/error_tracker`
4. 联系技术支持并提供相关日志