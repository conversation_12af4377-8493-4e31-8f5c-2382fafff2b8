<div class="batch-clear-page">
  <div class="page-header">
    <h1>批量数据清除</h1>
    <.link navigate={~p"/admin/test-data"} class="btn btn-outline">返回首页</.link>
  </div>

  <div class="main-content">
    <!-- 用户ID列表输入 -->
    <div class="section">
      <h3>1. 输入用户ID列表</h3>
      <div class="form-group">
        <label for="user_ids">用户ID列表（每行一个）</label>
        <textarea 
          id="user_ids" 
          placeholder="输入用户ID，每行一个：&#10;user_1&#10;user_2&#10;user_3"
          class="form-control"
          rows="10"></textarea>
        <small class="help-text">支持用户ID或昵称，每行一个，最多100个用户</small>
      </div>
      
      <button type="button" onclick="validateUsers()" class="btn btn-info">
        验证用户列表
      </button>
      
      <div id="user_validation" class="validation-result" style="display: none;">
        <h4>用户验证结果</h4>
        <div class="validation-stats">
          <div class="stat-item">
            <span class="stat-label">有效用户:</span>
            <span class="stat-value" id="valid_users_count">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">无效用户:</span>
            <span class="stat-value" id="invalid_users_count">0</span>
          </div>
        </div>
        <div id="invalid_users_list" class="invalid-users" style="display: none;">
          <h5>无效用户列表:</h5>
          <ul id="invalid_users"></ul>
        </div>
      </div>
    </div>

    <!-- 活动类型选择 -->
    <div class="section">
      <h3>2. 选择活动类型</h3>
      <div class="activity-types">
        <label class="checkbox-group">
          <input type="checkbox" value="" checked onchange="toggleAllActivities(this)">
          <span class="checkmark"></span>
          全部活动类型
        </label>
        
        <%= for {name, value} <- @activity_types do %>
          <label class="checkbox-group">
            <input type="checkbox" name="activity_types" value={value} class="activity-checkbox">
            <span class="checkmark"></span>
            <%= name %>
          </label>
        <% end %>
      </div>
    </div>

    <!-- 批量预览 -->
    <div class="section">
      <h3>3. 批量预览</h3>
      <button type="button" onclick="batchPreview()" class="btn btn-primary" id="preview_btn" disabled>
        预览批量清除数据
      </button>
      
      <div id="batch_preview_result" class="preview-result" style="display: none;">
        <h4>批量预览结果</h4>
        <div class="preview-summary">
          <div class="summary-item">
            <span class="summary-label">总用户数:</span>
            <span class="summary-value" id="total_users">0</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">预计删除记录:</span>
            <span class="summary-value" id="total_records_to_delete">0</span>
          </div>
        </div>
        
        <div class="user-preview-list">
          <h5>用户详细预览</h5>
          <div id="users_preview_table"></div>
        </div>
      </div>
    </div>

    <!-- 确认执行 -->
    <div class="section">
      <h3>4. 确认执行</h3>
      <div class="warning-box danger">
        <div class="warning-icon">⚠️</div>
        <div class="warning-content">
          <p><strong>危险操作：批量数据清除！</strong></p>
          <p>此操作将清除多个用户的活动数据，影响范围较大。</p>
          <p>请确认用户列表和活动类型选择正确。</p>
          <p>建议在执行前先备份重要数据。</p>
        </div>
      </div>
      
      <button type="button" onclick="requestBatchConfirmation()" class="btn btn-danger" id="execute_btn" disabled>
        获取批量确认令牌
      </button>
      
      <div id="confirmation_section" style="display: none;">
        <div class="confirmation-info">
          <h4>批量操作确认</h4>
          <div id="confirmation_details"></div>
          <div class="form-group">
            <label for="confirmation_token">确认令牌</label>
            <input type="text" id="confirmation_token" class="form-control" readonly>
            <small>此令牌已自动生成，点击执行按钮完成批量操作</small>
          </div>
          <button type="button" onclick="executeBatchOperation()" class="btn btn-danger">
            确认执行批量清除操作
          </button>
        </div>
      </div>
    </div>

    <!-- 操作结果 -->
    <div id="result_section" class="section" style="display: none;">
      <h3>批量操作结果</h3>
      <div id="operation_result" class="result-box"></div>
    </div>
  </div>
</div>

<style>
.batch-clear-page {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 20px;
}

.section {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-family: monospace;
}

.help-text {
  color: #666;
  font-size: 12px;
  margin-top: 5px;
}

.validation-result {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.validation-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: white;
  border-radius: 4px;
}

.invalid-users {
  background: #fff3cd;
  border: 1px solid #ffeeba;
  border-radius: 4px;
  padding: 10px;
}

.invalid-users ul {
  margin: 5px 0;
  padding-left: 20px;
}

.activity-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.checkbox-group:hover {
  background-color: #f8f9fa;
}

.checkbox-group input[type="checkbox"] {
  margin-right: 8px;
}

.preview-result {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.preview-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background: white;
  border-radius: 4px;
  font-weight: bold;
}

.user-preview-list {
  background: white;
  border-radius: 4px;
  padding: 15px;
}

.warning-box.danger {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
}

.warning-box {
  display: flex;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.warning-icon {
  font-size: 24px;
  margin-right: 15px;
}

.warning-content p {
  margin: 5px 0;
}

.confirmation-info {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 15px;
  margin-top: 15px;
}

.result-box {
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.result-box.success {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.result-box.error {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  font-weight: 500;
  margin-right: 10px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-outline {
  background: white;
  border: 1px solid #6c757d;
  color: #6c757d;
}
</style>

<script>
let validUsers = [];
let previewData = null;
let confirmationToken = null;

function validateUsers() {
  const textarea = document.getElementById('user_ids').value.trim();
  if (!textarea) {
    alert('请输入用户ID列表');
    return;
  }
  
  const userIds = textarea.split('\n').map(id => id.trim()).filter(id => id);
  
  if (userIds.length === 0) {
    alert('请输入有效的用户ID');
    return;
  }
  
  if (userIds.length > 100) {
    alert('用户数量不能超过100个');
    return;
  }
  
  // 模拟用户验证
  // 在实际实现中，这里应该调用API验证用户
  setTimeout(() => {
    const invalidUsers = userIds.filter(id => Math.random() < 0.1); // 10%概率无效
    validUsers = userIds.filter(id => !invalidUsers.includes(id));
    
    displayValidationResult(validUsers, invalidUsers);
    
    if (validUsers.length > 0) {
      document.getElementById('preview_btn').disabled = false;
    }
  }, 1000);
}

function displayValidationResult(valid, invalid) {
  document.getElementById('valid_users_count').textContent = valid.length;
  document.getElementById('invalid_users_count').textContent = invalid.length;
  
  if (invalid.length > 0) {
    const invalidList = document.getElementById('invalid_users');
    invalidList.innerHTML = invalid.map(id => `<li>${id}</li>`).join('');
    document.getElementById('invalid_users_list').style.display = 'block';
  } else {
    document.getElementById('invalid_users_list').style.display = 'none';
  }
  
  document.getElementById('user_validation').style.display = 'block';
}

function toggleAllActivities(checkbox) {
  const activityCheckboxes = document.querySelectorAll('.activity-checkbox');
  activityCheckboxes.forEach(cb => {
    cb.checked = checkbox.checked;
  });
}

function getSelectedActivityTypes() {
  const checkboxes = document.querySelectorAll('.activity-checkbox:checked');
  return Array.from(checkboxes).map(cb => cb.value);
}

function batchPreview() {
  if (validUsers.length === 0) {
    alert('请先验证用户列表');
    return;
  }
  
  const activityTypes = getSelectedActivityTypes();
  
  // 调用批量预览API
  fetch('/admin/test-data/preview-clear', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
      user_ids: validUsers,
      activity_types: activityTypes
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      previewData = data.data;
      displayBatchPreviewResult(data.data);
      document.getElementById('execute_btn').disabled = false;
    } else {
      alert('批量预览失败: ' + data.error);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('请求失败');
  });
}

function displayBatchPreviewResult(data) {
  document.getElementById('total_users').textContent = validUsers.length;
  
  // 计算总记录数
  const totalRecords = data.reduce((sum, userData) => {
    return sum + (userData.total_records || 0);
  }, 0);
  
  document.getElementById('total_records_to_delete').textContent = totalRecords;
  
  // 创建用户预览表格
  const tableHtml = `
    <table class="preview-table">
      <thead>
        <tr>
          <th>用户ID</th>
          <th>参与记录</th>
          <th>活动记录</th>
          <th>奖励记录</th>
          <th>总计</th>
        </tr>
      </thead>
      <tbody>
        ${data.map(user => `
          <tr>
            <td>${user.user_id}</td>
            <td>${user.data_counts.participations}</td>
            <td>${user.data_counts.records}</td>
            <td>${user.data_counts.rewards}</td>
            <td><strong>${user.total_records}</strong></td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;
  
  document.getElementById('users_preview_table').innerHTML = tableHtml;
  document.getElementById('batch_preview_result').style.display = 'block';
}

function requestBatchConfirmation() {
  if (!previewData) {
    alert('请先进行批量预览');
    return;
  }
  
  const activityTypes = getSelectedActivityTypes();
  
  fetch('/admin/test-data/get-confirmation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
      operation_type: 'batch_clear_user_data',
      params: {
        user_ids: validUsers,
        activity_types: activityTypes
      }
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      confirmationToken = data.data.confirmation_token;
      displayBatchConfirmationInfo(data.data);
    } else {
      alert('获取确认令牌失败: ' + data.error);
    }
  });
}

function displayBatchConfirmationInfo(data) {
  const totalRecords = previewData.reduce((sum, user) => sum + user.total_records, 0);
  
  const details = `
    <div class="confirmation-details">
      <p><strong>操作类型:</strong> 批量清除用户数据</p>
      <p><strong>影响用户数:</strong> ${validUsers.length} 个</p>
      <p><strong>预计删除记录:</strong> ${totalRecords} 条</p>
      <div class="warnings">
        <h5>注意事项:</h5>
        <ul>
          ${data.warnings.map(warning => `<li>${warning}</li>`).join('')}
        </ul>
      </div>
    </div>
  `;
  
  document.getElementById('confirmation_details').innerHTML = details;
  document.getElementById('confirmation_token').value = confirmationToken;
  document.getElementById('confirmation_section').style.display = 'block';
}

function executeBatchOperation() {
  if (!confirmationToken) {
    alert('确认令牌无效');
    return;
  }
  
  const activityTypes = getSelectedActivityTypes();
  
  fetch('/admin/test-data/execute-batch-clear', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
      user_ids: validUsers.join('\n'),
      activity_types: activityTypes,
      confirmation_token: confirmationToken
    })
  })
  .then(response => response.json())
  .then(data => {
    displayBatchOperationResult(data);
  })
  .catch(error => {
    console.error('Error:', error);
    displayBatchOperationResult({success: false, error: '请求失败'});
  });
}

function displayBatchOperationResult(data) {
  const resultBox = document.getElementById('operation_result');
  
  if (data.success) {
    resultBox.className = 'result-box success';
    
    const successCount = data.data.filter(([_, status]) => status === 'ok').length;
    const errorCount = data.data.filter(([_, status]) => status === 'error').length;
    
    resultBox.innerHTML = `
      <h4>批量操作完成</h4>
      <p>成功处理：${successCount} 个用户</p>
      <p>失败处理：${errorCount} 个用户</p>
      
      <details>
        <summary>详细结果</summary>
        <ul>
          ${data.data.map(([userId, status, result]) => `
            <li>
              <strong>${userId}:</strong> 
              ${status === 'ok' ? 
                `成功 - 删除 ${result.total_deleted} 条记录` : 
                `失败 - ${result}`
              }
            </li>
          `).join('')}
        </ul>
      </details>
    `;
  } else {
    resultBox.className = 'result-box error';
    resultBox.innerHTML = `
      <h4>批量操作失败</h4>
      <p>错误信息：${data.error}</p>
    `;
  }
  
  document.getElementById('result_section').style.display = 'block';
}
</script>

<style>
.preview-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.preview-table th,
.preview-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.preview-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.preview-table tr:hover {
  background-color: #f8f9fa;
}
</style>