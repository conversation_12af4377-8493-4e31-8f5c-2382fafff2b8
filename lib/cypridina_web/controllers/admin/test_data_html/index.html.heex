<div class="test-data-manager">
  <div class="header">
    <h1 class="title">测试数据管理中心</h1>
    <div class="environment-warning">
      <span class="warning-icon">⚠️</span>
      <span>当前运行在测试环境，请谨慎操作</span>
    </div>
  </div>

  <div class="management-grid">
    <!-- 用户数据清除 -->
    <div class="card">
      <div class="card-header">
        <h3>用户数据清除</h3>
        <span class="risk-level low">低风险</span>
      </div>
      <div class="card-content">
        <p>清除指定用户的活动参与数据，包括进度、奖励记录等</p>
        <ul class="feature-list">
          <li>支持选择特定活动类型</li>
          <li>提供数据预览功能</li>
          <li>需要二次确认</li>
        </ul>
      </div>
      <div class="card-actions">
        <.link navigate={~p"/admin/test-data/user-clear"} class="btn btn-primary">
          开始操作
        </.link>
      </div>
    </div>

    <!-- 批量数据清除 -->
    <div class="card">
      <div class="card-header">
        <h3>批量数据清除</h3>
        <span class="risk-level medium">中风险</span>
      </div>
      <div class="card-content">
        <p>批量清除多个用户的活动数据，适用于测试用户组</p>
        <ul class="feature-list">
          <li>支持用户ID列表输入</li>
          <li>可选择活动类型范围</li>
          <li>显示操作结果统计</li>
        </ul>
      </div>
      <div class="card-actions">
        <.link navigate={~p"/admin/test-data/batch-clear"} class="btn btn-warning">
          批量操作
        </.link>
      </div>
    </div>

    <!-- 活动类型清除 -->
    <div class="card">
      <div class="card-header">
        <h3>活动类型清除</h3>
        <span class="risk-level high">高风险</span>
      </div>
      <div class="card-content">
        <p>清除特定活动类型的所有用户数据，影响范围较大</p>
        <ul class="feature-list">
          <li>按活动类型全量清除</li>
          <li>影响所有参与用户</li>
          <li>需要最高权限确认</li>
        </ul>
      </div>
      <div class="card-actions">
        <.link navigate={~p"/admin/test-data/activity-clear"} class="btn btn-danger">
          活动清除
        </.link>
      </div>
    </div>

    <!-- 时间模拟 -->
    <div class="card">
      <div class="card-header">
        <h3>时间模拟</h3>
        <span class="risk-level low">低风险</span>
      </div>
      <div class="card-content">
        <p>模拟时间推进，测试基于时间的活动逻辑</p>
        <ul class="feature-list">
          <li>指定用户时间推进</li>
          <li>模拟日期变化</li>
          <li>测试定时重置逻辑</li>
        </ul>
      </div>
      <div class="card-actions">
        <.link navigate={~p"/admin/test-data/time-simulation"} class="btn btn-info">
          时间模拟
        </.link>
      </div>
    </div>
  </div>

  <!-- 快速操作区域 -->
  <div class="quick-actions">
    <h3>快速操作</h3>
    <div class="quick-buttons">
      <button class="btn btn-outline" onclick="showPreviewModal()">
        数据预览
      </button>
      <button class="btn btn-outline" onclick="showOperationLog()">
        操作日志
      </button>
      <button class="btn btn-outline" onclick="exportOperationReport()">
        导出报告
      </button>
    </div>
  </div>

  <!-- 安全提示 -->
  <div class="security-notice">
    <h4>安全提示</h4>
    <ul>
      <li>所有操作都会记录详细日志，包括操作者、时间、影响范围</li>
      <li>危险操作需要二次确认，请仔细阅读确认信息</li>
      <li>建议在执行清除前使用预览功能确认影响范围</li>
      <li>测试数据管理功能仅在开发和测试环境中可用</li>
      <li>如有疑问，请联系开发团队确认操作安全性</li>
    </ul>
  </div>
</div>

<style>
.test-data-manager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 2rem;
  color: #333;
  margin-bottom: 10px;
}

.environment-warning {
  background: #fff3cd;
  border: 1px solid #ffeeba;
  border-radius: 4px;
  padding: 10px;
  color: #856404;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.card {
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
  background: #f8f9fa;
  padding: 15px;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.risk-level {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.risk-level.low {
  background: #d4edda;
  color: #155724;
}

.risk-level.medium {
  background: #fff3cd;
  color: #856404;
}

.risk-level.high {
  background: #f8d7da;
  color: #721c24;
}

.card-content {
  padding: 15px;
}

.feature-list {
  margin: 10px 0;
  padding-left: 20px;
}

.card-actions {
  padding: 15px;
  border-top: 1px solid #f0f0f0;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  display: inline-block;
  font-weight: 500;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-outline {
  background: white;
  border: 1px solid #6c757d;
  color: #6c757d;
}

.quick-actions {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.quick-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.security-notice {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 20px;
}

.security-notice h4 {
  color: #0066cc;
  margin-top: 0;
}

.security-notice ul {
  margin: 10px 0;
  padding-left: 20px;
}

.security-notice li {
  margin: 5px 0;
  color: #0066cc;
}
</style>

<script>
function showPreviewModal() {
  alert('数据预览功能 - 需要在具体操作页面中使用');
}

function showOperationLog() {
  // 这里可以实现操作日志查看功能
  window.open('/admin/operation-logs?type=test_data_management', '_blank');
}

function exportOperationReport() {
  alert('导出功能 - 可以导出操作日志报告');
}
</script>