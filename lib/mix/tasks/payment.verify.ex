defmodule Mix.Tasks.Payment.Verify do
  @moduledoc """
  验证支付系统配置完整性和正确性

  使用方法:
    mix payment.verify                    # 完整验证
    mix payment.verify --gateway-only     # 仅验证网关配置
    mix payment.verify --connectivity     # 测试网关连通性
    mix payment.verify --fix              # 自动修复发现的问题
  """

  use Mix.Task
  require Logger

  alias Teen.PaymentSystem.{
    PaymentGateway,
    PaymentGatewayConfig,
    PaymentConfig,
    ExchangeConfig,
    WithdrawalConfig,
    BankConfig
  }

  @shortdoc "验证支付系统配置"

  def run(args) do
    Mix.Task.run("app.start")

    {opts, _args, _} = OptionParser.parse(args,
      switches: [
        gateway_only: :boolean,
        connectivity: :boolean,
        fix: :boolean,
        verbose: :boolean
      ],
      aliases: [
        g: :gateway_only,
        c: :connectivity,
        f: :fix,
        v: :verbose
      ]
    )

    Mix.shell().info("🔍 开始验证支付系统配置...")
    
    results = %{
      gateway_check: false,
      config_check: false,
      payment_check: false,
      exchange_check: false,
      withdrawal_check: false,
      connectivity_check: false,
      errors: [],
      warnings: [],
      fixes_applied: []
    }

    results = 
      results
      |> verify_gateways(opts)
      |> verify_gateway_configs(opts)
      |> then(fn res -> 
        if opts[:gateway_only], do: res, else: verify_payment_configs(res, opts)
      end)
      |> then(fn res -> 
        if opts[:gateway_only], do: res, else: verify_exchange_configs(res, opts)
      end)
      |> then(fn res -> 
        if opts[:gateway_only], do: res, else: verify_withdrawal_configs(res, opts)
      end)
      |> then(fn res -> 
        if opts[:connectivity], do: verify_connectivity(res, opts), else: res
      end)

    print_summary(results, opts)
  end

  # ==================== 验证支付网关 ====================

  defp verify_gateways(results, opts) do
    Mix.shell().info("🏗️ 验证支付网关配置...")

    case PaymentGateway.read() do
      {:ok, gateways} ->
        errors = []
        warnings = []
        fixes = []

        # 检查是否有启用的充值网关
        recharge_gateways = Enum.filter(gateways, &(&1.enabled && &1.gateway_type == "recharge"))
        {errors, warnings, fixes} = if Enum.empty?(recharge_gateways) do
          error = "❌ 没有启用的充值网关"
          {[error | errors], warnings, fixes}
        else
          Mix.shell().info("✅ 找到 #{length(recharge_gateways)} 个启用的充值网关")
          {errors, warnings, fixes}
        end

        # 检查是否有启用的提现网关
        withdrawal_gateways = Enum.filter(gateways, &(&1.enabled && &1.gateway_type == "withdraw"))
        {errors, warnings, fixes} = if Enum.empty?(withdrawal_gateways) do
          warning = "⚠️ 没有启用的提现网关"
          {errors, [warning | warnings], fixes}
        else
          Mix.shell().info("✅ 找到 #{length(withdrawal_gateways)} 个启用的提现网关")
          {errors, warnings, fixes}
        end

        # 检查MasterPay88网关
        masterpay_gateways = Enum.filter(gateways, &String.contains?(&1.name, "MasterPay88"))
        {errors, warnings, fixes} = if Enum.empty?(masterpay_gateways) do
          error = "❌ 未找到MasterPay88网关"
          {[error | errors], warnings, fixes}
        else
          Mix.shell().info("✅ 找到 #{length(masterpay_gateways)} 个MasterPay88网关")
          
          # 检查网关配置完整性
          Enum.reduce(masterpay_gateways, {errors, warnings, fixes}, fn gateway, {e, w, f} ->
            check_gateway_integrity(gateway, opts) |> merge_results({e, w, f})
          end)
        end

        %{results |
          gateway_check: Enum.empty?(errors),
          errors: results.errors ++ errors,
          warnings: results.warnings ++ warnings,
          fixes_applied: results.fixes_applied ++ fixes
        }

      {:error, reason} ->
        error = "❌ 无法读取支付网关: #{inspect(reason)}"
        %{results | errors: [error | results.errors]}
    end
  end

  defp check_gateway_integrity(gateway, opts) do
    errors = []
    warnings = []
    fixes = []

    # 检查必要字段
    {errors, warnings, fixes} = 
      [:name, :gateway_type, :gateway_url, :channel_id]
      |> Enum.reduce({errors, warnings, fixes}, fn field, {e, w, f} ->
        value = Map.get(gateway, field)
        if is_nil(value) || value == "" do
          error = "❌ 网关 #{gateway.name} 缺少字段: #{field}"
          {[error | e], w, f}
        else
          {e, w, f}
        end
      end)

    # 检查金额限制
    {errors, warnings, fixes} = if gateway.min_amount && gateway.max_amount do
      if Decimal.compare(gateway.min_amount, gateway.max_amount) == :gt do
        error = "❌ 网关 #{gateway.name} 最小金额大于最大金额"
        {[error | errors], warnings, fixes}
      else
        {errors, warnings, fixes}
      end
    else
      warning = "⚠️ 网关 #{gateway.name} 未设置金额限制"
      {errors, [warning | warnings], fixes}
    end

    # 检查URL格式
    {errors, warnings, fixes} = if gateway.gateway_url do
      if String.starts_with?(gateway.gateway_url, ["http://", "https://"]) do
        {errors, warnings, fixes}
      else
        error = "❌ 网关 #{gateway.name} URL格式无效: #{gateway.gateway_url}"
        {[error | errors], warnings, fixes}
      end
    else
      {errors, warnings, fixes}
    end

    if opts[:verbose] && Enum.empty?(errors) do
      Mix.shell().info("  ✅ 网关 #{gateway.name} 配置检查通过")
    end

    {errors, warnings, fixes}
  end

  # ==================== 验证网关配置 ====================

  defp verify_gateway_configs(results, opts) do
    Mix.shell().info("⚙️ 验证网关配置...")

    case PaymentGatewayConfig.read() do
      {:ok, configs} ->
        errors = []
        warnings = []
        fixes = []

        # 检查是否有激活的配置
        active_configs = Enum.filter(configs, &(&1.status == "active"))
        {errors, warnings, fixes} = if Enum.empty?(active_configs) do
          error = "❌ 没有激活的网关配置"
          {[error | errors], warnings, fixes}
        else
          Mix.shell().info("✅ 找到 #{length(active_configs)} 个激活的网关配置")
          {errors, warnings, fixes}
        end

        # 检查MasterPay88配置
        mp88_config = Enum.find(configs, &(&1.gateway_code == "MP88"))
        {errors, warnings, fixes} = if is_nil(mp88_config) do
          error = "❌ 未找到MasterPay88网关配置"
          {[error | errors], warnings, fixes}
        else
          Mix.shell().info("✅ 找到MasterPay88网关配置")
          check_masterpay_config(mp88_config, opts) |> merge_results({errors, warnings, fixes})
        end

        %{results |
          config_check: Enum.empty?(errors),
          errors: results.errors ++ errors,
          warnings: results.warnings ++ warnings,
          fixes_applied: results.fixes_applied ++ fixes
        }

      {:error, reason} ->
        error = "❌ 无法读取网关配置: #{inspect(reason)}"
        %{results | errors: [error | results.errors]}
    end
  end

  defp check_masterpay_config(config, opts) do
    errors = []
    warnings = []
    fixes = []

    # 检查关键字段
    required_fields = [:merchant_id, :merchant_key, :gateway_url, :callback_ip]
    {errors, warnings, fixes} = 
      Enum.reduce(required_fields, {errors, warnings, fixes}, fn field, {e, w, f} ->
        value = Map.get(config, field)
        if is_nil(value) || value == "" do
          error = "❌ MasterPay88配置缺少字段: #{field}"
          {[error | e], w, f}
        else
          {e, w, f}
        end
      end)

    # 检查商户ID格式
    {errors, warnings, fixes} = if config.merchant_id do
      if String.match?(config.merchant_id, ~r/^\d+$/) do
        {errors, warnings, fixes}
      else
        warning = "⚠️ 商户ID格式可能不正确: #{config.merchant_id}"
        {errors, [warning | warnings], fixes}
      end
    else
      {errors, warnings, fixes}
    end

    # 检查回调配置
    {errors, warnings, fixes} = if config.config_data do
      notify_url = get_in(config.config_data, ["notify_url"])
      if notify_url && String.starts_with?(notify_url, ["http://", "https://"]) do
        {errors, warnings, fixes}
      else
        warning = "⚠️ 回调URL配置可能不正确"
        {errors, [warning | warnings], fixes}
      end
    else
      warning = "⚠️ 缺少扩展配置数据"
      {errors, [warning | warnings], fixes}
    end

    # 自动修复
    {errors, warnings, fixes} = if opts[:fix] && config.status != "active" do
      case PaymentGatewayConfig.update(config, %{status: "active"}) do
        {:ok, _} ->
          fix = "🔧 自动激活MasterPay88配置"
          {errors, warnings, [fix | fixes]}
        {:error, reason} ->
          error = "❌ 自动激活失败: #{inspect(reason)}"
          {[error | errors], warnings, fixes}
      end
    else
      {errors, warnings, fixes}
    end

    if opts[:verbose] && Enum.empty?(errors) do
      Mix.shell().info("  ✅ MasterPay88配置检查通过")
    end

    {errors, warnings, fixes}
  end

  # ==================== 验证支付配置 ====================

  defp verify_payment_configs(results, opts) do
    Mix.shell().info("💳 验证支付配置...")

    case PaymentConfig.read() do
      {:ok, configs} ->
        errors = []
        warnings = []
        fixes = []

        # 检查是否有激活的支付配置
        active_configs = Enum.filter(configs, &(&1.status == 1))
        {errors, warnings, fixes} = if Enum.empty?(active_configs) do
          error = "❌ 没有激活的支付配置"
          {[error | errors], warnings, fixes}
        else
          Mix.shell().info("✅ 找到 #{length(active_configs)} 个激活的支付配置")
          {errors, warnings, fixes}
        end

        # 检查UPI配置
        upi_configs = Enum.filter(active_configs, &String.contains?(&1.payment_type, "upi"))
        {errors, warnings, fixes} = if Enum.empty?(upi_configs) do
          warning = "⚠️ 没有UPI支付配置"
          {errors, [warning | warnings], fixes}
        else
          Mix.shell().info("✅ 找到 #{length(upi_configs)} 个UPI支付配置")
          {errors, warnings, fixes}
        end

        # 检查费率合理性
        {errors, warnings, fixes} = 
          Enum.reduce(active_configs, {errors, warnings, fixes}, fn config, {e, w, f} ->
            check_payment_config_integrity(config, opts) |> merge_results({e, w, f})
          end)

        %{results |
          payment_check: Enum.empty?(errors),
          errors: results.errors ++ errors,
          warnings: results.warnings ++ warnings,
          fixes_applied: results.fixes_applied ++ fixes
        }

      {:error, reason} ->
        error = "❌ 无法读取支付配置: #{inspect(reason)}"
        %{results | errors: [error | results.errors]}
    end
  end

  defp check_payment_config_integrity(config, _opts) do
    errors = []
    warnings = []
    fixes = []

    # 检查费率
    {errors, warnings, fixes} = if config.fee_rate do
      fee_rate_float = Decimal.to_float(config.fee_rate)
      cond do
        fee_rate_float < 0 ->
          error = "❌ #{config.payment_type_name} 费率不能为负数"
          {[error | errors], warnings, fixes}
        fee_rate_float > 10 ->
          warning = "⚠️ #{config.payment_type_name} 费率过高: #{fee_rate_float}%"
          {errors, [warning | warnings], fixes}
        true ->
          {errors, warnings, fixes}
      end
    else
      {errors, warnings, fixes}
    end

    # 检查金额限制
    {errors, warnings, fixes} = if config.min_amount && config.max_amount do
      if Decimal.compare(config.min_amount, config.max_amount) == :gt do
        error = "❌ #{config.payment_type_name} 最小金额大于最大金额"
        {[error | errors], warnings, fixes}
      else
        {errors, warnings, fixes}
      end
    else
      {errors, warnings, fixes}
    end

    {errors, warnings, fixes}
  end

  # ==================== 验证兑换配置 ====================

  defp verify_exchange_configs(results, _opts) do
    Mix.shell().info("💱 验证兑换配置...")

    case ExchangeConfig.read() do
      {:ok, configs} ->
        active_configs = Enum.filter(configs, &(&1.status == 1))
        
        if Enum.empty?(active_configs) do
          error = "❌ 没有激活的兑换配置"
          %{results | errors: [error | results.errors]}
        else
          Mix.shell().info("✅ 找到 #{length(active_configs)} 个激活的兑换配置")
          %{results | exchange_check: true}
        end

      {:error, reason} ->
        error = "❌ 无法读取兑换配置: #{inspect(reason)}"
        %{results | errors: [error | results.errors]}
    end
  end

  # ==================== 验证提现配置 ====================

  defp verify_withdrawal_configs(results, _opts) do
    Mix.shell().info("🏦 验证提现配置...")

    case WithdrawalConfig.read() do
      {:ok, configs} ->
        active_configs = Enum.filter(configs, &(&1.status == 1))
        
        if Enum.empty?(active_configs) do
          warning = "⚠️ 没有激活的提现配置"
          %{results | warnings: [warning | results.warnings]}
        else
          Mix.shell().info("✅ 找到 #{length(active_configs)} 个激活的提现配置")
          %{results | withdrawal_check: true}
        end

      {:error, reason} ->
        error = "❌ 无法读取提现配置: #{inspect(reason)}"
        %{results | errors: [error | results.errors]}
    end
  end

  # ==================== 验证连通性 ====================

  defp verify_connectivity(results, _opts) do
    Mix.shell().info("🌐 测试网关连通性...")

    case PaymentGatewayConfig.get_by_code(%{gateway_code: "MP88"}) do
      {:ok, config} ->
        case test_masterpay_connectivity(config) do
          :ok ->
            Mix.shell().info("✅ MasterPay88网关连通性正常")
            %{results | connectivity_check: true}
          {:error, reason} ->
            error = "❌ MasterPay88网关连通性测试失败: #{reason}"
            %{results | errors: [error | results.errors]}
        end

      {:error, _} ->
        error = "❌ 无法找到MasterPay88配置进行连通性测试"
        %{results | errors: [error | results.errors]}
    end
  end

  defp test_masterpay_connectivity(config) do
    test_url = "#{config.gateway_url}/v1.0/api/ping"
    
    try do
      case :httpc.request(:get, {String.to_charlist(test_url), []}, [{:timeout, 5000}], []) do
        {:ok, {{_, status_code, _}, _headers, _body}} when status_code in 200..299 ->
          :ok
        {:ok, {{_, 404, _}, _headers, _body}} ->
          # 网关可能不支持ping接口，但网络是通的
          :ok
        {:ok, {{_, status_code, _}, _headers, _body}} ->
          {:error, "HTTP #{status_code}"}
        {:error, reason} ->
          {:error, inspect(reason)}
      end
    rescue
      _ ->
        {:error, "连接超时或网络错误"}
    end
  end

  # ==================== 辅助函数 ====================

  defp merge_results({errors1, warnings1, fixes1}, {errors2, warnings2, fixes2}) do
    {errors1 ++ errors2, warnings1 ++ warnings2, fixes1 ++ fixes2}
  end

  defp print_summary(results, opts) do
    Mix.shell().info("")
    Mix.shell().info("📋 验证结果总结:")
    Mix.shell().info("")

    # 打印检查项状态
    checks = [
      {"支付网关", results.gateway_check},
      {"网关配置", results.config_check},
      {"支付配置", results.payment_check},
      {"兑换配置", results.exchange_check},
      {"提现配置", results.withdrawal_check}
    ]

    checks = if opts[:connectivity] do
      checks ++ [{"连通性测试", results.connectivity_check}]
    else
      checks
    end

    Enum.each(checks, fn {name, status} ->
      icon = if status, do: "✅", else: "❌"
      Mix.shell().info("  #{icon} #{name}")
    end)

    # 打印错误
    if not Enum.empty?(results.errors) do
      Mix.shell().info("")
      Mix.shell().info("🚨 发现的错误:")
      Enum.each(results.errors, &Mix.shell().info("  #{&1}"))
    end

    # 打印警告
    if not Enum.empty?(results.warnings) do
      Mix.shell().info("")
      Mix.shell().info("⚠️ 警告信息:")
      Enum.each(results.warnings, &Mix.shell().info("  #{&1}"))
    end

    # 打印修复
    if not Enum.empty?(results.fixes_applied) do
      Mix.shell().info("")
      Mix.shell().info("🔧 应用的修复:")
      Enum.each(results.fixes_applied, &Mix.shell().info("  #{&1}"))
    end

    # 总结
    Mix.shell().info("")
    total_checks = length(checks)
    passed_checks = Enum.count(checks, fn {_, status} -> status end)
    
    if passed_checks == total_checks do
      Mix.shell().info("🎉 所有检查项通过！支付系统配置正常。")
    else
      failed_checks = total_checks - passed_checks
      Mix.shell().info("❌ #{failed_checks}/#{total_checks} 检查项失败，请检查配置。")
      
      if not opts[:fix] && not Enum.empty?(results.errors) do
        Mix.shell().info("💡 提示: 使用 --fix 参数尝试自动修复部分问题")
      end
    end
  end
end