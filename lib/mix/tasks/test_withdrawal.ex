defmodule Mix.Tasks.TestWithdrawal do
  @moduledoc """
  测试提现接口的Mix任务
  """
  use Mix.Task

  @shortdoc "测试提现接口"

  def run(_) do
    # 启动应用
    Mix.Task.run("app.start")

    # 模拟用户ID
    user_id = "550e8400-e29b-41d4-a716-************"

    # 准备提现参数
    withdrawal_params = %{
      withdrawal_amount: Decimal.new("1000"),
      payment_method: "bank_card",
      bank_info: Jason.encode!(%{
        bank_name: "Industrial and Commercial Bank of China",
        account_number: "6212260200023557777",
        account_holder: "张三",
        ifsc_code: "ICBC0000123"
      }),
      ip_address: "*************"
    }

    IO.puts("开始测试提现接口...")
    IO.puts("用户ID: #{user_id}")
    IO.puts("提现金额: #{withdrawal_params.withdrawal_amount}")
    IO.puts("支付方式: #{withdrawal_params.payment_method}")

    # 调用提现服务
    try do
      case Teen.Services.WithdrawalService.create_withdrawal(user_id, withdrawal_params) do
        {:ok, withdrawal} ->
          IO.puts("✅ 提现申请创建成功!")
          IO.inspect(withdrawal, pretty: true)
          
        {:error, reason} ->
          IO.puts("❌ 提现申请创建失败:")
          IO.inspect(reason, pretty: true)
      end
    catch
      kind, reason ->
        IO.puts("❌ 调用提现服务时发生异常:")
        IO.puts("Kind: #{kind}")
        IO.inspect(reason, pretty: true)
    end

    IO.puts("\n测试完成!")
  end
end