defmodule Teen.ActivitySystem.ActivityIntegration do
  @moduledoc """
  活动系统集成模块

  提供活动系统的统一入口和集成点：
  - 协议处理集成
  - 事件监听集成
  - 活动生命周期管理
  - 系统初始化
  """

  alias Teen.ActivitySystem.{
    ActivityService,
    ActivityPurchaseService,
    ActivityTaskService,
    ScratchCardService,
    WheelService,
    LossRebateService,
    CdkeyService
  }

  alias Teen.Protocol.HallActivityProtocol
  alias Phoenix.PubSub

  require Logger

  @doc """
  初始化活动系统
  """
  def init() do
    Logger.info("🎯 [ACTIVITY_SYSTEM] 初始化活动系统")

    # 订阅相关事件
    subscribe_events()

    # 加载活动配置
    load_activity_configs()

    # 启动定时任务
    start_scheduled_tasks()

    {:ok, :initialized}
  end

  @doc """
  处理活动相关协议
  """
  def handle_protocol(main_protocol, sub_protocol, data, user_id) do
    case main_protocol do
      101 ->
        # 大厅活动协议
        HallActivityProtocol.handle_protocol(sub_protocol, data, user_id)

      _ ->
        Logger.warning("未知的活动协议: #{main_protocol}")
        {:error, :unknown_protocol}
    end
  end

  @doc """
  获取用户活动面板数据
  """
  def get_user_activity_panel(user_id) do
    try do
      # 获取所有活动配置和用户进度
      activities = ActivityService.get_user_available_activities(user_id)

      {:ok, activities}
    rescue
      e ->
        Logger.error("获取用户活动面板失败: #{inspect(e)}")
        {:error, :fetch_error}
    end
  end

  @doc """
  处理活动商品购买
  """
  def purchase_activity_item(user_id, item_type, item_id, payment_info \\ %{}) do
    case item_type do
      :weekly_card ->
        ActivityPurchaseService.purchase_activity_product(
          user_id,
          :weekly_card,
          item_id,
          payment_info.payment_method || :points
        )

      :first_recharge_gift ->
        ActivityPurchaseService.purchase_activity_product(
          user_id,
          :first_recharge_gift,
          item_id,
          payment_info.payment_method || :cash
        )

      :vip_gift ->
        ActivityPurchaseService.purchase_activity_product(
          user_id,
          :vip_gift,
          item_id,
          payment_info.payment_method || :points
        )

      _ ->
        {:error, :unsupported_item_type}
    end
  end

  @doc """
  领取活动奖励
  """
  def claim_activity_reward(user_id, activity_type, activity_data) do
    case activity_type do
      "scratch_card" ->
        # 30次刮卡活动
        ActivityService.claim_activity_reward(user_id, activity_type, activity_data)

      "game_task" ->
        # 游戏任务
        task_id = Map.get(activity_data, "task_id") || activity_data
        ActivityTaskService.claim_task_reward(user_id, :game_task, task_id)

      "seven_day_task" ->
        # 七日签到
        day_number = Map.get(activity_data, "day") || activity_data
        ActivityTaskService.claim_task_reward(user_id, :seven_day_task, day_number)

      "recharge_wheel" ->
        # 充值转盘
        WheelService.spin_wheel(user_id)

      "weekly_card" ->
        # 周卡每日领取
        card_id = Map.get(activity_data, "card_id") || activity_data
        ActivityPurchaseService.claim_daily_reward(user_id, card_id)

      "loss_rebate" ->
        # 损失返水
        date = Map.get(activity_data, "date")
        LossRebateService.claim_rebate(user_id, date)

      "cdkey" ->
        # 兑换码
        code = Map.get(activity_data, "code") || activity_data
        CdkeyService.redeem_code(user_id, code)

      "vip_daily_bonus" ->
        # VIP每日奖励
        Teen.VipSystem.UserVipInfo.claim_daily_vip_bonus(user_id)

      "vip_gift" ->
        # VIP礼包奖励
        Teen.ActivitySystem.VipGift.process_vip_gift_claim(user_id, activity_data)

      _ ->
        # 通用领取接口
        ActivityService.claim_reward(user_id, String.to_atom(activity_type), activity_data)
    end
  end

  @doc """
  检查活动状态
  """
  def check_activity_status(activity_type, activity_id \\ nil) do
    # TODO: 实现活动状态检查
    {:ok, :active}
  end

  # 私有函数

  defp subscribe_events do
    # 订阅充值事件
    PubSub.subscribe(Cypridina.PubSub, "payment:recharge:completed")

    # 订阅游戏事件
    PubSub.subscribe(Cypridina.PubSub, "game:round:completed")

    # 订阅用户事件
    PubSub.subscribe(Cypridina.PubSub, "user:login")
    PubSub.subscribe(Cypridina.PubSub, "user:level_up")

    Logger.info("🎯 [ACTIVITY_SYSTEM] 事件订阅完成")
  end

  defp load_activity_configs do
    # 加载所有活动配置
    activity_types = [
      :game_task,
      :seven_day_task,
      :weekly_card,
      :vip_gift,
      :recharge_task,
      :recharge_wheel,
      :scratch_card,
      :first_recharge_gift,
      :binding_reward,
      :free_bonus_task,
      :cdkey
    ]

    Enum.each(activity_types, fn type ->
      case ActivityService.get_activity_config(type) do
        {:ok, configs} ->
          Logger.info("🎯 [ACTIVITY_SYSTEM] 加载 #{type} 配置: #{length(configs)} 个")

        {:error, reason} ->
          Logger.warning("🎯 [ACTIVITY_SYSTEM] 加载 #{type} 配置失败: #{inspect(reason)}")
      end
    end)
    
    # 初始化VIP系统
    Teen.VipSystem.VipService.initialize_vip_system()
  end

  defp start_scheduled_tasks do
    # 启动每日重置任务
    schedule_daily_reset()

    # 启动活动过期检查
    schedule_activity_expiry_check()

    Logger.info("🎯 [ACTIVITY_SYSTEM] 定时任务启动完成")
  end

  defp schedule_daily_reset do
    # 计算到下一个凌晨的时间
    now = DateTime.utc_now()
    tomorrow = DateTime.add(now, 86400, :second)
    next_midnight = %{tomorrow | hour: 0, minute: 0, second: 0, microsecond: {0, 0}}

    delay = DateTime.diff(next_midnight, now, :millisecond)

    Process.send_after(self(), :daily_reset, delay)
  end

  defp schedule_activity_expiry_check do
    # 每小时检查一次活动过期
    Process.send_after(self(), :check_activity_expiry, 3600_000)
  end

  @doc """
  处理定时消息
  """
  def handle_info(:daily_reset, state) do
    Logger.info("🎯 [ACTIVITY_SYSTEM] 执行每日重置")

    # 重置每日任务
    reset_daily_tasks()

    # 重置每日活动
    reset_daily_activities()

    # 重新安排下一次重置
    schedule_daily_reset()

    {:noreply, state}
  end

  def handle_info(:check_activity_expiry, state) do
    Logger.info("🎯 [ACTIVITY_SYSTEM] 检查活动过期")

    # 检查并处理过期活动
    check_and_handle_expired_activities()

    # 重新安排下一次检查
    schedule_activity_expiry_check()

    {:noreply, state}
  end

  def handle_info({:pubsub, topic, message}, state) do
    # 处理PubSub消息
    handle_pubsub_message(topic, message)

    {:noreply, state}
  end

  defp reset_daily_tasks do
    Logger.info("🎯 [ACTIVITY_SYSTEM] 开始重置每日任务")

    try do
      # 重置每日游戏任务进度
      reset_daily_game_tasks()

      # 重置每日登录任务
      reset_daily_login_tasks()

      # 重置每日免费奖励任务
      reset_daily_bonus_tasks()

      # 重置刮刮卡每日次数
      reset_daily_scratch_cards()

      # 计算昨日损失返水
      LossRebateService.calculate_daily_rebates()

      Logger.info("🎯 [ACTIVITY_SYSTEM] 每日任务重置完成")
      :ok
    rescue
      e ->
        Logger.error("🎯 [ACTIVITY_SYSTEM] 每日任务重置失败: #{inspect(e)}")
        :error
    end
  end

  defp reset_daily_activities do
    Logger.info("🎯 [ACTIVITY_SYSTEM] 开始重置每日活动")

    try do
      # 重置VIP每日奖励
      reset_vip_daily_bonuses()

      # 重置周卡每日领取
      reset_weekly_card_daily_claims()

      # 重置每日转盘次数
      reset_daily_wheel_spins()

      Logger.info("🎯 [ACTIVITY_SYSTEM] 每日活动重置完成")
      :ok
    rescue
      e ->
        Logger.error("🎯 [ACTIVITY_SYSTEM] 每日活动重置失败: #{inspect(e)}")
        :error
    end
  end

  defp check_and_handle_expired_activities do
    Logger.info("🎯 [ACTIVITY_SYSTEM] 开始检查活动过期状态")

    try do
      now = DateTime.utc_now()

      # 检查过期的周卡
      check_expired_weekly_cards(now)

      # 检查过期的首充礼包
      check_expired_first_recharge_gifts(now)

      # 检查过期的用户活动参与记录
      check_expired_participations(now)

      Logger.info("🎯 [ACTIVITY_SYSTEM] 活动过期检查完成")
      :ok
    rescue
      e ->
        Logger.error("🎯 [ACTIVITY_SYSTEM] 活动过期检查失败: #{inspect(e)}")
        :error
    end
  end

  # 每日任务重置实现

  defp reset_daily_game_tasks do
    # 重置所有用户的游戏任务进度
    case UserActivityParticipation.list_by_activity(%{activity_type: :game_task}) do
      {:ok, participations} ->
        Enum.each(participations, fn participation ->
          if should_reset_daily_task?(participation) do
            UserActivityParticipation.update_progress(participation, %{
              progress: 0,
              status: :active,
              participation_data: Map.put(
                participation.participation_data || %{},
                :last_reset,
                DateTime.utc_now()
              )
            })
          end
        end)

      {:error, reason} ->
        Logger.error("获取游戏任务参与记录失败: #{inspect(reason)}")
    end
  end

  defp reset_daily_login_tasks do
    # 七日签到任务不需要每日重置，而是累进式的
    Logger.debug("七日签到任务无需重置")
  end

  defp reset_daily_bonus_tasks do
    # 重置免费积分任务的每日可领取状态
    case UserActivityParticipation.list_by_activity(%{activity_type: :free_bonus_task}) do
      {:ok, participations} ->
        Enum.each(participations, fn participation ->
          reset_daily_bonus_participation(participation)
        end)

      {:error, reason} ->
        Logger.error("获取免费奖励任务参与记录失败: #{inspect(reason)}")
    end
  end

  defp reset_daily_scratch_cards do
    # 重置刮刮卡每日可刮次数
    case UserActivityParticipation.list_by_activity(%{activity_type: :scratch_card}) do
      {:ok, participations} ->
        Enum.each(participations, fn participation ->
          data = participation.participation_data || %{}
          new_data = Map.put(data, :daily_cards_claimed, 0)

          UserActivityParticipation.update_progress(participation, %{
            participation_data: new_data
          })
        end)

      {:error, reason} ->
        Logger.error("获取刮刮卡参与记录失败: #{inspect(reason)}")
    end
  end

  # 每日活动重置实现

  defp reset_vip_daily_bonuses do
    # VIP每日奖励重置由VIP系统自己处理
    Logger.debug("VIP每日奖励由VIP系统处理")
    
    # 处理VIP礼包相关的日常任务
    Teen.VipSystem.VipService.handle_daily_vip_tasks()
  end

  defp reset_weekly_card_daily_claims do
    # 重置周卡每日领取状态
    case UserActivityParticipation.list_by_activity(%{activity_type: :weekly_card}) do
      {:ok, participations} ->
        Enum.each(participations, fn participation ->
          if participation.status == :active do
            data = participation.participation_data || %{}
            new_data = Map.put(data, :daily_claimed, false)

            UserActivityParticipation.update_progress(participation, %{
              participation_data: new_data
            })
          end
        end)

      {:error, reason} ->
        Logger.error("获取周卡参与记录失败: #{inspect(reason)}")
    end
  end

  defp reset_daily_wheel_spins do
    # 充值转盘每日次数不需要重置，是根据充值动态增加的
    Logger.debug("充值转盘次数无需重置")
  end

  # 过期检查实现

  defp check_expired_weekly_cards(now) do
    case UserActivityParticipation.list_by_activity(%{activity_type: :weekly_card}) do
      {:ok, participations} ->
        Enum.each(participations, fn participation ->
          if participation.status == :active do
            check_weekly_card_expiry(participation, now)
          end
        end)

      {:error, reason} ->
        Logger.error("获取周卡参与记录失败: #{inspect(reason)}")
    end
  end

  defp check_expired_first_recharge_gifts(now) do
    case UserActivityParticipation.list_by_activity(%{activity_type: :first_recharge_gift}) do
      {:ok, participations} ->
        Enum.each(participations, fn participation ->
          if participation.status == :active do
            check_first_recharge_expiry(participation, now)
          end
        end)

      {:error, reason} ->
        Logger.error("获取首充礼包参与记录失败: #{inspect(reason)}")
    end
  end

  defp check_expired_participations(now) do
    # 检查所有活动参与记录是否过期
    case UserActivityParticipation.read() do
      {:ok, participations} ->
        Enum.each(participations, fn participation ->
          if should_check_expiry?(participation) do
            check_participation_expiry(participation, now)
          end
        end)

      {:error, reason} ->
        Logger.error("获取活动参与记录失败: #{inspect(reason)}")
    end
  end

  # 辅助函数

  defp should_reset_daily_task?(participation) do
    # 检查是否需要重置（比如超过24小时）
    last_reset = get_in(participation.participation_data, [:last_reset])

    case last_reset do
      nil -> true
      last_reset_dt when is_binary(last_reset_dt) ->
        case DateTime.from_iso8601(last_reset_dt) do
          {:ok, dt, _} -> DateTime.diff(DateTime.utc_now(), dt) > 86400
          _ -> true
        end
      %DateTime{} = dt ->
        DateTime.diff(DateTime.utc_now(), dt) > 86400
      _ -> true
    end
  end

  defp reset_daily_bonus_participation(participation) do
    data = participation.participation_data || %{}
    new_data = Map.put(data, :daily_reset_at, DateTime.utc_now())

    UserActivityParticipation.update_progress(participation, %{
      participation_data: new_data
    })
  end

  defp check_weekly_card_expiry(participation, now) do
    # 检查周卡是否过期（7天）
    purchase_time = participation.participated_at
    expire_time = DateTime.add(purchase_time, 7 * 24 * 60 * 60, :second)

    if DateTime.compare(now, expire_time) != :lt do
      UserActivityParticipation.update_progress(participation, %{
        status: :expired,
        participation_data: Map.put(
          participation.participation_data || %{},
          :expired_at,
          now
        )
      })

      Logger.info("周卡已过期: 用户=#{participation.user_id}")
    end
  end

  defp check_first_recharge_expiry(participation, now) do
    # 检查首充礼包是否过期（根据配置的天数）
    case get_first_recharge_config(participation.activity_id) do
      {:ok, config} ->
        purchase_time = participation.participated_at
        expire_time = DateTime.add(purchase_time, config.expire_days * 24 * 60 * 60, :second)

        if DateTime.compare(now, expire_time) != :lt do
          UserActivityParticipation.update_progress(participation, %{
            status: :expired,
            participation_data: Map.put(
              participation.participation_data || %{},
              :expired_at,
              now
            )
          })

          Logger.info("首充礼包已过期: 用户=#{participation.user_id}")
        end

      _ ->
        nil
    end
  end

  defp should_check_expiry?(participation) do
    participation.status in [:active, :claimable]
  end

  defp check_participation_expiry(participation, now) do
    # 通用的活动参与过期检查
    case get_activity_expiry_config(participation.activity_type) do
      {:ok, expire_hours} ->
        start_time = participation.participated_at
        expire_time = DateTime.add(start_time, expire_hours * 60 * 60, :second)

        if DateTime.compare(now, expire_time) != :lt do
          UserActivityParticipation.update_progress(participation, %{
            status: :expired,
            participation_data: Map.put(
              participation.participation_data || %{},
              :expired_at,
              now
            )
          })

          Logger.info("活动参与已过期: 用户=#{participation.user_id}, 活动=#{participation.activity_type}")
        end

      _ ->
        nil
    end
  end

  defp get_first_recharge_config(activity_id) do
    case ActivityService.get_activity_config(:first_recharge_gift) do
      {:ok, configs} ->
        config = Enum.find(configs, fn c -> c.id == activity_id end)
        if config, do: {:ok, config}, else: {:error, :not_found}

      _ ->
        {:error, :no_config}
    end
  end

  defp get_activity_expiry_config(activity_type) do
    # 返回各种活动类型的过期时间配置（小时）
    config = %{
      scratch_card: 24 * 7,  # 7天
      binding_reward: 24 * 3,  # 3天
      cdkey_activity: 24 * 30,  # 30天
      invite_cash: 24 * 30  # 30天
    }

    case Map.get(config, activity_type) do
      nil -> {:error, :no_expiry}
      hours -> {:ok, hours}
    end
  end

  defp handle_pubsub_message("payment:recharge:completed", %{user_id: user_id, amount: amount}) do
    # 处理充值完成事件
    ActivityTaskService.update_task_progress(
      user_id,
      :recharge_completed,
      %{amount: amount}
    )
  end

  defp handle_pubsub_message("game:round:completed", game_data) do
    # 游戏回合完成事件已在游戏房间中直接处理
    :ok
  end

  defp handle_pubsub_message("user:login", %{user_id: user_id}) do
    # 处理用户登录事件
    ActivityTaskService.update_task_progress(
      user_id,
      :user_login,
      %{login_time: DateTime.utc_now()}
    )
  end

  defp handle_pubsub_message(_, _) do
    :ok
  end

  @doc """
  获取活动系统状态
  """
  def get_system_status do
    %{
      status: :running,
      active_activities: get_active_activity_count(),
      total_participants: get_total_participant_count(),
      last_reset: get_last_reset_time()
    }
  end

  defp get_active_activity_count do
    # TODO: 实现获取活跃活动数量
    0
  end

  defp get_total_participant_count do
    # TODO: 实现获取总参与人数
    0
  end

  defp get_last_reset_time do
    # TODO: 实现获取最后重置时间
    DateTime.utc_now()
  end
end
