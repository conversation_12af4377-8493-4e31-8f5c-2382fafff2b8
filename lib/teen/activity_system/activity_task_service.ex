defmodule Teen.ActivitySystem.ActivityTaskService do
  @moduledoc """
  活动任务系统服务

  处理各种活动任务的进度追踪和奖励发放：
  - 游戏局数任务
  - 胜利次数任务
  - 充值金额任务
  - 连胜任务
  - 每日任务
  """

  alias Teen.ActivitySystem.{
    GameTask,
    SevenDayTask,
    RechargeTask,
    FreeBonusTask,
    UserActivityParticipation,
    RewardClaimRecord,
    ActivityService,
    UserGameStatistics
  }

  alias Cypridina.Accounts.User
  alias Phoenix.PubSub

  require Logger

  @task_types %{
    game_rounds: :game_task,
    win_rounds: :game_task,
    recharge_amount: :recharge_task,
    consecutive_wins: :game_task,
    daily_login: :seven_day_task,
    free_bonus: :free_bonus_task
  }

  @doc """
  更新任务进度
  """
  def update_task_progress(user_id, event_type, event_data \\ %{}) do
    Logger.info("更新任务进度: 用户=#{user_id}, 事件=#{event_type}, 数据=#{inspect(event_data)}")

    case event_type do
      :game_completed ->
        handle_game_completed(user_id, event_data)

      :game_won ->
        handle_game_won(user_id, event_data)

      :game_lost ->
        handle_game_lost(user_id, event_data)

      :recharge_completed ->
        handle_recharge_completed(user_id, event_data)

      :user_login ->
        handle_user_login(user_id, event_data)

      _ ->
        Logger.warning("未知的任务事件类型: #{event_type}")
        {:ok, :ignored}
    end
  end

  @doc """
  检查并自动完成任务
  """
  def check_and_complete_tasks(user_id) do
    # 获取用户所有活动中的任务
    active_tasks = get_user_active_tasks(user_id)

    Enum.each(active_tasks, fn task ->
      check_task_completion(user_id, task)
    end)

    {:ok, length(active_tasks)}
  end

  @doc """
  获取成就任务列表
  """
  def get_achievement_tasks(user_id) do
    try do
      # 获取所有活动中的成就任务
      case GameTask.list_active_tasks() do
        {:ok, tasks} ->
          # 过滤出成就类型的任务（可以根据实际需求调整过滤条件）
          achievement_tasks = Enum.filter(tasks, fn task -> 
            task.task_type in [:game_rounds, :win_rounds, :consecutive_wins]
          end)
          
          # 为每个任务获取用户参与信息
          tasks_with_progress = 
            achievement_tasks
            |> Enum.map(fn task ->
              case ActivityService.get_user_participation(user_id, :game_task, task.id) do
                {:ok, participation} when not is_nil(participation) ->
                  %{
                    task_id: task.id,
                    task_name: task.task_name || "成就任务",
                    task_type: task.task_type,
                    required_count: task.required_count,
                    reward_amount: Decimal.to_integer(task.reward_amount),
                    progress: participation.progress,
                    status: participation.status,
                    completed: participation.progress >= task.required_count,
                    claimable: participation.status == :claimable
                  }
                _ ->
                  %{
                    task_id: task.id,
                    task_name: task.task_name || "成就任务",
                    task_type: task.task_type,
                    required_count: task.required_count,
                    reward_amount: Decimal.to_integer(task.reward_amount),
                    progress: 0,
                    status: :not_started,
                    completed: false,
                    claimable: false
                  }
              end
            end)
          
          {:ok, tasks_with_progress}
        
        {:error, reason} ->
          Logger.error("获取成就任务失败: #{inspect(reason)}")
          {:error, reason}
      end
    rescue
      e ->
        Logger.error("获取成就任务异常: #{inspect(e)}")
        {:error, :exception}
    end
  end

  @doc """
  领取任务奖励
  """
  def claim_task_reward(user_id, task_type, task_id) do
    with {:ok, task} <- get_task(task_type, task_id),
         {:ok, participation} <-
           ActivityService.get_user_participation(user_id, task_type, task_id),
         :ok <- validate_task_completion(participation, task),
         {:ok, reward} <- calculate_task_reward(task),
         {:ok, _} <- distribute_task_reward(user_id, task, reward),
         {:ok, _} <- mark_task_claimed(participation) do
      Logger.info("任务奖励领取成功: 用户=#{user_id}, 任务=#{task_id}, 奖励=#{reward}")

      {:ok,
       %{
         task_id: task_id,
         reward: reward,
         message: "奖励领取成功"
       }}
    else
      {:error, reason} ->
        Logger.error("任务奖励领取失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  # 私有函数

  defp handle_game_completed(user_id, %{game_id: game_id, room_id: room_id} = data) do
    is_win = Map.get(data, :is_win, false)
    
    # 更新游戏局数相关任务
    update_game_round_tasks(user_id, 1)

    # 更新游戏统计
    UserGameStatistics.update_user_game_stats(user_id, 1, if(is_win, do: 1, else: 0))

    # 处理连胜/连败
    if is_win do
      update_win_streak(user_id, 1)
    else
      reset_win_streak(user_id)
    end

    {:ok, :updated}
  end

  defp handle_game_won(user_id, %{game_id: game_id, win_amount: win_amount} = data) do
    # 更新胜利次数任务
    update_win_tasks(user_id, 1)

    # 更新赢取金额任务 (暂时禁用，因为GameTask不支持:win_amount类型)
    # if win_amount > 0 do
    #   update_win_amount_tasks(user_id, win_amount)
    # end

    {:ok, :updated}
  end

  defp handle_game_lost(user_id, %{game_id: game_id, loss_amount: loss_amount} = data) do
    # 重置连胜
    reset_win_streak(user_id)

    # 更新损失相关任务（如返水活动）
    if loss_amount > 0 do
      update_loss_amount(user_id, loss_amount)
    end

    {:ok, :updated}
  end

  defp handle_recharge_completed(user_id, %{amount: amount, order_id: order_id} = data) do
    # 更新充值任务进度
    update_recharge_tasks(user_id, amount)

    # 更新累计充值
    update_cumulative_recharge(user_id, amount)

    # 触发充值活动事件
    PubSub.broadcast(
      Cypridina.PubSub,
      "activity:recharge:#{user_id}",
      {:recharge_completed, %{user_id: user_id, amount: amount}}
    )

    {:ok, :updated}
  end

  defp handle_user_login(user_id, %{login_time: login_time} = data) do
    # 更新每日登录任务
    update_daily_login_task(user_id, login_time)

    # 更新七日签到
    update_seven_day_login(user_id, login_time)

    # 检查连续登录
    check_consecutive_login(user_id, login_time)

    {:ok, :updated}
  end

  defp get_user_active_tasks(user_id) do
    # 获取所有类型的活动任务
    game_tasks = get_active_game_tasks(user_id)
    recharge_tasks = get_active_recharge_tasks(user_id)
    daily_tasks = get_active_daily_tasks(user_id)

    game_tasks ++ recharge_tasks ++ daily_tasks
  end

  defp get_active_game_tasks(user_id) do
    case GameTask.list_active_tasks() do
      {:ok, tasks} ->
        Enum.filter(tasks, fn task ->
          task_not_completed?(user_id, :game_task, task.id)
        end)

      _ ->
        []
    end
  end

  defp get_active_recharge_tasks(user_id) do
    case RechargeTask.list() do
      {:ok, tasks} ->
        Enum.filter(tasks, fn task ->
          task.status == :active and task_not_completed?(user_id, :recharge_task, task.id)
        end)

      _ ->
        []
    end
  end

  defp get_active_daily_tasks(user_id) do
    # 获取每日任务
    []
  end

  defp task_not_completed?(user_id, task_type, task_id) do
    case ActivityService.get_user_participation(user_id, task_type, task_id) do
      {:ok, participation} when not is_nil(participation) ->
        participation.status != :completed

      _ ->
        true
    end
  end

  defp check_task_completion(user_id, %{task_type: :game_rounds} = task) do
    case ActivityService.get_user_participation(user_id, :game_task, task.id) do
      {:ok, participation} when not is_nil(participation) ->
        if participation.progress >= task.required_count do
          mark_task_completed(participation)
        end

      _ ->
        nil
    end
  end

  defp check_task_completion(user_id, task) do
    # 其他任务类型的完成检查
    nil
  end

  defp update_game_round_tasks(user_id, count) do
    # 获取所有游戏局数任务
    case GameTask.list_active_tasks() do
      {:ok, tasks} ->
        # 过滤出游戏局数任务
        game_round_tasks = Enum.filter(tasks, fn task -> task.task_type == :game_rounds end)

        Enum.each(game_round_tasks, fn task ->
          update_single_task_progress(user_id, :game_task, task.id, count, task.required_count)
        end)

      _ ->
        nil
    end
  end

  defp update_win_tasks(user_id, count) do
    # 获取所有胜利次数任务
    case GameTask.list_active_tasks() do
      {:ok, tasks} ->
        # 过滤出胜利次数任务
        win_tasks = Enum.filter(tasks, fn task -> task.task_type == :win_rounds end)

        Enum.each(win_tasks, fn task ->
          update_single_task_progress(user_id, :game_task, task.id, count, task.required_count)
        end)

      _ ->
        nil
    end
  end

  # 暂时禁用赢取金额任务功能，因为GameTask资源不支持:win_amount任务类型
  # defp update_win_amount_tasks(user_id, amount) do
  #   # 获取所有赢取金额任务
  #   case GameTask.list_active_tasks() do
  #     {:ok, tasks} ->
  #       # 过滤出赢取金额任务
  #       win_amount_tasks = Enum.filter(tasks, fn task -> task.task_type == :win_amount end)
  #
  #       Enum.each(win_amount_tasks, fn task ->
  #         update_single_task_progress(user_id, :game_task, task.id, amount, task.required_count)
  #       end)
  #
  #     _ ->
  #       nil
  #   end
  # end

  defp update_recharge_tasks(user_id, amount) do
    # 获取所有充值任务
    case RechargeTask.list_active_tasks() do
      {:ok, tasks} ->
        Enum.each(tasks, fn task ->
          update_single_task_progress(
            user_id,
            :recharge_task,
            task.id,
            amount,
            Decimal.to_integer(task.recharge_amount)
          )
        end)

      _ ->
        nil
    end
  end

  defp update_single_task_progress(user_id, task_type, task_id, progress_delta, required_amount) do
    case ActivityService.get_user_participation(user_id, task_type, task_id) do
      {:ok, nil} ->
        # 首次参与
        ActivityService.participate_activity(user_id, task_type, task_id, %{
          start_time: DateTime.utc_now()
        })
        |> case do
          {:ok, participation} ->
            update_participation_progress(participation, progress_delta, required_amount)

          _ ->
            nil
        end

      {:ok, participation} ->
        update_participation_progress(participation, progress_delta, required_amount)

      _ ->
        nil
    end
  end

  defp update_participation_progress(participation, progress_delta, required_amount) do
    new_progress = participation.progress + progress_delta

    update_data = %{
      progress: new_progress,
      participation_data:
        Map.put(
          participation.participation_data || %{},
          :last_updated,
          DateTime.utc_now()
        )
    }

    # 如果达到要求，标记为可领取
    update_data =
      if new_progress >= required_amount do
        Map.put(update_data, :status, :claimable)
      else
        update_data
      end

    UserActivityParticipation.update_progress(participation, update_data)
  end

  defp update_win_streak(user_id, _count) do
    # 更新连胜统计
    case UserGameStatistics.update_user_win_streak(user_id, true) do
      {:ok, _stats} ->
        Logger.debug("用户 #{user_id} 连胜统计已更新")
        :ok

      {:error, reason} ->
        Logger.error("更新用户 #{user_id} 连胜统计失败: #{inspect(reason)}")
        :error
    end
  end

  defp reset_win_streak(user_id) do
    # 重置连胜统计
    case UserGameStatistics.update_user_win_streak(user_id, false) do
      {:ok, _stats} ->
        Logger.debug("用户 #{user_id} 连胜统计已重置")
        :ok

      {:error, reason} ->
        Logger.error("重置用户 #{user_id} 连胜统计失败: #{inspect(reason)}")
        :error
    end
  end

  defp update_loss_amount(user_id, amount) do
    # 更新损失金额（用于返水）
    case UserGameStatistics.update_user_loss(user_id, amount) do
      {:ok, _stats} ->
        Logger.debug("用户 #{user_id} 损失统计已更新: #{amount}")
        :ok

      {:error, reason} ->
        Logger.error("更新用户 #{user_id} 损失统计失败: #{inspect(reason)}")
        :error
    end
  end

  defp update_cumulative_recharge(user_id, amount) do
    # 更新累计充值金额
    case UserGameStatistics.update_user_recharge(user_id, amount) do
      {:ok, _stats} ->
        Logger.debug("用户 #{user_id} 充值统计已更新: #{amount}")
        :ok

      {:error, reason} ->
        Logger.error("更新用户 #{user_id} 充值统计失败: #{inspect(reason)}")
        :error
    end
  end

  defp update_daily_login_task(user_id, login_time) do
    # 更新每日登录任务
    today = Date.utc_today()

    case ActivityService.get_user_participation(user_id, :seven_day_task) do
      {:ok, nil} ->
        # 首次参与七日签到
        ActivityService.participate_activity(user_id, :seven_day_task, nil, %{
          start_date: today,
          last_login: login_time,
          claimed_days: []
        })

      {:ok, participation} ->
        data = participation.participation_data || %{}
        last_login = Map.get(data, "last_login")

        # 检查是否是新的一天
        if is_nil(last_login) or Date.compare(DateTime.to_date(last_login), today) == :lt do
          new_data =
            data
            |> Map.put("last_login", login_time)
            |> Map.put("current_day", participation.progress + 1)

          UserActivityParticipation.update_progress(participation, %{
            progress: participation.progress + 1,
            participation_data: new_data
          })
        end

      _ ->
        nil
    end
  end

  defp update_seven_day_login(user_id, login_time) do
    # 七日签到更新逻辑已在update_daily_login_task中处理
    :ok
  end

  defp check_consecutive_login(user_id, login_time) do
    # TODO: 检查连续登录天数
    :ok
  end

  defp get_task(:game_task, task_id), do: GameTask.read(task_id)
  defp get_task(:recharge_task, task_id), do: RechargeTask.read(task_id)
  defp get_task(:seven_day_task, task_id), do: SevenDayTask.read(task_id)
  defp get_task(:free_bonus_task, task_id), do: FreeBonusTask.read(task_id)
  defp get_task(_, _), do: {:error, :unknown_task_type}

  defp validate_task_completion(nil, _task), do: {:error, :not_participated}

  defp validate_task_completion(participation, task) do
    cond do
      participation.status == :completed ->
        {:error, :already_claimed}

      participation.progress < get_task_requirement(task) ->
        {:error, :task_not_completed}

      true ->
        :ok
    end
  end

  defp get_task_requirement(%GameTask{required_count: count}), do: count

  defp get_task_requirement(%RechargeTask{recharge_amount: amount}),
    do: Decimal.to_integer(amount)

  defp get_task_requirement(%SevenDayTask{day_number: day}), do: day
  defp get_task_requirement(_), do: 1

  defp calculate_task_reward(%GameTask{reward_amount: amount}),
    do: {:ok, Decimal.to_integer(amount)}

  defp calculate_task_reward(%RechargeTask{reward_amount: amount}),
    do: {:ok, Decimal.to_integer(amount)}

  defp calculate_task_reward(%SevenDayTask{reward_amount: amount}),
    do: {:ok, Decimal.to_integer(amount)}

  defp calculate_task_reward(%FreeBonusTask{reward_amount: amount}),
    do: {:ok, Decimal.to_integer(amount)}

  defp calculate_task_reward(_), do: {:error, :no_reward}

  defp distribute_task_reward(user_id, task, reward) do
    # 发放积分奖励
    Cypridina.Accounts.add_points(user_id, reward,
      transaction_type: :bonus,
      description: "task_reward"
    )

    # 记录奖励发放
    RewardClaimRecord.create(%{
      user_id: user_id,
      activity_type: get_task_activity_type(task),
      activity_id: task.id,
      reward_type: :coins,
      reward_amount: reward,
      reward_data: %{
        task_name: task.task_name || "未知任务",
        claimed_at: DateTime.utc_now()
      }
    })
  end

  defp get_task_activity_type(%GameTask{}), do: :game_task
  defp get_task_activity_type(%RechargeTask{}), do: :recharge_task
  defp get_task_activity_type(%SevenDayTask{}), do: :seven_day_task
  defp get_task_activity_type(%FreeBonusTask{}), do: :free_bonus_task
  defp get_task_activity_type(_), do: :unknown

  defp mark_task_claimed(participation) do
    UserActivityParticipation.update_progress(participation, %{
      status: :completed,
      participation_data:
        Map.put(
          participation.participation_data || %{},
          :claimed_at,
          DateTime.utc_now()
        )
    })
  end

  defp mark_task_completed(participation) do
    UserActivityParticipation.update_progress(participation, %{
      status: :claimable,
      participation_data:
        Map.put(
          participation.participation_data || %{},
          :completed_at,
          DateTime.utc_now()
        )
    })
  end
end
