defmodule Teen.ActivitySystem.CdkeyService do
  @moduledoc """
  兑换码服务

  处理兑换码的生成、验证和兑换
  """

  alias Teen.ActivitySystem.{CdkeyActivity, CdkeyTemplate, CdkeyBatch, RewardClaimRecord, CdkeyManagementService}
  alias Cypridina.Accounts.User
  alias Phoenix.PubSub

  require Logger

  # 兑换码字符集（去除容易混淆的字符）
  @charset "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"
  @code_length 12

  @doc """
  生成兑换码
  """
  def generate_codes(batch_name, count, reward_config) do
    codes =
      for _ <- 1..count do
        generate_single_code()
      end

    # 创建兑换码记录
    Enum.map(codes, fn code ->
      case CdkeyActivity.create(%{
             code: code,
             batch_name: batch_name,
             reward_type: reward_config.type || :coins,
             reward_amount: reward_config.amount || 0,
             reward_items: reward_config.items || %{},
             max_uses: reward_config.max_uses || 1,
             used_count: 0,
             valid_from: reward_config.valid_from || DateTime.utc_now(),
             valid_to:
               reward_config.valid_to ||
                 DateTime.add(DateTime.utc_now(), 30 * 24 * 60 * 60, :second),
             status: :active,
             metadata: %{
               "creator" => reward_config.creator,
               "purpose" => reward_config.purpose
             }
           }) do
        {:ok, cdkey} ->
          Logger.info("生成兑换码: #{code}")
          {:ok, cdkey}

        {:error, reason} ->
          Logger.error("生成兑换码失败: #{inspect(reason)}")
          {:error, reason}
      end
    end)
  end

  @doc """
  使用兑换码
  """
  def redeem_code(user_id, code) do
    with {:ok, cdkey} <- validate_code(code),
         :ok <- CdkeyManagementService.validate_user_eligibility(user_id, cdkey),
         {:ok, _} <- mark_code_used(cdkey, user_id),
         {:ok, _} <- distribute_rewards(user_id, cdkey),
         {:ok, _} <- create_redeem_record(user_id, cdkey) do
      Logger.info("兑换码使用成功: 用户=#{user_id}, 码=#{code}")

      # 发布兑换事件
      PubSub.broadcast(Cypridina.PubSub, "cdkey_events", {
        :cdkey_redeemed,
        %{
          user_id: user_id,
          code: code,
          batch_name: cdkey.batch_name,
          rewards: format_rewards(cdkey),
          timestamp: DateTime.utc_now()
        }
      })

      {:ok,
       %{
         code: code,
         rewards: format_rewards(cdkey),
         message: "兑换成功"
       }}
    else
      {:error, reason} ->
        Logger.error("兑换码使用失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  验证兑换码
  """
  def validate_code(code) do
    code = String.upcase(String.trim(code))

    case CdkeyActivity.by_code(code) do
      {:ok, [cdkey | _]} ->
        validate_cdkey_status(cdkey)

      {:ok, []} ->
        {:error, :invalid_code}

      {:error, _} ->
        {:error, :system_error}
    end
  end

  @doc """
  批量生成兑换码（带前缀）
  """
  def generate_batch_with_prefix(prefix, count, reward_config) do
    batch_name = "#{prefix}_#{DateTime.to_unix(DateTime.utc_now())}"

    codes =
      for i <- 1..count do
        "#{prefix}#{generate_suffix(8)}"
      end
      |> Enum.uniq()
      |> Enum.take(count)

    # 创建兑换码记录
    results =
      Enum.map(codes, fn code ->
        case CdkeyActivity.create(%{
               code: code,
               batch_name: batch_name,
               reward_type: reward_config.type || :coins,
               reward_amount: reward_config.amount || 0,
               reward_items: reward_config.items || %{},
               max_uses: reward_config.max_uses || 1,
               used_count: 0,
               valid_from: reward_config.valid_from || DateTime.utc_now(),
               valid_to:
                 reward_config.valid_to ||
                   DateTime.add(DateTime.utc_now(), 30 * 24 * 60 * 60, :second),
               status: :active,
               metadata: %{
                 "prefix" => prefix,
                 "creator" => reward_config.creator,
                 "purpose" => reward_config.purpose
               }
             }) do
          {:ok, cdkey} ->
            {:ok, cdkey}

          {:error, reason} ->
            {:error, reason}
        end
      end)

    successful = Enum.filter(results, fn {status, _} -> status == :ok end)

    Logger.info("批量生成兑换码: 批次=#{batch_name}, 成功=#{length(successful)}/#{count}")

    {:ok,
     %{
       batch_name: batch_name,
       total: count,
       successful: length(successful),
       codes: Enum.map(successful, fn {:ok, cdkey} -> cdkey.code end)
     }}
  end

  @doc """
  查询兑换码状态
  """
  def get_code_status(code) do
    case validate_code(code) do
      {:ok, cdkey} ->
        {:ok,
         %{
           code: cdkey.code,
           status: cdkey.status,
           used_count: cdkey.used_count,
           max_uses: cdkey.max_uses,
           remaining_uses: cdkey.max_uses - cdkey.used_count,
           valid_from: cdkey.valid_from,
           valid_to: cdkey.valid_to,
           rewards: format_rewards(cdkey)
         }}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取批次统计
  """
  def get_batch_statistics(batch_name) do
    case CdkeyActivity.by_batch(batch_name) do
      {:ok, cdkeys} ->
        total = length(cdkeys)
        used = Enum.count(cdkeys, fn c -> c.used_count > 0 end)

        expired =
          Enum.count(cdkeys, fn c ->
            DateTime.compare(DateTime.utc_now(), c.valid_to) == :gt
          end)

        {:ok,
         %{
           batch_name: batch_name,
           total_codes: total,
           used_codes: used,
           unused_codes: total - used,
           expired_codes: expired,
           usage_rate: if(total > 0, do: Float.round(used / total * 100, 2), else: 0)
         }}

      _ ->
        {:error, :batch_not_found}
    end
  end

  @doc """
  导出兑换码
  """
  def export_codes(batch_name) do
    case CdkeyActivity.by_batch(batch_name) do
      {:ok, cdkeys} ->
        csv_content =
          (["兑换码,状态,使用次数,最大使用次数,有效期开始,有效期结束,奖励类型,奖励数量\n"] ++
             Enum.map(cdkeys, fn c ->
               "#{c.code},#{c.status},#{c.used_count},#{c.max_uses},#{c.valid_from},#{c.valid_to},#{c.reward_type},#{c.reward_amount}\n"
             end))
          |> Enum.join("")

        {:ok, csv_content}

      _ ->
        {:error, :batch_not_found}
    end
  end

  # 私有函数

  defp generate_single_code do
    for _ <- 1..@code_length, into: "" do
      <<Enum.random(String.to_charlist(@charset))>>
    end
    |> format_code()
  end

  defp generate_suffix(length) do
    for _ <- 1..length, into: "" do
      <<Enum.random(String.to_charlist(@charset))>>
    end
  end

  defp format_code(code) do
    # 格式化为 XXXX-XXXX-XXXX
    code
    |> String.graphemes()
    |> Enum.chunk_every(4)
    |> Enum.map(&Enum.join/1)
    |> Enum.join("-")
  end

  defp validate_cdkey_status(cdkey) do
    now = DateTime.utc_now()

    cond do
      cdkey.status != :active ->
        {:error, :code_inactive}

      DateTime.compare(now, cdkey.valid_from) == :lt ->
        {:error, :code_not_yet_valid}

      DateTime.compare(now, cdkey.valid_to) == :gt ->
        {:error, :code_expired}

      cdkey.used_count >= cdkey.max_uses ->
        {:error, :code_used_up}

      true ->
        {:ok, cdkey}
    end
  end

  defp check_user_eligibility(user_id, cdkey) do
    # 检查用户是否已经使用过这个兑换码
    case RewardClaimRecord.read() do
      {:ok, records} ->
        already_used =
          Enum.any?(records, fn r ->
            r.user_id == user_id and
              r.activity_type == :cdkey and
              Map.get(r.reward_data || %{}, "code") == cdkey.code
          end)

        if already_used do
          {:error, :already_redeemed}
        else
          # 检查是否达到同类型兑换码使用限制
          check_category_limit(user_id, cdkey)
        end

      _ ->
        :ok
    end
  end

  defp check_category_limit(user_id, cdkey) do
    # 可以根据兑换码的元数据检查用户是否超过某类兑换码的使用限制
    # 例如：每个用户每月只能使用3个新手礼包码
    :ok
  end

  defp mark_code_used(cdkey, user_id) do
    new_used_count = cdkey.used_count + 1
    new_status = if new_used_count >= cdkey.max_uses, do: :used_up, else: :active

    used_by = Map.get(cdkey.metadata || %{}, "used_by", [])
    new_used_by = used_by ++ [%{"user_id" => user_id, "used_at" => DateTime.utc_now()}]

    CdkeyActivity.update(cdkey, %{
      used_count: new_used_count,
      status: new_status,
      metadata: Map.put(cdkey.metadata || %{}, "used_by", new_used_by)
    })
  end

  defp distribute_rewards(user_id, cdkey) do
    results = []

    # 发放金币奖励
    if cdkey.reward_amount > 0 do
      amount = Decimal.to_integer(cdkey.reward_amount)
      Cypridina.Accounts.add_points(user_id, amount, 
        transaction_type: :bonus,
        description: "兑换码奖励",
        metadata: %{
          "operation" => "cdkey_redeem",
          "cdkey_code" => cdkey.code,
          "amount" => Decimal.to_string(cdkey.reward_amount)
        }
      )
    end

    # 发放道具奖励
    if cdkey.reward_items && map_size(cdkey.reward_items) > 0 do
      # TODO: 实现道具发放逻辑
      Logger.info("发放道具奖励: #{inspect(cdkey.reward_items)}")
    end

    {:ok, :rewards_distributed}
  end

  defp create_redeem_record(user_id, cdkey) do
    RewardClaimRecord.create(%{
      user_id: user_id,
      activity_type: :cdkey,
      activity_id: cdkey.id,
      reward_type: cdkey.reward_type,
      reward_amount: cdkey.reward_amount,
      reward_data: %{
        "code" => cdkey.code,
        "batch_name" => cdkey.batch_name,
        "reward_items" => cdkey.reward_items,
        "redeemed_at" => DateTime.utc_now()
      }
    })
  end

  defp format_rewards(cdkey) do
    rewards = []

    if cdkey.reward_amount > 0 do
      rewards =
        rewards ++
          [
            %{
              type: cdkey.reward_type,
              amount: Decimal.to_integer(cdkey.reward_amount)
            }
          ]
    end

    if cdkey.reward_items && map_size(cdkey.reward_items) > 0 do
      item_rewards =
        Enum.map(cdkey.reward_items, fn {item_id, quantity} ->
          %{type: :item, item_id: item_id, quantity: quantity}
        end)

      rewards = rewards ++ item_rewards
    end

    rewards
  end

  @doc """
  使兑换码失效
  """
  def deactivate_code(code) do
    case validate_code(code) do
      {:ok, cdkey} ->
        CdkeyActivity.update(cdkey, %{status: :inactive})

      error ->
        error
    end
  end

  @doc """
  批量使兑换码失效
  """
  def deactivate_batch(batch_name) do
    case CdkeyActivity.by_batch(batch_name) do
      {:ok, cdkeys} ->
        results =
          Enum.map(cdkeys, fn cdkey ->
            CdkeyActivity.update(cdkey, %{status: :inactive})
          end)

        successful = Enum.count(results, fn {status, _} -> status == :ok end)
        {:ok, %{total: length(cdkeys), successful: successful}}

      _ ->
        {:error, :batch_not_found}
    end
  end
end
