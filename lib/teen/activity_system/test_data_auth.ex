defmodule Teen.ActivitySystem.TestDataAuth do
  import Bitwise
  @moduledoc """
  测试数据管理权限控制模块
  
  负责验证用户权限、环境检查、操作确认等安全措施
  """

  alias Teen.SystemSettings.{AdminUser, Role, Permission}
  alias Cypridina.Accounts.User
  
  @required_permission "test_data_management"
  @dangerous_operations ["clear_all_users", "batch_clear", "clear_by_activity_type"]

  @doc """
  验证用户是否有测试数据管理权限
  """
  def authorize_user(admin_user_id) do
    with {:ok, admin_user} <- get_admin_user(admin_user_id),
         {:ok, _} <- check_permission(admin_user),
         :ok <- check_environment() do
      {:ok, admin_user}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  验证操作权限并获取确认信息
  """
  def authorize_operation(admin_user, operation_type, params \\ %{}) do
    with :ok <- validate_operation_type(operation_type),
         :ok <- check_dangerous_operation(operation_type, admin_user),
         {:ok, confirmation} <- generate_confirmation_info(operation_type, params) do
      {:ok, confirmation}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  验证操作确认令牌
  """
  def verify_confirmation_token(admin_user_id, operation_type, token, params) do
    expected_token = generate_confirmation_token(admin_user_id, operation_type, params)
    
    if secure_compare(token, expected_token) do
      :ok
    else
      {:error, "确认令牌无效"}
    end
  end

  @doc """
  生成操作确认令牌
  """
  def generate_confirmation_token(admin_user_id, operation_type, params) do
    data = "#{admin_user_id}:#{operation_type}:#{Jason.encode!(params)}:#{System.system_time(:second)}"
    :crypto.hash(:sha256, data) |> Base.encode16(case: :lower)
  end

  @doc """
  创建操作员信息结构
  """
  def create_operator_info(admin_user, ip_address, user_agent) do
    %{
      id: admin_user.id,
      username: admin_user.username,
      ip_address: ip_address,
      user_agent: user_agent,
      authorized_at: DateTime.utc_now()
    }
  end

  # 私有函数

  defp get_admin_user(admin_user_id) do
    case AdminUser |> Ash.get(admin_user_id) do
      {:ok, admin_user} -> {:ok, admin_user}
      {:error, _} -> {:error, "管理员用户不存在"}
    end
  end

  defp check_permission(admin_user) do
    # 检查用户是否有测试数据管理权限
    # 这里需要根据你们的权限系统实现
    permissions = get_user_permissions(admin_user)
    
    if @required_permission in permissions do
      {:ok, :authorized}
    else
      {:error, "用户没有测试数据管理权限"}
    end
  end

  defp check_environment do
    case Application.get_env(:cypridina, :environment, :prod) do
      env when env in [:dev, :test] -> :ok
      _ -> {:error, "测试数据管理功能仅在开发和测试环境中可用"}
    end
  end

  defp validate_operation_type(operation_type) do
    valid_operations = [
      "clear_user_activity_data",
      "batch_clear_user_data", 
      "clear_by_activity_type",
      "simulate_time_advance",
      "reset_activity_progress",
      "preview_clear_data"
    ]
    
    if operation_type in valid_operations do
      :ok
    else
      {:error, "不支持的操作类型: #{operation_type}"}
    end
  end

  defp check_dangerous_operation(operation_type, admin_user) do
    if operation_type in @dangerous_operations do
      # 危险操作需要更高级别的权限验证
      if has_dangerous_operation_permission?(admin_user) do
        :ok
      else
        {:error, "该操作需要更高级别的权限"}
      end
    else
      :ok
    end
  end

  defp generate_confirmation_info(operation_type, params) do
    confirmation = %{
      operation_type: operation_type,
      params: params,
      warnings: get_operation_warnings(operation_type, params),
      requires_confirmation: requires_confirmation?(operation_type),
      estimated_affected_records: estimate_affected_records(operation_type, params)
    }
    
    {:ok, confirmation}
  end

  defp get_operation_warnings("clear_user_activity_data", %{"user_id" => user_id}) do
    [
      "将清除用户 #{user_id} 的所有活动数据",
      "此操作无法撤销",
      "请确认用户ID正确"
    ]
  end

  defp get_operation_warnings("batch_clear_user_data", %{"user_ids" => user_ids}) do
    [
      "将批量清除 #{length(user_ids)} 个用户的活动数据",
      "此操作无法撤销",
      "建议先进行预览操作确认范围"
    ]
  end

  defp get_operation_warnings("clear_by_activity_type", %{"activity_type" => activity_type}) do
    [
      "将清除所有参与 #{activity_type} 活动的用户数据",
      "此操作可能影响大量用户",
      "建议先进行预览操作确认影响范围"
    ]
  end

  defp get_operation_warnings("simulate_time_advance", %{"days_forward" => days}) do
    [
      "将模拟时间推进 #{days} 天",
      "这可能影响基于时间的活动逻辑",
      "请确认推进天数正确"
    ]
  end

  defp get_operation_warnings(_, _), do: ["请仔细确认操作参数"]

  defp requires_confirmation?("preview_clear_data"), do: false
  defp requires_confirmation?(_), do: true

  defp estimate_affected_records("clear_user_activity_data", %{"user_id" => user_id}) do
    # 这里可以实际查询数据库获取精确数量
    # 为了简化，返回估算值
    %{
      participations: "估算 5-50 条",
      records: "估算 10-100 条", 
      rewards: "估算 0-20 条"
    }
  end

  defp estimate_affected_records(_, _), do: %{total: "待确认"}

  defp get_user_permissions(admin_user) do
    # 简化实现，实际应该从数据库查询
    # 这里假设有基本的权限检查
    case admin_user.username do
      "developer" -> [@required_permission]
      "tester" -> [@required_permission]  
      _ -> []
    end
  end

  defp has_dangerous_operation_permission?(admin_user) do
    # 危险操作权限检查
    admin_user.username in ["developer", "super_admin"]
  end

  defp secure_compare(a, b) when byte_size(a) == byte_size(b) do
    secure_compare_accumulator(a, b, 0, 0)
  end
  
  defp secure_compare(_, _), do: false

  defp secure_compare_accumulator(<<a, rest_a::binary>>, <<b, rest_b::binary>>, index, acc) do
    xor_result = bxor(a, b)
    secure_compare_accumulator(rest_a, rest_b, index + 1, acc ||| xor_result)
  end

  defp secure_compare_accumulator(<<>>, <<>>, _index, acc) do
    acc === 0
  end
end