defmodule Teen.ActivitySystem.TestDataBackup do
  @moduledoc """
  测试数据备份和回滚功能

  在执行危险操作前创建数据备份，支持操作回滚
  """

  import Ash.Expr

  alias Teen.ActivitySystem.{UserActivityParticipation, UserActivityRecord, RewardClaimRecord}
  alias Teen.SystemSettings.OperationLog
  require Logger

  @backup_retention_days 7

  @doc """
  创建数据备份
  """
  def create_backup(user_id, activity_types, operator) do
    backup_id = generate_backup_id()
    backup_data = collect_backup_data(user_id, activity_types)

    backup_record = %{
      backup_id: backup_id,
      user_id: user_id,
      activity_types: activity_types,
      backup_data: backup_data,
      created_by: operator.id,
      created_at: DateTime.utc_now(),
      status: :active
    }

    # 保存备份到文件系统或数据库
    case save_backup(backup_record) do
      :ok ->
        log_backup_operation(operator, "create_backup", backup_record)
        {:ok, backup_id}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  执行数据回滚
  """
  def rollback_data(backup_id, operator) do
    with {:ok, backup_record} <- load_backup(backup_id),
         :ok <- validate_backup(backup_record),
         {:ok, _result} <- restore_backup_data(backup_record, operator) do

      mark_backup_used(backup_id)
      log_backup_operation(operator, "rollback_data", backup_record)
      {:ok, backup_record}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  列出可用的备份
  """
  def list_backups(user_id \\ nil, limit \\ 50) do
    backups_dir = get_backups_directory()

    case File.ls(backups_dir) do
      {:ok, files} ->
        backups = files
        |> Enum.filter(&String.ends_with?(&1, ".backup"))
        |> Enum.map(&load_backup_metadata/1)
        |> Enum.reject(&is_nil/1)
        |> Enum.filter(&filter_by_user(&1, user_id))
        |> Enum.sort_by(& &1.created_at, {:desc, DateTime})
        |> Enum.take(limit)

        {:ok, backups}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  清理过期备份
  """
  def cleanup_expired_backups do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-@backup_retention_days * 24 * 3600, :second)

    case list_backups() do
      {:ok, backups} ->
        expired_backups = Enum.filter(backups, fn backup ->
          DateTime.compare(backup.created_at, cutoff_date) == :lt
        end)

        results = Enum.map(expired_backups, fn backup ->
          case delete_backup(backup.backup_id) do
            :ok -> {backup.backup_id, :deleted}
            {:error, reason} -> {backup.backup_id, {:error, reason}}
          end
        end)

        Logger.info("清理过期备份: #{length(expired_backups)} 个备份被删除")
        {:ok, results}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # 私有函数

  defp generate_backup_id do
    timestamp = DateTime.utc_now() |> DateTime.to_unix()
    random = :crypto.strong_rand_bytes(8) |> Base.encode16(case: :lower)
    "backup_#{timestamp}_#{random}"
  end

  defp collect_backup_data(user_id, activity_types) do
    participations = UserActivityParticipation
    |> Ash.Query.filter(expr(user_id == ^user_id))
    |> apply_activity_filter(activity_types)
    |> Ash.read!()
    |> Enum.map(&serialize_record/1)

    records = UserActivityRecord
    |> Ash.Query.filter(expr(user_id == ^user_id))
    |> apply_activity_filter(activity_types)
    |> Ash.read!()
    |> Enum.map(&serialize_record/1)

    rewards = RewardClaimRecord
    |> Ash.Query.filter(expr(user_id == ^user_id))
    |> apply_activity_filter(activity_types)
    |> Ash.read!()
    |> Enum.map(&serialize_record/1)

    %{
      participations: participations,
      records: records,
      rewards: rewards,
      metadata: %{
        user_id: user_id,
        activity_types: activity_types,
        backup_created_at: DateTime.utc_now(),
        total_records: length(participations) + length(records) + length(rewards)
      }
    }
  end

  defp apply_activity_filter(query, []), do: query
  defp apply_activity_filter(query, activity_types) do
    query |> Ash.Query.filter(expr(activity_type in ^activity_types))
  end

  defp serialize_record(record) do
    record
    |> Map.from_struct()
    |> Map.drop([:__meta__, :__struct__])
    |> Enum.map(fn {k, v} ->
      case v do
        %DateTime{} -> {k, DateTime.to_iso8601(v)}
        %NaiveDateTime{} -> {k, NaiveDateTime.to_iso8601(v)}
        _ -> {k, v}
      end
    end)
    |> Enum.into(%{})
  end

  defp save_backup(backup_record) do
    backups_dir = get_backups_directory()
    File.mkdir_p(backups_dir)

    backup_file = Path.join(backups_dir, "#{backup_record.backup_id}.backup")
    backup_json = Jason.encode!(backup_record)

    case File.write(backup_file, backup_json) do
      :ok -> :ok
      {:error, reason} -> {:error, "保存备份失败: #{reason}"}
    end
  end

  defp load_backup(backup_id) do
    backup_file = Path.join(get_backups_directory(), "#{backup_id}.backup")

    case File.read(backup_file) do
      {:ok, content} ->
        case Jason.decode(content) do
          {:ok, backup_data} -> {:ok, atomize_keys(backup_data)}
          {:error, reason} -> {:error, "解析备份文件失败: #{reason}"}
        end

      {:error, :enoent} ->
        {:error, "备份文件不存在: #{backup_id}"}

      {:error, reason} ->
        {:error, "读取备份文件失败: #{reason}"}
    end
  end

  defp validate_backup(backup_record) do
    cond do
      backup_record[:status] == "used" ->
        {:error, "备份已被使用，无法再次回滚"}

      is_backup_expired?(backup_record) ->
        {:error, "备份已过期"}

      true ->
        :ok
    end
  end

  defp restore_backup_data(backup_record, operator) do
    Cypridina.Repo.transaction(fn ->
      backup_data = backup_record.backup_data

      # 首先删除现有数据（如果有的话）
      clear_existing_data(backup_record.user_id, backup_record.activity_types)

      # 恢复参与记录
      Enum.each(backup_data["participations"] || [], fn participation_data ->
        restore_participation(participation_data)
      end)

      # 恢复活动记录
      Enum.each(backup_data["records"] || [], fn record_data ->
        restore_activity_record(record_data)
      end)

      # 恢复奖励记录
      Enum.each(backup_data["rewards"] || [], fn reward_data ->
        restore_reward_record(reward_data)
      end)

      %{
        user_id: backup_record.user_id,
        backup_id: backup_record.backup_id,
        restored_records: backup_data["metadata"]["total_records"]
      }
    end)
  end

  defp clear_existing_data(user_id, activity_types) do
    # 删除现有数据
    UserActivityParticipation
    |> Ash.Query.filter(expr(user_id == ^user_id))
    |> apply_activity_filter(activity_types)
    |> Ash.bulk_destroy!(:destroy, %{})

    UserActivityRecord
    |> Ash.Query.filter(expr(user_id == ^user_id))
    |> apply_activity_filter(activity_types)
    |> Ash.bulk_destroy!(:destroy, %{})

    RewardClaimRecord
    |> Ash.Query.filter(expr(user_id == ^user_id))
    |> apply_activity_filter(activity_types)
    |> Ash.bulk_destroy!(:destroy, %{})
  end

  defp restore_participation(data) do
    UserActivityParticipation.create!(deserialize_record(data, UserActivityParticipation))
  end

  defp restore_activity_record(data) do
    UserActivityRecord.create!(deserialize_record(data, UserActivityRecord))
  end

  defp restore_reward_record(data) do
    RewardClaimRecord.create!(deserialize_record(data, RewardClaimRecord))
  end

  defp deserialize_record(data, module) do
    data
    |> Enum.map(fn {k, v} ->
      key = if is_binary(k), do: String.to_atom(k), else: k

      value = case v do
        datetime_str when is_binary(datetime_str) ->
          case DateTime.from_iso8601(datetime_str) do
            {:ok, dt, _} -> dt
            _ ->
              case NaiveDateTime.from_iso8601(datetime_str) do
                {:ok, ndt} -> ndt
                _ -> v
              end
          end
        _ -> v
      end

      {key, value}
    end)
    |> Enum.into(%{})
    |> Map.drop([:id])  # 移除原ID，让系统生成新ID
  end

  defp mark_backup_used(backup_id) do
    case load_backup(backup_id) do
      {:ok, backup_record} ->
        updated_record = Map.put(backup_record, :status, "used")
        save_backup(updated_record)

      {:error, _} -> :ok
    end
  end

  defp delete_backup(backup_id) do
    backup_file = Path.join(get_backups_directory(), "#{backup_id}.backup")
    File.rm(backup_file)
  end

  defp load_backup_metadata(filename) do
    backup_file = Path.join(get_backups_directory(), filename)

    case File.read(backup_file) do
      {:ok, content} ->
        case Jason.decode(content) do
          {:ok, data} ->
            %{
              backup_id: data["backup_id"],
              user_id: data["user_id"],
              activity_types: data["activity_types"],
              created_at: parse_datetime(data["created_at"]),
              status: data["status"] || "active",
              total_records: get_in(data, ["backup_data", "metadata", "total_records"]) || 0
            }

          _ -> nil
        end

      _ -> nil
    end
  end

  defp filter_by_user(backup, nil), do: true
  defp filter_by_user(backup, user_id), do: backup.user_id == user_id

  defp is_backup_expired?(backup_record) do
    cutoff_date = DateTime.utc_now() |> DateTime.add(-@backup_retention_days * 24 * 3600, :second)
    created_at = parse_datetime(backup_record[:created_at])

    DateTime.compare(created_at, cutoff_date) == :lt
  end

  defp parse_datetime(datetime_str) when is_binary(datetime_str) do
    case DateTime.from_iso8601(datetime_str) do
      {:ok, dt, _} -> dt
      _ -> DateTime.utc_now()
    end
  end
  defp parse_datetime(%DateTime{} = dt), do: dt
  defp parse_datetime(_), do: DateTime.utc_now()

  defp get_backups_directory do
    Application.get_env(:cypridina, :test_data_backup_dir, "./tmp/test_data_backups")
  end

  defp atomize_keys(map) when is_map(map) do
    Enum.map(map, fn {k, v} ->
      key = if is_binary(k), do: String.to_atom(k), else: k
      value = if is_map(v), do: atomize_keys(v), else: v
      {key, value}
    end)
    |> Enum.into(%{})
  end
  defp atomize_keys(value), do: value

  defp log_backup_operation(operator, operation_type, backup_record) do
    OperationLog.create(%{
      admin_user_id: operator.id,
      operation_type: "backup_#{operation_type}",
      operation_data: %{
        backup_id: backup_record[:backup_id] || backup_record.backup_id,
        user_id: backup_record[:user_id] || backup_record.user_id,
        activity_types: backup_record[:activity_types] || backup_record.activity_types
      },
      ip_address: operator.ip_address || "127.0.0.1",
      user_agent: operator.user_agent || "Test Data Manager"
    })
  rescue
    e ->
      Logger.error("记录备份操作日志失败: #{inspect(e)}")
      :ok
  end
end
