defmodule Teen.ActivitySystem.TestDataManager do
  @moduledoc """
  测试环境活动数据管理器

  专门用于测试环境的数据清除和重置功能
  包括用户活动状态重置、时间模拟等功能

  重要: 仅在开发和测试环境中启用
  """

  import Ash.Expr

  alias Teen.ActivitySystem.{
    UserActivityParticipation,
    UserActivityRecord,
    RewardClaimRecord,
    SignInActivity,
    WeeklyCard,
    GameTask,
    SevenDayTask,
    VipGift,
    RechargeTask,
    ScratchCardActivity,
    FirstRechargeGift,
    LossRebateJar,
    InviteCashActivity,
    BindingReward,
    FreeBonusTask,
    CdkeyActivity
  }

  alias Teen.SystemSettings.OperationLog
  alias Cypridina.Accounts.User
  require Logger

  # 环境检查，确保只在非生产环境运行
  @allowed_environments [:dev, :test]

  @activity_modules %{
    sign_in_activity: SignInActivity,
    weekly_card: WeeklyCard,
    game_task: GameTask,
    seven_day_task: SevenDayTask,
    vip_gift: VipGift,
    recharge_task: RechargeTask,
    scratch_card: ScratchCardActivity,
    first_recharge_gift: FirstRechargeGift,
    loss_rebate_jar: LossRebateJar,
    invite_cash: InviteCashActivity,
    binding_reward: BindingReward,
    free_bonus_task: FreeBonusTask,
    cdkey_activity: CdkeyActivity
  }

  @doc """
  验证当前环境是否允许执行测试数据操作
  """
  def validate_environment! do
    current_env = Application.get_env(:cypridina, :environment, :prod)

    unless current_env in @allowed_environments do
      raise "测试数据管理功能仅在开发和测试环境中可用。当前环境: #{current_env}"
    end

    :ok
  end

  @doc """
  清除指定用户的活动数据

  ## 参数
  - user_id: 用户ID
  - activity_types: 要清除的活动类型列表，空列表表示清除所有
  - operator: 操作员信息
  - preview?: 是否为预览模式，不实际执行删除
  """
  def clear_user_activity_data(user_id, activity_types \\ [], operator, preview? \\ false) do
    validate_environment!()

    # 验证用户存在
    case User |> Ash.get(user_id) do
      {:ok, user} ->
        perform_clear_operation(user, activity_types, operator, preview?)

      {:error, _} ->
        {:error, "用户不存在: #{user_id}"}
    end
  end

  @doc """
  批量清除多个用户的活动数据
  """
  def batch_clear_user_data(user_ids, activity_types \\ [], operator, preview? \\ false) do
    validate_environment!()

    results = Enum.map(user_ids, fn user_id ->
      case clear_user_activity_data(user_id, activity_types, operator, preview?) do
        {:ok, result} -> {user_id, :ok, result}
        {:error, reason} -> {user_id, :error, reason}
      end
    end)

    {:ok, results}
  end

  @doc """
  按活动类型清除数据
  """
  def clear_by_activity_type(activity_type, operator, preview? \\ false) do
    validate_environment!()

    unless activity_type in Map.keys(@activity_modules) do
      {:error, "不支持的活动类型: #{activity_type}"}
    else
      # 获取所有参与该活动的用户
      {:ok, participations} = UserActivityParticipation
      |> Ash.Query.filter(expr(activity_type == ^activity_type))
      |> Ash.read()

      user_ids = participations |> Enum.map(& &1.user_id) |> Enum.uniq()

      batch_clear_user_data(user_ids, [activity_type], operator, preview?)
    end
  end

  @doc """
  预览将要删除的数据
  """
  def preview_clear_data(user_id, activity_types \\ []) do
    validate_environment!()

    # 统计各类数据数量
    participation_query = UserActivityParticipation
    |> Ash.Query.filter(expr(user_id == ^user_id))

    participation_query = if Enum.empty?(activity_types) do
      participation_query
    else
      participation_query |> Ash.Query.filter(expr(activity_type in ^activity_types))
    end

    {:ok, participation_count} = participation_query |> Ash.count()

    record_query = UserActivityRecord
    |> Ash.Query.filter(expr(user_id == ^user_id))

    record_query = if Enum.empty?(activity_types) do
      record_query
    else
      record_query |> Ash.Query.filter(expr(activity_type in ^activity_types))
    end

    {:ok, record_count} = record_query |> Ash.count()

    reward_query = RewardClaimRecord
    |> Ash.Query.filter(expr(user_id == ^user_id))

    reward_query = if Enum.empty?(activity_types) do
      reward_query
    else
      reward_query |> Ash.Query.filter(expr(activity_type in ^activity_types))
    end

    {:ok, reward_count} = reward_query |> Ash.count()

    {:ok, %{
      user_id: user_id,
      activity_types: activity_types,
      data_counts: %{
        participations: participation_count,
        records: record_count,
        rewards: reward_count
      },
      total_records: participation_count + record_count + reward_count
    }}
  end

  @doc """
  时间模拟功能 - 模拟时间推进
  """
  def simulate_time_advance(user_id, days_forward, operator) do
    validate_environment!()

    # 这里实现时间推进逻辑
    # 主要是更新各种时间相关的字段
    simulated_time = DateTime.utc_now() |> DateTime.add(days_forward * 24 * 3600, :second)

    # 更新用户活动参与记录中的时间字段
    {:ok, participations} = UserActivityParticipation
    |> Ash.Query.filter(expr(user_id == ^user_id))
    |> Ash.read()

    Enum.each(participations, fn participation ->
      new_data = Map.merge(participation.participation_data || %{}, %{
        "simulated_time" => simulated_time,
        "last_update" => DateTime.utc_now(),
        "time_advance_days" => days_forward
      })

      participation
      |> Ash.Changeset.for_update(:update, %{participation_data: new_data})
      |> Ash.update!()
    end)

    # 记录操作日志
    log_operation(operator, "time_simulation", %{
      user_id: user_id,
      days_forward: days_forward,
      simulated_time: simulated_time
    })

    {:ok, %{
      user_id: user_id,
      days_advanced: days_forward,
      simulated_time: simulated_time,
      affected_records: length(participations)
    }}
  end

  @doc """
  重置特定活动的进度
  """
  def reset_activity_progress(user_id, activity_type, operator) do
    validate_environment!()

    case UserActivityParticipation.reset_scratch_card_progress(user_id: user_id) do
      {:ok, updated_records} ->
        log_operation(operator, "reset_activity_progress", %{
          user_id: user_id,
          activity_type: activity_type,
          affected_records: length(updated_records)
        })

        {:ok, updated_records}

      {:error, reason} ->
        {:error, reason}
    end
  end

  # 私有函数

  defp perform_clear_operation(user, activity_types, operator, preview?) do
    if preview? do
      preview_clear_data(user.id, activity_types)
    else
      execute_clear_operation(user, activity_types, operator)
    end
  end

  defp execute_clear_operation(user, activity_types, operator) do
    Cypridina.Repo.transaction(fn ->
      # 1. 删除用户活动参与记录
      participation_query = UserActivityParticipation
      |> Ash.Query.filter(expr(user_id == ^user.id))

      participation_query = if Enum.empty?(activity_types) do
        participation_query
      else
        participation_query |> Ash.Query.filter(expr(activity_type in ^activity_types))
      end

      {:ok, participations} = participation_query |> Ash.read()
      participation_count = Enum.each(participations, &Ash.destroy!/1) |> length()

      # 2. 删除用户活动记录
      record_query = UserActivityRecord
      |> Ash.Query.filter(expr(user_id == ^user.id))

      record_query = if Enum.empty?(activity_types) do
        record_query
      else
        record_query |> Ash.Query.filter(expr(activity_type in ^activity_types))
      end

      {:ok, records} = record_query |> Ash.read()
      record_count = Enum.each(records, &Ash.destroy!/1) |> length()

      # 3. 删除奖励领取记录
      reward_query = RewardClaimRecord
      |> Ash.Query.filter(expr(user_id == ^user.id))

      reward_query = if Enum.empty?(activity_types) do
        reward_query
      else
        reward_query |> Ash.Query.filter(expr(activity_type in ^activity_types))
      end

      {:ok, rewards} = reward_query |> Ash.read()
      reward_count = Enum.each(rewards, &Ash.destroy!/1) |> length()

      # 4. 清除具体活动模块的数据（如果需要）
      specific_counts = clear_specific_activity_data(user.id, activity_types)

      total_deleted = participation_count + record_count + reward_count +
                     (specific_counts |> Map.values() |> Enum.sum())

      # 记录操作日志
      log_operation(operator, "clear_user_activity_data", %{
        user_id: user.id,
        activity_types: activity_types,
        deleted_counts: %{
          participations: participation_count,
          records: record_count,
          rewards: reward_count,
          specific_activities: specific_counts
        },
        total_deleted: total_deleted
      })

      {:ok, %{
        user_id: user.id,
        activity_types: activity_types,
        deleted_counts: %{
          participations: participation_count,
          records: record_count,
          rewards: reward_count,
          specific_activities: specific_counts
        },
        total_deleted: total_deleted
      }}
    end)
    |> case do
      {:ok, result} -> result
      {:error, reason} -> {:error, reason}
    end
  end

  defp clear_specific_activity_data(user_id, []), do: %{}
  defp clear_specific_activity_data(user_id, activity_types) do
    activity_types
    |> Enum.map(fn activity_type ->
      case Map.get(@activity_modules, activity_type) do
        nil -> {activity_type, 0}
        module ->
          # 这里需要根据具体的模块实现清除逻辑
          # 大多数活动模块可能不需要额外清除，因为主要数据在通用表中
          {activity_type, 0}
      end
    end)
    |> Enum.into(%{})
  end

  defp log_operation(operator, operation_type, data) do
    OperationLog.create(%{
      admin_user_id: operator.id,
      operation_type: operation_type,
      operation_data: data,
      ip_address: operator.ip_address || "127.0.0.1",
      user_agent: operator.user_agent || "Test Data Manager"
    })
  rescue
    e ->
      Logger.error("记录操作日志失败: #{inspect(e)}")
      :ok
  end
end
