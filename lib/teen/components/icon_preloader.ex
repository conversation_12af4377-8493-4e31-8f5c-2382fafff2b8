defmodule Teen.Components.IconPreloader do
  @moduledoc """
  图标预加载组件 - 通过在 HTML 中使用图标来强制 Tailwind 编译
  """
  use CypridinaWeb, :html

  @doc """
  预加载侧边栏中使用的所有图标
  这个组件会被包含在布局中，但不会显示，只是为了触发 Tailwind 编译
  """
  def preload_sidebar_icons(assigns) do
    ~H"""
    <!-- 隐藏的图标预加载器 - 强制 Tailwind 编译这些图标 -->
    <div style="display: none !important;" aria-hidden="true" class="hidden">
      <!-- 用户相关图标 -->
      <.icon name="hero-users" class="size-6" />
      <.icon name="hero-user-group" class="size-6" />
      <.icon name="hero-user" class="size-6" />
      <.icon name="hero-no-symbol" class="size-6" />
      
      <!-- 设备和通信图标 -->
      <.icon name="hero-device-phone-mobile" class="size-6" />
      
      <!-- 时间和活动图标 -->
      <.icon name="hero-calendar-days" class="size-6" />
      <.icon name="hero-ticket" class="size-6" />
      <.icon name="hero-gift" class="size-6" />
      <.icon name="hero-gift-top" class="size-6" />
      
      <!-- 图表和统计图标 -->
      <.icon name="hero-chart-bar-square" class="size-6" />
      <.icon name="hero-chart-pie" class="size-6" />
      
      <!-- 文档和列表图标 -->
      <.icon name="hero-clipboard-document-list" class="size-6" />
      <.icon name="hero-document-text" class="size-6" />
      <.icon name="hero-document-duplicate" class="size-6" />
      
      <!-- 操作和导航图标 -->
      <.icon name="hero-squares-plus" class="size-6" />
      <.icon name="hero-squares-2x2" class="size-6" />
      <.icon name="hero-arrow-uturn-left" class="size-6" />
      <.icon name="hero-arrow-path" class="size-6" />
      
      <!-- 通信和社交图标 -->
      <.icon name="hero-megaphone" class="size-6" />
      <.icon name="hero-hand-raised" class="size-6" />
      <.icon name="hero-link" class="size-6" />
      
      <!-- 系统和设置图标 -->
      <.icon name="hero-cog-6-tooth" class="size-6" />
      <.icon name="hero-adjustments-horizontal" class="size-6" />
      <.icon name="hero-wrench-screwdriver" class="size-6" />
      <.icon name="hero-shield-exclamation" class="size-6" />
      <.icon name="hero-key" class="size-6" />
      
      <!-- 建筑和位置图标 -->
      <.icon name="hero-building-library" class="size-6" />
      <.icon name="hero-globe-alt" class="size-6" />
      
      <!-- 商务和金融图标 -->
      <.icon name="hero-currency-dollar" class="size-6" />
      <.icon name="hero-credit-card" class="size-6" />
      <.icon name="hero-receipt-percent" class="size-6" />
      <.icon name="hero-shopping-cart" class="size-6" />
      
      <!-- 技术和服务器图标 -->
      <.icon name="hero-server" class="size-6" />
      <.icon name="hero-cpu-chip" class="size-6" />
      <.icon name="hero-cube" class="size-6" />
      
      <!-- 奖励和成就图标 -->
      <.icon name="hero-trophy" class="size-6" />
      <.icon name="hero-star" class="size-6" />
      <.icon name="hero-sparkles" class="size-6" />
      <.icon name="hero-check-circle" class="size-6" />
      
      <!-- 设计和工具图标 -->
      <.icon name="hero-swatch" class="size-6" />
    </div>
    """
  end
end
