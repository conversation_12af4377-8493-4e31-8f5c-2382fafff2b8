defmodule Teen.Jackpot.JackpotController do
  @moduledoc """
  奖池管理API控制器

  提供奖池配置的CRUD操作和管理功能
  """

  use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, :controller
  require Logger

  alias Teen.GameManagement.JackpotConfig
  alias Teen.GameSystem.JackpotManager

  @doc """
  获取游戏的奖池配置列表
  GET /api/jackpots/:game_id
  """
  def index(conn, %{"game_id" => game_id}) do
    try do
      configs = JackpotConfig.list_by_game!(game_id: game_id)

      # 获取实时余额
      configs_with_balance =
        Enum.map(configs, fn config ->
          current_balance = JackpotManager.get_jackpot_balance(config.game_id, config.jackpot_id)

          config
          |> Map.from_struct()
          |> Map.put(:real_time_balance, current_balance)
          |> Map.put(
            :is_healthy,
            current_balance >= config.min_amount && current_balance <= config.max_amount
          )
        end)

      json(conn, %{
        success: true,
        data: configs_with_balance,
        message: "获取奖池配置成功"
      })
    rescue
      error ->
        Logger.error("获取奖池配置失败: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          message: "获取奖池配置失败",
          error: Exception.message(error)
        })
    end
  end

  @doc """
  获取所有游戏的奖池配置
  GET /api/jackpots
  """
  def index_all(conn, _params) do
    try do
      configs = Ash.read!(JackpotConfig)

      # 按游戏分组
      grouped_configs = Enum.group_by(configs, & &1.game_id)

      # 获取实时余额
      result =
        Enum.into(grouped_configs, %{}, fn {game_id, game_configs} ->
          configs_with_balance =
            Enum.map(game_configs, fn config ->
              current_balance =
                JackpotManager.get_jackpot_balance(config.game_id, config.jackpot_id)

              config
              |> Map.from_struct()
              |> Map.put(:real_time_balance, current_balance)
              |> Map.put(
                :is_healthy,
                current_balance >= config.min_amount && current_balance <= config.max_amount
              )
            end)

          {game_id, configs_with_balance}
        end)

      json(conn, %{
        success: true,
        data: result,
        message: "获取所有奖池配置成功"
      })
    rescue
      error ->
        Logger.error("获取所有奖池配置失败: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          message: "获取所有奖池配置失败",
          error: Exception.message(error)
        })
    end
  end

  @doc """
  创建奖池配置
  POST /api/jackpots
  """
  def create(conn, %{"jackpot" => jackpot_params}) do
    try do
      case JackpotConfig.create(jackpot_params) do
        {:ok, config} ->
          # 初始化奖池
          if config.base_amount > 0 do
            case JackpotManager.init_single_jackpot(
                   config.game_id,
                   config.jackpot_id,
                   config.base_amount
                 ) do
              {:ok, _} ->
                Logger.info("奖池初始化成功: #{config.game_id}:#{config.jackpot_id}")

              {:error, reason} ->
                Logger.warning("奖池初始化失败: #{inspect(reason)}")
            end
          end

          json(conn, %{
            success: true,
            data: config,
            message: "创建奖池配置成功"
          })

        {:error, changeset} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(%{
            success: false,
            message: "创建奖池配置失败",
            errors: format_changeset_errors(changeset)
          })
      end
    rescue
      error ->
        Logger.error("创建奖池配置异常: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          message: "创建奖池配置异常",
          error: Exception.message(error)
        })
    end
  end

  @doc """
  更新奖池配置
  PUT /api/jackpots/:id
  """
  def update(conn, %{"id" => id, "jackpot" => jackpot_params}) do
    try do
      config = Ash.get!(JackpotConfig, id)

      case JackpotConfig.update(config, jackpot_params) do
        {:ok, updated_config} ->
          json(conn, %{
            success: true,
            data: updated_config,
            message: "更新奖池配置成功"
          })

        {:error, changeset} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(%{
            success: false,
            message: "更新奖池配置失败",
            errors: format_changeset_errors(changeset)
          })
      end
    rescue
      Ash.Error.Query.NotFound ->
        conn
        |> put_status(:not_found)
        |> json(%{
          success: false,
          message: "奖池配置不存在"
        })

      error ->
        Logger.error("更新奖池配置异常: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          message: "更新奖池配置异常",
          error: Exception.message(error)
        })
    end
  end

  @doc """
  删除奖池配置
  DELETE /api/jackpots/:id
  """
  def delete(conn, %{"id" => id}) do
    try do
      config = Ash.get!(JackpotConfig, id)

      case JackpotConfig.destroy(config) do
        :ok ->
          json(conn, %{
            success: true,
            message: "删除奖池配置成功"
          })

        {:error, changeset} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(%{
            success: false,
            message: "删除奖池配置失败",
            errors: format_changeset_errors(changeset)
          })
      end
    rescue
      Ash.Error.Query.NotFound ->
        conn
        |> put_status(:not_found)
        |> json(%{
          success: false,
          message: "奖池配置不存在"
        })

      error ->
        Logger.error("删除奖池配置异常: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          message: "删除奖池配置异常",
          error: Exception.message(error)
        })
    end
  end

  @doc """
  初始化奖池
  POST /api/jackpots/:id/initialize
  """
  def initialize(conn, %{"id" => id, "amount" => amount})
      when is_integer(amount) and amount > 0 do
    try do
      config = Ash.get!(JackpotConfig, id)

      case JackpotManager.init_single_jackpot(config.game_id, config.jackpot_id, amount) do
        {:ok, _} ->
          # 更新配置记录
          Ash.update!(config, %{}, action: :initialize_jackpot, arguments: %{amount: amount})

          json(conn, %{
            success: true,
            message: "奖池初始化成功",
            data: %{
              game_id: config.game_id,
              jackpot_id: config.jackpot_id,
              amount: amount
            }
          })

        {:error, reason} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(%{
            success: false,
            message: "奖池初始化失败",
            error: inspect(reason)
          })
      end
    rescue
      Ash.Error.Query.NotFound ->
        conn
        |> put_status(:not_found)
        |> json(%{
          success: false,
          message: "奖池配置不存在"
        })

      error ->
        Logger.error("奖池初始化异常: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          message: "奖池初始化异常",
          error: Exception.message(error)
        })
    end
  end

  @doc """
  重置奖池
  POST /api/jackpots/:id/reset
  """
  def reset(conn, %{"id" => id}) do
    try do
      config = Ash.get!(JackpotConfig, id)

      case JackpotManager.reset_jackpot(config.game_id, config.jackpot_id, config.base_amount) do
        {:ok, _} ->
          # 更新配置记录
          JackpotConfig.reset_jackpot!(config)

          json(conn, %{
            success: true,
            message: "奖池重置成功",
            data: %{
              game_id: config.game_id,
              jackpot_id: config.jackpot_id,
              base_amount: config.base_amount
            }
          })

        {:error, reason} ->
          conn
          |> put_status(:unprocessable_entity)
          |> json(%{
            success: false,
            message: "奖池重置失败",
            error: inspect(reason)
          })
      end
    rescue
      Ash.Error.Query.NotFound ->
        conn
        |> put_status(:not_found)
        |> json(%{
          success: false,
          message: "奖池配置不存在"
        })

      error ->
        Logger.error("奖池重置异常: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          message: "奖池重置异常",
          error: Exception.message(error)
        })
    end
  end

  # 私有函数

  defp format_changeset_errors(changeset) do
    Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
      Enum.reduce(opts, msg, fn {key, value}, acc ->
        String.replace(acc, "%{#{key}}", to_string(value))
      end)
    end)
  end
end
