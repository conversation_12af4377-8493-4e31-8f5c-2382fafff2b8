defmodule Teen.Controllers.RobotManagementController do
  @moduledoc """
  机器人管理API控制器
  """

  use Cy<PERSON>ridinaWeb, :controller
  require Logger

  alias Teen.RobotManagement.{RobotEntity, SimpleRobotProvider, RobotStateManager}

  # 获取机器人列表
  def index(conn, params) do
    try do
      page = String.to_integer(params["page"] || "1")
      per_page = min(String.to_integer(params["per_page"] || "20"), 100)
      status_filter = params["status"]

      robots = RobotEntity |> Ash.read!()

      # 应用状态过滤
      filtered_robots =
        if status_filter && status_filter != "all" do
          status_atom = String.to_existing_atom(status_filter)
          Enum.filter(robots, &(&1.status == status_atom))
        else
          robots
        end

      # 排序
      sorted_robots = Enum.sort_by(filtered_robots, & &1.inserted_at, {:desc, DateTime})

      # 分页
      total_count = length(sorted_robots)
      offset = (page - 1) * per_page
      paginated_robots = Enum.slice(sorted_robots, offset, per_page)

      # 构建响应
      response = %{
        robots: Enum.map(paginated_robots, &format_robot/1),
        pagination: %{
          page: page,
          per_page: per_page,
          total_count: total_count,
          total_pages: ceil(total_count / per_page)
        }
      }

      json(conn, %{success: true, data: response})
    rescue
      error ->
        Logger.error("🤖 [API] 获取机器人列表失败: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: "获取机器人列表失败"})
    end
  end

  # 获取机器人统计信息
  def stats(conn, _params) do
    try do
      stats = RobotStateManager.get_robot_status_stats()
      json(conn, %{success: true, data: stats})
    rescue
      error ->
        Logger.error("🤖 [API] 获取统计信息失败: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: "获取统计信息失败"})
    end
  end

  # 创建机器人
  def create(conn, params) do
    try do
      case SimpleRobotProvider.create_robot_manually(params) do
        {:ok, robot} ->
          Logger.info("🤖 [API] 机器人创建成功: #{robot.robot_id}")

          json(conn, %{
            success: true,
            data: format_robot(robot),
            message: "机器人创建成功"
          })

        {:error, error} ->
          Logger.error("🤖 [API] 机器人创建失败: #{inspect(error)}")

          conn
          |> put_status(:bad_request)
          |> json(%{success: false, error: "机器人创建失败: #{inspect(error)}"})
      end
    rescue
      error ->
        Logger.error("🤖 [API] 创建机器人异常: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: "创建机器人异常"})
    end
  end

  # 批量创建机器人
  def batch_create(conn, %{"count" => count, "game_type" => game_type} = _params) do
    try do
      case Integer.parse(to_string(count)) do
        {num, ""} when num > 0 and num <= 50 ->
          case SimpleRobotProvider.batch_create_robots(game_type, num) do
            {:ok, robots} ->
              Logger.info("🤖 [API] 批量创建成功: #{length(robots)} 个机器人")

              json(conn, %{
                success: true,
                data: %{
                  created_count: length(robots),
                  robots: Enum.map(robots, &format_robot/1)
                },
                message: "批量创建成功，创建了 #{length(robots)} 个机器人"
              })

            {:error, error} ->
              Logger.error("🤖 [API] 批量创建失败: #{inspect(error)}")

              conn
              |> put_status(:bad_request)
              |> json(%{success: false, error: "批量创建失败: #{inspect(error)}"})
          end

        _ ->
          conn
          |> put_status(:bad_request)
          |> json(%{success: false, error: "请输入有效的数量 (1-50)"})
      end
    rescue
      error ->
        Logger.error("🤖 [API] 批量创建异常: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: "批量创建异常"})
    end
  end

  def batch_create(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> json(%{success: false, error: "缺少必要参数: count, game_type"})
  end

  # 回收机器人
  def recycle(conn, %{"robot_ids" => robot_ids}) when is_list(robot_ids) do
    try do
      admin_id = get_admin_id(conn)

      results =
        Enum.map(robot_ids, fn robot_id ->
          case RobotStateManager.recycle_robot_by_admin(robot_id, admin_id, true) do
            {:ok, _robot} -> {:ok, robot_id}
            error -> {:error, robot_id, error}
          end
        end)

      success_count = Enum.count(results, &match?({:ok, _}, &1))
      failed_results = Enum.filter(results, &match?({:error, _, _}, &1))

      Logger.info("🤖 [API] 回收操作完成: 成功 #{success_count}, 失败 #{length(failed_results)}")

      json(conn, %{
        success: true,
        data: %{
          success_count: success_count,
          failed_count: length(failed_results),
          failed_robots: failed_results
        },
        message: "回收操作完成：成功 #{success_count} 个，失败 #{length(failed_results)} 个"
      })
    rescue
      error ->
        Logger.error("🤖 [API] 回收机器人异常: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: "回收机器人异常"})
    end
  end

  def recycle(conn, %{"robot_id" => robot_id}) do
    recycle(conn, %{"robot_ids" => [robot_id]})
  end

  def recycle(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> json(%{success: false, error: "缺少必要参数: robot_id 或 robot_ids"})
  end

  # 释放机器人
  def release(conn, %{"robot_ids" => robot_ids}) when is_list(robot_ids) do
    try do
      SimpleRobotProvider.release_robots(robot_ids)

      Logger.info("🤖 [API] 释放机器人成功: #{inspect(robot_ids)}")

      json(conn, %{
        success: true,
        data: %{released_count: length(robot_ids)},
        message: "成功释放 #{length(robot_ids)} 个机器人"
      })
    rescue
      error ->
        Logger.error("🤖 [API] 释放机器人异常: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: "释放机器人异常"})
    end
  end

  def release(conn, %{"robot_id" => robot_id}) do
    release(conn, %{"robot_ids" => [robot_id]})
  end

  def release(conn, _params) do
    conn
    |> put_status(:bad_request)
    |> json(%{success: false, error: "缺少必要参数: robot_id 或 robot_ids"})
  end

  # 清理异常状态机器人
  def cleanup(conn, _params) do
    try do
      case RobotStateManager.cleanup_stuck_robots() do
        {:ok, count} ->
          Logger.info("🤖 [API] 清理异常机器人成功: #{count} 个")

          json(conn, %{
            success: true,
            data: %{cleaned_count: count},
            message: "成功清理 #{count} 个异常状态机器人"
          })

        error ->
          Logger.error("🤖 [API] 清理异常机器人失败: #{inspect(error)}")

          conn
          |> put_status(:internal_server_error)
          |> json(%{success: false, error: "清理失败: #{inspect(error)}"})
      end
    rescue
      error ->
        Logger.error("🤖 [API] 清理异常机器人异常: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: "清理异常"})
    end
  end

  # 获取单个机器人详情
  def show(conn, %{"id" => robot_id_str}) do
    try do
      robot_id = String.to_integer(robot_id_str)

      case RobotEntity |> Ash.read(action: :by_robot_id, arguments: %{robot_id: robot_id}) do
        {:ok, [robot]} ->
          json(conn, %{
            success: true,
            data: format_robot_detail(robot)
          })

        {:ok, []} ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, error: "机器人不存在"})

        {:ok, robots} when length(robots) > 1 ->
          Logger.warning("🤖 [API] 发现重复的机器人ID: #{robot_id}")

          json(conn, %{
            success: true,
            data: format_robot_detail(List.first(robots)),
            warning: "发现重复机器人ID"
          })

        {:error, _error} ->
          conn
          |> put_status(:not_found)
          |> json(%{success: false, error: "机器人不存在"})
      end
    rescue
      ArgumentError ->
        conn
        |> put_status(:bad_request)
        |> json(%{success: false, error: "无效的机器人ID"})

      error ->
        Logger.error("🤖 [API] 获取机器人详情失败: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{success: false, error: "获取机器人详情失败"})
    end
  end

  # 私有函数

  # 格式化机器人信息（列表用）
  defp format_robot(robot) do
    %{
      id: robot.id,
      robot_id: robot.robot_id,
      nickname: robot.nickname,
      avatar_id: robot.avatar_id,
      level: robot.level,
      status: robot.status,
      current_points: robot.current_points,
      current_game_type: robot.current_game_type,
      current_room_id: robot.current_room_id,
      is_in_round: robot.is_in_round,
      can_be_kicked: robot.can_be_kicked,
      is_auto_created: robot.is_auto_created,
      status_changed_at: robot.status_changed_at,
      last_activity_at: robot.last_activity_at,
      inserted_at: robot.inserted_at
    }
  end

  # 格式化机器人详细信息
  defp format_robot_detail(robot) do
    format_robot(robot)
    |> Map.merge(%{
      min_points_threshold: robot.min_points_threshold,
      last_bet_amount: robot.last_bet_amount,
      kick_reason: robot.kick_reason,
      kicked_by: robot.kicked_by,
      robot_config: robot.robot_config,
      creator_admin_id: robot.creator_admin_id,
      tags: robot.tags,
      seat_number: robot.seat_number,
      game_joined_at: robot.game_joined_at,
      updated_at: robot.updated_at
    })
  end

  # 获取管理员ID
  defp get_admin_id(conn) do
    # 这里应该从认证信息中获取管理员ID
    # 暂时使用一个默认值
    case conn.assigns[:current_user] do
      %{id: admin_id} -> to_string(admin_id)
      _ -> "system_admin"
    end
  end
end
