defmodule Teen.CustomerService do
  @moduledoc """
  客服管理域

  包含客服聊天、用户问题、兑换订单、支付订单等功能
  """

  use Ash.Domain,
    otp_app: :cypridina,
    extensions: [AshAdmin.Domain]

  import Ash.Expr

  alias Teen.CustomerService.{CustomerChat, ExchangeOrder, SensitiveWord, VerificationCode}
  alias Teen.PaymentSystem.WithdrawalRecord
  alias Cypridina.Accounts.User

  admin do
    show? true
  end

  resources do
    resource Teen.CustomerService.CustomerChat
    resource Teen.CustomerService.UserQuestion
    resource Teen.CustomerService.ExchangeOrder
    resource Teen.CustomerService.SensitiveWord
    resource Teen.CustomerService.UserTag
  end

  # ==================== 业务逻辑函数 ====================

  @doc """
  处理客服聊天消息，包括敏感词过滤
  """
  def process_customer_chat(attrs) do
    with {:ok, filtered_question} <- filter_sensitive_words(attrs["question"]),
         attrs <- Map.put(attrs, "question", filtered_question),
         {:ok, chat} <- CustomerChat.create(attrs) do
      # 自动分配客服
      assign_customer_service(chat)
      {:ok, chat}
    end
  end

  @doc """
  过滤敏感词
  """
  def filter_sensitive_words(content) when is_binary(content) do
    case SensitiveWord.list_active_words() do
      {:ok, words} ->
        filtered_content =
          Enum.reduce(words, content, fn word, acc ->
            case word.action_type do
              1 -> String.replace(acc, word.keyword, word.replacement || "***")
              2 -> String.replace(acc, word.keyword, "***")
              # 警告但不替换
              3 -> acc
            end
          end)

        {:ok, filtered_content}

      {:error, reason} ->
        {:error, reason}
    end
  end

  def filter_sensitive_words(_), do: {:error, "Invalid content"}

  @doc """
  批量回复客服消息
  """
  def batch_reply_messages(chat_ids, reply_content) when is_list(chat_ids) do
    results =
      Enum.map(chat_ids, fn chat_id ->
        case CustomerChat.read(chat_id) do
          {:ok, chat} ->
            CustomerChat.batch_reply(chat, %{reply_content: reply_content})

          {:error, reason} ->
            {:error, {chat_id, reason}}
        end
      end)

    {successes, failures} =
      Enum.split_with(results, fn
        {:ok, _} -> true
        _ -> false
      end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  @doc """
  审核兑换订单
  """
  def audit_exchange_order(order_id, auditor_id, action, opts \\ []) do
    case ExchangeOrder.read(order_id) do
      {:ok, order} ->
        case action do
          :approve ->
            approve_exchange_order(order, auditor_id)

          :reject ->
            feedback = Keyword.get(opts, :feedback, "审核不通过")
            reject_exchange_order(order, auditor_id, feedback)

          _ ->
            {:error, "Invalid action"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  审核提现申请
  """
  def audit_withdrawal(withdrawal_id, auditor_id, action, opts \\ []) do
    case WithdrawalRecord.read(withdrawal_id) do
      {:ok, withdrawal} ->
        case action do
          :approve ->
            approve_withdrawal(withdrawal, auditor_id)

          :reject ->
            feedback = Keyword.get(opts, :feedback, "审核不通过")
            reject_withdrawal(withdrawal, auditor_id, feedback)

          _ ->
            {:error, "Invalid action"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  批量审核提现申请
  """
  def batch_audit_withdrawals(withdrawal_ids, auditor_id, action, opts \\ [])
      when is_list(withdrawal_ids) do
    results =
      Enum.map(withdrawal_ids, fn withdrawal_id ->
        audit_withdrawal(withdrawal_id, auditor_id, action, opts)
      end)

    {successes, failures} =
      Enum.split_with(results, fn
        {:ok, _} -> true
        _ -> false
      end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  @doc """
  自动审核提现申请
  """
  def auto_audit_withdrawal(withdrawal_id) do
    case WithdrawalRecord.read(withdrawal_id) do
      {:ok, withdrawal} ->
        # 检查是否符合自动审核条件
        case check_auto_audit_eligibility(withdrawal) do
          {:ok, :auto_approve} ->
            approve_withdrawal(withdrawal, nil, "系统自动审核")

          {:ok, :manual_review} ->
            {:ok, %{status: :manual_review, message: "需要人工审核"}}

          {:error, reason} ->
            reject_withdrawal(withdrawal, nil, reason, "系统自动审核")
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  批量审核兑换订单
  """
  def batch_audit_orders(order_ids, auditor_id, action, opts \\ []) when is_list(order_ids) do
    results =
      Enum.map(order_ids, fn order_id ->
        audit_exchange_order(order_id, auditor_id, action, opts)
      end)

    {successes, failures} =
      Enum.split_with(results, fn
        {:ok, _} -> true
        _ -> false
      end)

    %{
      success_count: length(successes),
      failure_count: length(failures),
      failures: failures
    }
  end

  @doc """
  计算提现手续费和税费
  """
  def calculate_withdrawal_fees(amount, payment_method, user_id \\ nil) do
    case Teen.PaymentSystem.WithdrawalConfig.list_by_payment_method(payment_method) do
      {:ok, [config | _]} ->
        Teen.Services.WithdrawalService.calculate_withdrawal_fees(amount, config, user_id)

      {:ok, []} ->
        {:error, "该支付方式暂不可用"}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  创建提现申请
  """
  def create_withdrawal_request(user_id, params) do
    Teen.Services.WithdrawalService.create_withdrawal(user_id, params)
  end

  @doc """
  获取用户提现记录
  """
  def list_user_withdrawals(user_id, opts \\ []) do
    limit = Keyword.get(opts, :limit, 20)

    case WithdrawalRecord.list_by_user(user_id) do
      {:ok, withdrawals} ->
        sorted_withdrawals = Enum.sort_by(withdrawals, & &1.inserted_at, :desc)
        limited_withdrawals = Enum.take(sorted_withdrawals, limit)
        {:ok, limited_withdrawals}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取待审核的提现申请
  """
  def list_pending_withdrawals(opts \\ []) do
    limit = Keyword.get(opts, :limit, 50)

    case WithdrawalRecord.list_pending_withdrawals() do
      {:ok, withdrawals} ->
        sorted_withdrawals = Enum.sort_by(withdrawals, & &1.inserted_at, :asc)
        limited_withdrawals = Enum.take(sorted_withdrawals, limit)
        {:ok, limited_withdrawals}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  计算兑换手续费和税费
  """
  def calculate_exchange_fees(amount, user_vip_level \\ 0) do
    # 5%基础手续费
    base_fee_rate = Decimal.new("0.05")
    # 20%税率
    tax_rate = Decimal.new("0.20")

    # VIP用户享受手续费减免
    vip_discount =
      case user_vip_level do
        # 50%减免
        level when level >= 5 -> Decimal.new("0.5")
        # 30%减免
        level when level >= 3 -> Decimal.new("0.3")
        # 10%减免
        level when level >= 1 -> Decimal.new("0.1")
        _ -> Decimal.new("0")
      end

    actual_fee_rate = Decimal.mult(base_fee_rate, Decimal.sub(Decimal.new("1"), vip_discount))
    fee_amount = Decimal.mult(amount, actual_fee_rate)
    tax_amount = Decimal.mult(amount, tax_rate)

    %{
      original_amount: amount,
      fee_rate: actual_fee_rate,
      fee_amount: fee_amount,
      tax_amount: tax_amount,
      final_amount: Decimal.sub(Decimal.sub(amount, fee_amount), tax_amount)
    }
  end

  @doc """
  发送验证码
  """
  def send_verification_code(phone_number, code_type, ip_address \\ nil) do
    params = %{phone_number: phone_number, code_type: code_type}
    params = if ip_address, do: Map.put(params, :ip_address, ip_address), else: params

    case VerificationCode.send_code(params) do
      {:ok, verification} ->
        # 这里应该调用实际的短信发送服务
        case send_sms(phone_number, verification.code) do
          :ok ->
            VerificationCode.mark_sent(verification)

          {:error, reason} ->
            VerificationCode.mark_failed(verification, %{failure_reason: reason})
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  验证验证码
  """
  def verify_code(phone_number, code) do
    case VerificationCode.list_by_phone(phone_number) do
      {:ok, codes} ->
        valid_code =
          Enum.find(codes, fn c ->
            c.code == code and c.status == 1 and
              DateTime.compare(c.expires_at, DateTime.utc_now()) == :gt
          end)

        case valid_code do
          nil ->
            {:error, "Invalid or expired verification code"}

          code_record ->
            VerificationCode.mark_used(code_record)
            {:ok, "Verification successful"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  # ==================== 私有函数 ====================

  defp assign_customer_service(chat) do
    # 简单的轮询分配策略
    case get_available_customer_service() do
      {:ok, cs_user} ->
        CustomerChat.mark_as_processed(chat, %{
          customer_service_id: cs_user.id,
          reply_content: "您好，我是客服，请问有什么可以帮助您的？"
        })

      {:error, _} ->
        # 没有可用客服，保持未处理状态
        :ok
    end
  end

  defp get_available_customer_service do
    # 这里应该查询可用的客服人员
    {:error, "No available customer service"}
  end

  defp approve_exchange_order(order, auditor_id) do
    with {:ok, user} <- User.get_by_id(order.user_id),
         :ok <- validate_exchange_eligibility(user, order),
         {:ok, updated_order} <- ExchangeOrder.approve_order(order, %{auditor_id: auditor_id}) do
      ExchangeOrder.update_progress(updated_order, %{progress_status: 0})
      notify_user_order_approved(user, updated_order)
      {:ok, updated_order}
    end
  end

  defp reject_exchange_order(order, auditor_id, feedback) do
    case ExchangeOrder.reject_order(order, %{auditor_id: auditor_id, feedback: feedback}) do
      {:ok, updated_order} ->
        notify_user_order_rejected(order.user_id, updated_order, feedback)
        {:ok, updated_order}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp validate_exchange_eligibility(user, order) do
    cond do
      user.status != 1 ->
        {:error, "用户状态异常"}

      order.exchange_amount < Decimal.new("10000") ->
        {:error, "兑换金额低于最小限额"}

      true ->
        :ok
    end
  end

  defp send_sms(_phone_number, _code) do
    # 模拟短信发送
    :ok
  end

  defp notify_user_order_approved(_user, _order), do: :ok
  defp notify_user_order_rejected(_user_id, _order, _feedback), do: :ok

  # ==================== 聊天相关函数 ====================

  @doc """
  获取最近的客服聊天记录
  """
  def list_recent_customer_chats(opts \\ []) do
    limit = Keyword.get(opts, :limit, 50)

    CustomerChat
    |> Ash.Query.new()
    |> Ash.Query.sort(inserted_at: :desc)
    |> Ash.Query.limit(limit)
    |> Ash.Query.load([:user])
    |> Ash.read!()
  end

  @doc """
  获取用户的聊天历史记录
  """
  def list_user_chat_history(user_id) do
    require Ash.Query
    import Ash.Expr

    CustomerChat
    |> Ash.Query.filter(expr(user_id == ^user_id))
    |> Ash.Query.sort(inserted_at: :asc)
    |> Ash.Query.load([:user, :customer_service])
    |> Ash.read!()
  end

  @doc """
  统计未读聊天数量
  """
  def count_unread_chats do
    require Ash.Query
    import Ash.Expr

    case CustomerChat
         |> Ash.Query.filter(expr(status == 0))
         |> Ash.read() do
      {:ok, chats} -> length(chats)
      _ -> 0
    end
  end

  @doc """
  创建客服聊天记录
  """
  def create_customer_chat(attrs) do
    CustomerChat.create(attrs)
  end

  @doc """
  更新聊天状态为已处理
  """
  def mark_chat_as_processed(chat_id, customer_service_id) do
    case CustomerChat.read(chat_id) do
      {:ok, chat} ->
        CustomerChat.update(chat, %{
          status: 1,
          customer_service_id: customer_service_id,
          processed_at: DateTime.utc_now()
        })

      error ->
        error
    end
  end

  @doc """
  获取在线客服列表
  """
  def list_online_customer_service do
    # 这里应该从 Presence 或其他状态管理获取在线客服
    # 暂时返回空列表
    []
  end

  @doc """
  检查用户是否已有活跃的客服聊天会话
  """
  def has_active_chat_session?(user_id) do
    require Ash.Query
    import Ash.Expr

    case CustomerChat
         # 未处理状态表示活跃会话
         |> Ash.Query.filter(expr(user_id == ^user_id and status == 0))
         |> Ash.Query.sort(inserted_at: :desc)
         |> Ash.Query.limit(1)
         |> Ash.read() do
      {:ok, []} -> false
      {:ok, [_chat | _]} -> true
      {:error, _} -> false
    end
  end

  @doc """
  获取用户的活跃聊天会话
  """
  def get_active_chat_session(user_id) do
    require Ash.Query
    import Ash.Expr

    CustomerChat
    # 未处理状态
    |> Ash.Query.filter(expr(user_id == ^user_id and status == 0))
    |> Ash.Query.sort(inserted_at: :desc)
    |> Ash.Query.limit(1)
    |> Ash.Query.load([:user, :customer_service])
    |> Ash.read_one()
  end

  @doc """
  创建客服聊天记录（带重复检查）
  """
  def create_customer_chat_with_check(attrs) do
    user_id = attrs[:user_id] || attrs["user_id"]

    # 检查是否已有活跃会话
    if has_active_chat_session?(user_id) do
      case get_active_chat_session(user_id) do
        {:ok, existing_chat} ->
          {:error, {:already_exists, existing_chat}}

        {:error, _} ->
          # 如果获取失败，仍然尝试创建新会话
          create_customer_chat(attrs)
      end
    else
      create_customer_chat(attrs)
    end
  end

  # ==================== 提现审核相关私有函数 ====================

  @doc """
  通过提现申请
  """
  defp approve_withdrawal(withdrawal, auditor_id, reason \\ "审核通过") do
    with {:ok, user} <- User.get_by_id(withdrawal.user_id),
         :ok <- validate_withdrawal_approval(user, withdrawal),
         {:ok, updated_withdrawal} <-
           WithdrawalRecord.approve_withdrawal(withdrawal, %{auditor_id: auditor_id}) do
      # 启动提现处理流程
      case Teen.Services.WithdrawalService.process_withdrawal(updated_withdrawal.id) do
        {:ok, _result} ->
          notify_user_withdrawal_approved(user, updated_withdrawal)
          {:ok, updated_withdrawal}

        {:error, reason} ->
          # 如果处理失败，更新状态为处理失败
          WithdrawalRecord.update_progress(updated_withdrawal, %{
            # 支付失败
            progress_status: 3,
            feedback: "支付处理失败: #{reason}"
          })

          {:error, "审核通过但支付处理失败: #{reason}"}
      end
    end
  end

  @doc """
  拒绝提现申请
  """
  defp reject_withdrawal(withdrawal, auditor_id, feedback, reason \\ "人工审核") do
    case WithdrawalRecord.reject_withdrawal(withdrawal, %{
           auditor_id: auditor_id,
           feedback: feedback
         }) do
      {:ok, updated_withdrawal} ->
        notify_user_withdrawal_rejected(withdrawal.user_id, updated_withdrawal, feedback)
        {:ok, updated_withdrawal}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  检查自动审核资格
  """
  defp check_auto_audit_eligibility(withdrawal) do
    with {:ok, user} <- User.get_by_id(withdrawal.user_id),
         {:ok, config} <- get_withdrawal_config_for_method(withdrawal.payment_method) do
      cond do
        # 用户状态异常
        user.status != 1 ->
          {:error, "用户状态异常"}

        # 超过自动审核限额
        config.auto_approve_limit &&
            Decimal.compare(withdrawal.withdrawal_amount, config.auto_approve_limit) == :gt ->
          {:ok, :manual_review}

        # 用户VIP等级不足
        config.vip_level_required &&
            (user.vip_level || 0) < config.vip_level_required ->
          {:ok, :manual_review}

        # 检查用户是否有风险标记
        has_risk_flags?(user) ->
          {:ok, :manual_review}

        # 检查提现频率
        withdrawal_frequency_too_high?(user.id) ->
          {:ok, :manual_review}

        # 通过所有检查，可以自动审核
        true ->
          {:ok, :auto_approve}
      end
    end
  end

  @doc """
  验证提现审核条件
  """
  defp validate_withdrawal_approval(user, withdrawal) do
    cond do
      user.status != 1 ->
        {:error, "用户状态异常"}

      withdrawal.audit_status != 0 ->
        {:error, "提现申请已被审核"}

      withdrawal.withdrawal_amount < Decimal.new("10000") ->
        {:error, "提现金额低于最小限额"}

      true ->
        :ok
    end
  end

  @doc """
  获取支付方式对应的提现配置
  """
  defp get_withdrawal_config_for_method(payment_method) do
    case Teen.PaymentSystem.WithdrawalConfig.list_by_payment_method(payment_method) do
      {:ok, [config | _]} -> {:ok, config}
      {:ok, []} -> {:error, "该支付方式暂不可用"}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  检查用户是否有风险标记
  """
  defp has_risk_flags?(user) do
    # 这里可以检查用户的风险标记
    # 比如：频繁提现、异常登录、投诉记录等
    false
  end

  @doc """
  检查提现频率是否过高
  """
  defp withdrawal_frequency_too_high?(user_id) do
    # 检查用户最近24小时内的提现次数
    # 如果超过3次，需要人工审核
    case WithdrawalRecord.list_by_user(user_id) do
      {:ok, withdrawals} ->
        recent_withdrawals =
          Enum.filter(withdrawals, fn w ->
            DateTime.diff(DateTime.utc_now(), w.inserted_at, :hour) <= 24
          end)

        length(recent_withdrawals) >= 3

      {:error, _} ->
        false
    end
  end

  # 通知函数
  defp notify_user_withdrawal_approved(_user, _withdrawal), do: :ok
  defp notify_user_withdrawal_rejected(_user_id, _withdrawal, _feedback), do: :ok
end
