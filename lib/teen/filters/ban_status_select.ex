defmodule Teen.Filters.BanStatusSelect do
  @moduledoc """
  封禁状态筛选器

  用于筛选封禁记录的状态
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "封禁状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择封禁状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"封禁中", 1},
      {"已解封", 0}
    ]
  end
end
