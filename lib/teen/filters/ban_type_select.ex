defmodule Teen.Filters.BanTypeSelect do
  @moduledoc """
  封禁类型筛选器

  用于筛选不同类型的封禁记录
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "封禁类型"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择封禁类型..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"账号封禁", 1},
      {"设备封禁", 2},
      {"IP封禁", 3},
      {"支付封禁", 4}
    ]
  end
end
