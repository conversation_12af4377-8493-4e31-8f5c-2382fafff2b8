defmodule Teen.Filters.BooleanSelect do
  @moduledoc """
  布尔值筛选器

  用于筛选是/否类型的字段
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "是否默认"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"是", true},
      {"否", false}
    ]
  end
end
