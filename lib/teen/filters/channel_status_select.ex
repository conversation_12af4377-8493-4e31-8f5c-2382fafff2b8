defmodule Teen.Filters.ChannelStatusSelect do
  @moduledoc """
  渠道状态筛选器

  用于筛选渠道的启用/禁用状态
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"启用", 1},
      {"禁用", 0}
    ]
  end
end
