defmodule Teen.Filters.DeliveryStatusSelect do
  @moduledoc """
  发放状态筛选器

  用于筛选商品的发放状态
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "发放状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择发放状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"待发放", :pending},
      {"已发放", :delivered},
      {"发放失败", :failed},
      {"已取消", :cancelled}
    ]
  end
end
