defmodule Teen.Filters.GatewaySelect do
  @moduledoc """
  支付网关筛选器

  用于筛选不同的支付网关
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "支付网关"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择支付网关..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    # 动态获取所有启用的支付网关
    case Teen.PaymentSystem.PaymentGateway.list_enabled() do
      {:ok, gateways} ->
        gateway_options =
          gateways
          |> Enum.map(fn gateway -> {gateway.name, gateway.id} end)

        [{"全部", nil}] ++ gateway_options

      _ ->
        [{"全部", nil}]
    end
  end
end
