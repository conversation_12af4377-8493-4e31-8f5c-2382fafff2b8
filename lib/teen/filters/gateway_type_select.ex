defmodule Teen.Filters.GatewayTypeSelect do
  @moduledoc """
  支付网关类型筛选器

  用于筛选不同类型的支付网关
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "网关类型"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择网关类型..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"第三方支付", "third_party"},
      {"银行直连", "bank_direct"},
      {"数字货币", "crypto"},
      {"电子钱包", "e_wallet"}
    ]
  end
end
