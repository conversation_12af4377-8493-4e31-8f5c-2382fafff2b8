defmodule Teen.Filters.PaymentChannelSelect do
  @moduledoc """
  支付渠道筛选器

  用于筛选不同的支付渠道
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "支付渠道"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择支付渠道..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    # 这里可以动态获取支付渠道列表
    # 暂时使用静态列表
    [
      {"全部", nil},
      {"支付宝", "alipay"},
      {"微信支付", "wechat"},
      {"银联支付", "unionpay"},
      {"网银支付", "ebank"},
      {"第三方支付", "3021"}
    ]
  end
end
