defmodule Teen.Filters.ProductStatusSelect do
  @moduledoc """
  商品状态筛选器

  用于筛选商品的上架/下架状态
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"上架", :active},
      {"下架", :inactive},
      {"售罄", :sold_out}
    ]
  end
end
