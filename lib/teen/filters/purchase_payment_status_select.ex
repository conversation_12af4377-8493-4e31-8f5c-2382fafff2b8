defmodule Teen.Filters.PurchasePaymentStatusSelect do
  @moduledoc """
  购买记录支付状态筛选器

  用于筛选购买记录的支付状态
  """

  use Backpex.Filters.Select

  @impl Backpex.Filter
  def label, do: "支付状态"

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filters.Select
  def prompt, do: "选择支付状态..."

  @impl Backpex.Filters.Select
  def options(_assigns) do
    [
      {"全部", nil},
      {"待支付", :pending},
      {"已支付", :completed},
      {"支付失败", :failed},
      {"已退款", :refunded}
    ]
  end
end
