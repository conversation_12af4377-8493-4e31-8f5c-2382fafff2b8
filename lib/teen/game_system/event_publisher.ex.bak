defmodule Cypridina.Teen.GameSystem.EventPublisher do
  @moduledoc """
  游戏事件发布器 - 统一处理游戏相关事件的异步发布

  功能：
  - 游戏完成事件发布
  - 异步任务进度更新
  - VIP经验值计算
  - 统一的事件处理机制
  """

  use GenServer
  require Logger

  alias Teen.ActivitySystem.ActivityTaskService
  alias Teen.GameManagement.GameRecord

  # 客户端API

  @doc """
  启动事件发布器
  """
  def start_link(_opts) do
    GenServer.start_link(__MODULE__, %{}, name: __MODULE__)
  end

  @doc """
  发布游戏完成事件
  """
  def publish_game_completed(player_id, game_round_id, game_result, opts \\ []) do
    event_data = %{
      player_id: player_id,
      game_round_id: game_round_id,
      game_result: game_result,
      game_type: Keyword.get(opts, :game_type, :unknown),
      room_id: Keyword.get(opts, :room_id),
      winner_id: Keyword.get(opts, :winner_id),
      is_win: Keyword.get(opts, :is_win, false),
      timestamp: DateTime.utc_now()
    }

    GenServer.cast(__MODULE__, {:game_completed, event_data})
  end

  @doc """
  批量发布游戏完成事件（用于多人游戏）
  """
  def publish_batch_game_completed(events) when is_list(events) do
    GenServer.cast(__MODULE__, {:batch_game_completed, events})
  end

  # GenServer 回调

  @impl true
  def init(state) do
    Logger.info("🎯 [EVENT_PUBLISHER] 事件发布器启动")
    {:ok, state}
  end

  @impl true
  def handle_cast({:game_completed, event_data}, state) do
    Task.start(fn -> process_game_completed_event(event_data) end)
    {:noreply, state}
  end

  @impl true
  def handle_cast({:batch_game_completed, events}, state) do
    Task.start(fn -> process_batch_game_completed_events(events) end)
    {:noreply, state}
  end

  # 私有函数

  @doc """
  处理单个游戏完成事件
  """
  defp process_game_completed_event(event_data) do
    %{
      player_id: player_id,  # 这是 user_id (UUID)
      game_round_id: game_round_id,
      game_result: game_result,
      game_type: game_type,
      room_id: room_id,
      is_win: is_win
    } = event_data

    Logger.info("🎯 [EVENT_PUBLISHER] 处理游戏完成事件: 用户#{player_id}, 游戏#{game_type}")

    try do
      # 1. 更新游戏局数任务进度
      update_game_round_tasks(player_id, game_round_id, game_type, room_id)

      # 2. 更新胜利次数任务进度
      if is_win do
        update_win_round_tasks(player_id, game_round_id, game_type, room_id)
      end

      # 3. 更新VIP经验值（根据下注金额）
      update_vip_experience(player_id, game_result)

      # 4. 创建游戏记录
      create_game_record(event_data)

      # 5. 更新其他活动任务进度
      update_activity_tasks(player_id, event_data)

      Logger.info("🎯 [EVENT_PUBLISHER] 游戏事件处理完成: 用户#{player_id}")
    rescue
      error ->
        Logger.error("🎯 [EVENT_PUBLISHER] 处理游戏事件失败: #{inspect(error)}")
        Logger.error("🎯 [EVENT_PUBLISHER] 事件数据: #{inspect(event_data)}")
    end
  end

  @doc """
  批量处理游戏完成事件
  """
  defp process_batch_game_completed_events(events) do
    Logger.info("🎯 [EVENT_PUBLISHER] 批量处理游戏完成事件: #{length(events)} 个事件")

    Enum.each(events, fn event_data ->
      process_game_completed_event(event_data)
    end)
  end

  @doc """
  更新游戏局数任务进度
  """
  defp update_game_round_tasks(player_id, game_round_id, game_type, room_id) do
    ActivityTaskService.update_task_progress(
      player_id,
      :game_completed,
      %{
        game_id: game_round_id,
        room_id: room_id,
        game_type: game_type
      }
    )
  end

  @doc """
  更新胜利次数任务进度
  """
  defp update_win_round_tasks(player_id, game_round_id, game_type, room_id) do
    ActivityTaskService.update_task_progress(
      player_id,
      :game_won,
      %{
        game_id: game_round_id,
        room_id: room_id,
        game_type: game_type
      }
    )
  end

  @doc """
  更新VIP经验值
  """
  defp update_vip_experience(player_id, game_result) do
    bet_amount = Map.get(game_result, :bet_amount, 0)

    if bet_amount > 0 do
      Teen.VipSystem.UserVipInfo.add_experience_from_source(player_id, :game_bet, bet_amount)
      Logger.debug("🎯 [EVENT_PUBLISHER] VIP经验更新: 用户#{player_id}, 下注#{bet_amount}")
    end
  end

  @doc """
  更新其他活动任务进度
  """
  defp update_activity_tasks(player_id, event_data) do
    %{
      game_result: game_result,
      is_win: is_win,
      game_round_id: game_round_id
    } = event_data

    # 根据游戏结果更新不同的任务进度
    if is_win do
      win_amount = Map.get(game_result, :win_amount, 0)
      bet_amount = Map.get(game_result, :bet_amount, 0)

      ActivityTaskService.update_task_progress(
        player_id,
        :game_won,
        %{
          game_id: game_round_id,
          win_amount: win_amount,
          bet_amount: bet_amount
        }
      )
    else
      loss_amount = Map.get(game_result, :loss_amount, 0)
      bet_amount = Map.get(game_result, :bet_amount, 0)

      ActivityTaskService.update_task_progress(
        player_id,
        :game_lost,
        %{
          game_id: game_round_id,
          loss_amount: loss_amount,
          bet_amount: bet_amount
        }
      )
    end
  end

  @doc """
  创建游戏记录
  """
  defp create_game_record(event_data) do
    %{
      player_id: player_id,
      game_round_id: game_round_id,
      game_result: game_result,
      game_type: game_type,
      is_win: is_win
    } = event_data

    # 提取游戏结果数据
    bet_amount = Map.get(game_result, :bet_amount, Decimal.new(0))
    win_amount = Map.get(game_result, :win_amount, Decimal.new(0))
    
    # 确定游戏结果状态
    result_status = cond do
      is_win == true -> :win
      bet_amount > 0 and win_amount == 0 -> :lose
      true -> :draw
    end

    # 计算净输赢（用于损失返水计算）
    net_amount = Decimal.sub(win_amount, bet_amount)

    # 准备游戏数据
    game_data = Map.merge(game_result, %{
      net_amount: net_amount,
      room_id: Map.get(event_data, :room_id),
      round: Map.get(event_data, :round)
    })

    # 创建游戏记录
    case GameRecord.create(%{
      user_id: player_id,  # 现在 player_id 已经是 user_id (UUID)
      game_id: game_round_id,
      game_type: to_string(game_type),
      bet_amount: bet_amount,
      win_amount: win_amount,
      result_status: result_status,
      game_data: game_data
    }) do
      {:ok, record} ->
        Logger.info("🎯 [EVENT_PUBLISHER] 游戏记录创建成功: 用户#{player_id}, 游戏#{game_round_id}")
        
        # 如果是输钱，触发损失返水更新
        if result_status == :lose and bet_amount > 0 do
          Logger.info("🎯 [EVENT_PUBLISHER] 用户#{player_id}输钱#{bet_amount}，触发损失返水更新")
          # 损失返水将通过活动任务系统处理
        end
        
        {:ok, record}
        
      {:error, reason} ->
        Logger.error("🎯 [EVENT_PUBLISHER] 创建游戏记录失败: #{inspect(reason)}")
        {:error, reason}
    end
  end
end
