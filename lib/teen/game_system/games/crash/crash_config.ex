defmodule Cypridina.Teen.GameSystem.Games.Crash.CrashConfig do
  @moduledoc """
  Crash游戏配置管理模块

  管理游戏的各种配置参数，包括：
  - 时间配置
  - 倍数配置
  - 筹码配置
  - 控制参数
  """

  @doc """
  获取默认配置
  """
  def get_default_config do
    %{
      # 时间配置（秒）
      # 等待时间
      free_time: 5,
      # 下注时间
      betting_time: 15,
      # 结算时间
      settling_time: 5,
      # 最大飞行时间（毫秒）
      max_fly_time: 110_460,

      # 筹码配置（分）
      chips: [500, 1000, 5000, 10000, 50000, 500_000],
      # 最小下注（分）
      min_bet: 500,
      # 下注必须是此数的倍数
      bet_multiple: 100,

      # 玩家配置
      # 最大玩家数
      max_players: 100,
      # 最小玩家数
      min_players: 1,

      # 税率配置
      # 税率 5% (50/1000)
      revenue_ratio: 50,

      # 机器人配置
      # 最大机器人数
      max_robots: 60,
      # 机器人下注概率
      robot_bet_probability: 0.3,

      # 倍数配置 - 基于C++配置文件格式
      multiplier_configs: generate_multiplier_configs()
    }
  end

  @doc """
  获取指定房间的配置
  """
  def get_room_config(_room_id) do
    # TODO: 根据房间ID获取特定配置
    # 目前返回默认配置
    get_default_config()
  end

  @doc """
  获取游戏配置参数
  """
  def get_game_params(config) do
    %{
      free_time: Map.get(config, :free_time, 5),
      betting_time: Map.get(config, :betting_time, 15),
      settling_time: Map.get(config, :settling_time, 5),
      max_fly_time: Map.get(config, :max_fly_time, 110_460),
      min_bet: Map.get(config, :min_bet, 500),
      bet_multiple: Map.get(config, :bet_multiple, 100),
      revenue_ratio: Map.get(config, :revenue_ratio, 50),
      max_players: Map.get(config, :max_players, 100),
      min_players: Map.get(config, :min_players, 1),
      max_robots: Map.get(config, :max_robots, 60),
      robot_bet_probability: Map.get(config, :robot_bet_probability, 0.3)
    }
  end

  @doc """
  获取合并后的配置
  """
  def get_merged_config(custom_config \\ %{}) do
    Map.merge(get_default_config(), custom_config)
  end

  @doc """
  生成倍数配置
  基于C++代码中的配置文件格式
  """
  def generate_multiplier_configs do
    # 基础配置：前100个点位，每20ms增加1个点
    base_configs =
      1..100
      |> Enum.map(fn id ->
        %{
          id: id,
          # 从1.00x开始
          multiplier: 100 + id - 1,
          # 每20ms一个点
          time: (id - 1) * 20
        }
      end)

    # 扩展配置：更高倍数的配置
    extended_configs = [
      # 1.10x
      %{id: 101, multiplier: 110, time: 2100},
      # 1.20x
      %{id: 102, multiplier: 120, time: 2400},
      # 1.30x
      %{id: 103, multiplier: 130, time: 2800},
      # 1.40x
      %{id: 104, multiplier: 140, time: 3200},
      # 1.50x
      %{id: 105, multiplier: 150, time: 3600},
      # 1.60x
      %{id: 106, multiplier: 160, time: 4000},
      # 1.70x
      %{id: 107, multiplier: 170, time: 4500},
      # 1.80x
      %{id: 108, multiplier: 180, time: 5000},
      # 1.90x
      %{id: 109, multiplier: 190, time: 5500},
      # 2.00x
      %{id: 110, multiplier: 200, time: 6000},
      # 2.20x
      %{id: 111, multiplier: 220, time: 7000},
      # 2.50x
      %{id: 112, multiplier: 250, time: 8000},
      # 3.00x
      %{id: 113, multiplier: 300, time: 10000},
      # 3.50x
      %{id: 114, multiplier: 350, time: 12000},
      # 4.00x
      %{id: 115, multiplier: 400, time: 15000},
      # 5.00x
      %{id: 116, multiplier: 500, time: 20000},
      # 6.00x
      %{id: 117, multiplier: 600, time: 25000},
      # 7.00x
      %{id: 118, multiplier: 700, time: 30000},
      # 8.00x
      %{id: 119, multiplier: 800, time: 35000},
      # 9.00x
      %{id: 120, multiplier: 900, time: 40000},
      # 10.00x
      %{id: 121, multiplier: 1000, time: 45000},
      # 12.00x
      %{id: 122, multiplier: 1200, time: 50000},
      # 15.00x
      %{id: 123, multiplier: 1500, time: 55000},
      # 20.00x
      %{id: 124, multiplier: 2000, time: 60000},
      # 25.00x
      %{id: 125, multiplier: 2500, time: 65000},
      # 30.00x
      %{id: 126, multiplier: 3000, time: 70000},
      # 40.00x
      %{id: 127, multiplier: 4000, time: 75000},
      # 50.00x
      %{id: 128, multiplier: 5000, time: 80000},
      # 75.00x
      %{id: 129, multiplier: 7500, time: 90000},
      # 100.00x
      %{id: 130, multiplier: 10000, time: 100_000},
      # 150.00x
      %{id: 131, multiplier: 15000, time: 105_000},
      # 200.00x
      %{id: 132, multiplier: 20000, time: 108_000},
      # 300.00x (接近最大时间)
      %{id: 133, multiplier: 30000, time: 110_000}
    ]

    base_configs ++ extended_configs
  end

  @doc """
  获取筹码配置
  """
  def get_chip_config(config \\ nil) do
    config = config || get_default_config()
    Map.get(config, :chips, [500, 1000, 5000, 10000, 50000, 500_000])
  end

  @doc """
  获取时间配置
  """
  def get_time_config(config \\ nil) do
    config = config || get_default_config()

    %{
      free_time: Map.get(config, :free_time, 5),
      betting_time: Map.get(config, :betting_time, 15),
      settling_time: Map.get(config, :settling_time, 5),
      max_fly_time: Map.get(config, :max_fly_time, 110_460)
    }
  end

  @doc """
  获取下注配置
  """
  def get_bet_config(config \\ nil) do
    config = config || get_default_config()

    %{
      min_bet: Map.get(config, :min_bet, 500),
      bet_multiple: Map.get(config, :bet_multiple, 100),
      revenue_ratio: Map.get(config, :revenue_ratio, 50)
    }
  end

  @doc """
  获取机器人配置
  """
  def get_robot_config(config \\ nil) do
    config = config || get_default_config()

    %{
      max_robots: Map.get(config, :max_robots, 60),
      robot_bet_probability: Map.get(config, :robot_bet_probability, 0.3)
    }
  end

  @doc """
  验证配置有效性
  """
  def validate_config(config) do
    errors = []

    # 验证时间配置
    errors =
      if Map.get(config, :free_time, 0) < 1 do
        ["free_time must be at least 1 second" | errors]
      else
        errors
      end

    errors =
      if Map.get(config, :betting_time, 0) < 5 do
        ["betting_time must be at least 5 seconds" | errors]
      else
        errors
      end

    # 验证下注配置
    errors =
      if Map.get(config, :min_bet, 0) < 100 do
        ["min_bet must be at least 100" | errors]
      else
        errors
      end

    errors =
      if Map.get(config, :bet_multiple, 0) < 1 do
        ["bet_multiple must be at least 1" | errors]
      else
        errors
      end

    # 验证玩家配置
    errors =
      if Map.get(config, :max_players, 0) < 1 do
        ["max_players must be at least 1" | errors]
      else
        errors
      end

    errors =
      if Map.get(config, :min_players, 0) < 1 do
        ["min_players must be at least 1" | errors]
      else
        errors
      end

    case errors do
      [] -> {:ok, config}
      _ -> {:error, errors}
    end
  end

  @doc """
  根据时间获取倍数点（对应C++的GetRewardPoint）
  """
  def get_multiplier_point_by_time(time_ms, config \\ nil) do
    config = config || get_default_config()
    multiplier_configs = Map.get(config, :multiplier_configs, [])

    # 找到对应时间的倍数点（每20毫秒一个点）
    point_index =
      Enum.find_index(multiplier_configs, fn point ->
        time_ms <= point.time
      end)

    point_index || max(0, length(multiplier_configs) - 1)
  end

  @doc """
  根据倍数点获取倍数（对应C++的GetMultiByRewardPoint）
  """
  def get_multiplier_by_point(point, config \\ nil) do
    config = config || get_default_config()
    multiplier_configs = Map.get(config, :multiplier_configs, [])

    case Enum.at(multiplier_configs, point) do
      # 默认1.00倍
      nil -> 100
      multiplier_point -> multiplier_point.multiplier
    end
  end

  @doc """
  根据倍数获取时间（对应C++中倍数到时间的转换）
  """
  def get_time_by_multiplier(multiplier, config \\ nil) do
    config = config || get_default_config()
    multiplier_configs = Map.get(config, :multiplier_configs, [])

    # 找到对应倍数的时间
    case Enum.find(multiplier_configs, fn point ->
           point.multiplier >= multiplier
         end) do
      nil -> Map.get(config, :max_fly_time, 110_460)
      point -> point.time
    end
  end

  @doc """
  根据倍数获取对应的点索引（对应C++中的倍数查找逻辑）
  """
  def get_point_by_multiplier(multiplier, config \\ nil) do
    config = config || get_default_config()
    multiplier_configs = Map.get(config, :multiplier_configs, [])

    # 找到对应倍数的点索引
    point_index =
      Enum.find_index(multiplier_configs, fn point ->
        point.multiplier >= multiplier
      end)

    point_index || max(0, length(multiplier_configs) - 1)
  end

  @doc """
  验证下注金额是否有效
  """
  def valid_bet_amount?(bet_amount, config \\ nil) do
    config = config || get_default_config()
    min_bet = Map.get(config, :min_bet, 500)
    bet_multiple = Map.get(config, :bet_multiple, 100)

    bet_amount >= min_bet and rem(bet_amount, bet_multiple) == 0
  end

  @doc """
  从配置文件加载配置（基于C++的ReadConfigInformation）
  """
  def load_from_file(file_path) do
    if File.exists?(file_path) do
      try do
        content = File.read!(file_path)
        parse_config_file(content)
      rescue
        error ->
          {:error, "Failed to load config file: #{inspect(error)}"}
      end
    else
      {:error, "Config file not found: #{file_path}"}
    end
  end

  @doc """
  保存配置到文件
  """
  def save_to_file(config, file_path) do
    try do
      content = format_config_file(config)
      File.write!(file_path, content)
      {:ok, file_path}
    rescue
      error ->
        {:error, "Failed to save config file: #{inspect(error)}"}
    end
  end

  @doc """
  获取环境变量配置
  """
  def get_env_config do
    %{
      free_time: get_env_int("CRASH_FREE_TIME", 5),
      betting_time: get_env_int("CRASH_BETTING_TIME", 15),
      settling_time: get_env_int("CRASH_SETTLING_TIME", 5),
      min_bet: get_env_int("CRASH_MIN_BET", 500),
      max_players: get_env_int("CRASH_MAX_PLAYERS", 100),
      revenue_ratio: get_env_int("CRASH_REVENUE_RATIO", 50)
    }
  end

  # ==================== 私有函数 ====================

  # 解析配置文件内容（基于C++的INI文件格式）
  defp parse_config_file(content) do
    try do
      config = get_default_config()

      # 解析INI格式的配置文件
      parsed_config =
        content
        |> String.split("\n")
        |> Enum.reduce(config, &parse_config_line/2)

      {:ok, parsed_config}
    rescue
      error ->
        {:error, "Failed to parse config: #{inspect(error)}"}
    end
  end

  # 解析单行配置
  defp parse_config_line(line, config) do
    line = String.trim(line)

    # 跳过空行和注释行
    if line == "" or String.starts_with?(line, "#") or String.starts_with?(line, ";") do
      config
    else
      case String.split(line, "=", parts: 2) do
        [key, value] ->
          key = String.trim(key)
          value = String.trim(value)
          parse_config_value(config, key, value)

        _ ->
          config
      end
    end
  end

  # 解析配置值
  defp parse_config_value(config, key, value) do
    case key do
      "free_time" -> Map.put(config, :free_time, parse_int(value, 5))
      "betting_time" -> Map.put(config, :betting_time, parse_int(value, 15))
      "settling_time" -> Map.put(config, :settling_time, parse_int(value, 5))
      "min_bet" -> Map.put(config, :min_bet, parse_int(value, 500))
      "max_players" -> Map.put(config, :max_players, parse_int(value, 100))
      "max_robots" -> Map.put(config, :max_robots, parse_int(value, 60))
      "revenue_ratio" -> Map.put(config, :revenue_ratio, parse_int(value, 50))
      _ -> config
    end
  end

  # 格式化配置文件内容
  defp format_config_file(config) do
    """
    # Crash游戏配置文件
    # 基于C++项目的配置格式

    [Config]
    free_time=#{Map.get(config, :free_time, 5)}
    betting_time=#{Map.get(config, :betting_time, 15)}
    settling_time=#{Map.get(config, :settling_time, 5)}
    min_bet=#{Map.get(config, :min_bet, 500)}
    max_players=#{Map.get(config, :max_players, 100)}
    max_robots=#{Map.get(config, :max_robots, 60)}
    revenue_ratio=#{Map.get(config, :revenue_ratio, 50)}

    [Chips]
    chip0=500
    chip1=1000
    chip2=5000
    chip3=10000
    chip4=50000
    chip5=500000

    [DrawPoint]
    # 格式：ID=倍数,时间(单位：20毫秒)
    """ <> format_multiplier_configs(Map.get(config, :multiplier_configs, []))
  end

  # 格式化倍数配置
  defp format_multiplier_configs(multiplier_configs) do
    multiplier_configs
    |> Enum.map(fn %{id: id, multiplier: multiplier, time: time} ->
      # 转换为20毫秒单位
      time_units = div(time, 20)
      "#{id}=#{multiplier},#{time_units}"
    end)
    |> Enum.join("\n")
  end

  # 解析整数值
  defp parse_int(value, default) do
    case Integer.parse(value) do
      {int_value, _} -> int_value
      :error -> default
    end
  end

  defp get_env_int(key, default) do
    case System.get_env(key) do
      nil ->
        default

      value ->
        case Integer.parse(value) do
          {int_value, _} -> int_value
          :error -> default
        end
    end
  end
end
