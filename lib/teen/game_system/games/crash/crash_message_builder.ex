defmodule <PERSON><PERSON>rid<PERSON>.Teen.GameSystem.Games.Crash.CrashMessageBuilder do
  @moduledoc """
  Crash游戏消息构建器

  统一处理所有客户端协议消息的构建，减少重复代码
  完全符合TpMasterClient前端框架的协议格式
  """

  alias Cy<PERSON>ridina.Teen.GameSystem.Games.Crash.{CrashGame, CrashLogic}
  require Logger

  # 主协议ID - 使用5作为游戏协议主ID（与Teen Patti保持一致）
  @main_id 5

  # 协议子ID定义 - 严格按照C++版本，保持兼容性
  @protocol_ids %{
    # 服务端发送协议（对应C++的SUB_S_*）
    # 游戏空闲状态（SUB_S_GAME_FREE）
    game_free: 1001,
    # 游戏开始（SUB_S_GAME_START）
    game_start: 1002,
    # 下注成功（SUB_S_PLACE_JETTON）- 实时广播给所有玩家
    place_bet_success: 1003,
    # 下车成功（SUB_S_ROCK_DOWN）
    cash_out_success: 1004,
    # 游戏结束（SUB_S_GAME_END）
    game_end: 1005,
    # 游戏记录（SUB_S_SEND_RECORD）
    game_record: 1006,
    #  @error_codes
    game_error: 1007,
    # 时间通知（SUB_S_TIME_NOTIFY）
    time_notify: 1008,
    # 开始飞行（SUB_S_FLY_START）
    fly_start: 1009,
    # 玩家数量变化（SUB_S_PLAYER_COUNT）
    player_count: 1010,

    # 客户端发送协议（对应C++的SUB_C_*）
    # 客户端下注（SUB_C_PLACE_JETTON）
    client_place_bet: 1000,
    # 客户端下车（SUB_C_ROCK_DOWN）
    client_cash_out: 1020
  }

  @doc """
  构建基础协议消息结构
  """
  def build_message(sub_id, data, opts \\ []) do
    base_message = %{
      "mainId" => @main_id,
      "subId" => sub_id,
      "data" => data
    }

    if opts[:with_timestamp] do
      Map.put(base_message, "timestamp", DateTime.utc_now() |> DateTime.to_unix(:millisecond))
    else
      base_message
    end
  end

  @doc """
  构建游戏空闲状态消息（对应C++的CMD_S_FreeState）
  """
  def build_game_free(state) do
    config = state.game_data.config
    time_leave = Map.get(config, :free_time, 5)

    data = %{
      # 剩余时间（秒）
      "cbTimeLeave" => time_leave
    }

    build_message(@protocol_ids.game_free, data)
  end

  @doc """
  构建游戏开始消息（对应C++的CMD_S_GameStart）
  """
  def build_game_start(state) do
    config = state.game_data.config
    time_leave = Map.get(config, :betting_time, 15)

    data = %{
      # 下注剩余时间（秒）
      "cbTimeLeave" => time_leave
    }

    build_message(@protocol_ids.game_start, data)
  end

  # 注意：游戏状态通过 game_free/game_start/game_end 等协议传递，无需单独的 game_status 协议

  @doc """
  构建游戏配置消息（兼容测试）
  """
  def build_game_config(state) do
    config = state.game_data.config

    data = %{
      "chips" => Map.get(config, :chips, [500, 1000, 5000, 10000, 50000, 500_000]),
      "minBet" => Map.get(config, :min_bet, 500),
      "maxBet" => Map.get(config, :max_bet, 1_000_000),
      "betMultiple" => Map.get(config, :bet_multiple, 100),
      "revenueRatio" => Map.get(config, :revenue_ratio, 50),
      "timeLimit" => %{
        "betting" => Map.get(config, :betting_time, 15),
        "flying" => Map.get(config, :max_fly_time, 110_460),
        "settling" => Map.get(config, :settling_time, 5)
      },
      "roundId" => Map.get(state.game_data, :round, 1)
    }

    # 使用 game_free 协议发送配置信息
    build_message(@protocol_ids.game_free, data)
  end

  @doc """
  构建游戏状态消息 (类似LongHu的game_state)
  """
  def build_game_state(state) do
    data = %{
      "state" => get_game_state_code(state.game_data.phase),
      "roundId" => Map.get(state.game_data, :round, 1),
      "timeLeft" => get_time_left(state),
      "currentMultiplier" => get_current_multiplier(state),
      "totalPlayers" => map_size(state.players),
      "totalBets" => Map.get(state.game_data, :total_bets, 0)
    }

    # 根据游戏阶段选择合适的协议
    protocol_id =
      case state.game_data.phase do
        :waiting -> @protocol_ids.game_free
        :betting -> @protocol_ids.game_start
        :flying -> @protocol_ids.fly_start
        :settling -> @protocol_ids.game_end
        _ -> @protocol_ids.game_free
      end
      Logger.info("🎰 [protocol_id————--#{protocol_id}")

    build_message(protocol_id, data)
  end

  @doc """
  构建下注成功消息（对应C++的CMD_S_PlaceJetton）
  """
  def build_place_bet_success(player, bet_amount, left_score) do
    data = %{
      # 用户位置
      "wChairID" => player.numeric_id,
      # 下注数目
      "lBetScore" => bet_amount,
      # 剩余金币
      "lLeftScore" => left_score
    }

    build_message(@protocol_ids.place_bet_success, data)
  end

  @doc """
  构建下注成功消息（兼容4参数版本）
  """
  def build_place_bet_success(player, bet_amount, current_money, total_bets) do
    data = %{
      # 用户位置
      "wChairID" => player.numeric_id,
      # 下注数目
      "lBetScore" => bet_amount,
      # 剩余金币
      "lLeftScore" => current_money,
      # 总下注额
      "lTotalBets" => total_bets
    }

    build_message(@protocol_ids.place_bet_success, data)
  end

  @doc """
  构建下注失败消息（对应C++的CMD_S_PlaceBetFail）
  """
  def build_place_bet_fail(error_code) do
    data = %{
      # 错误码：1=不在下注阶段 2=余额不足 3=已经下注
      "cbCode" => error_code
    }

    build_message(@protocol_ids.game_error, data)
  end

  @doc """
  构建下车成功消息（对应C++的CMD_S_RockDown）
  """
  def build_cash_out_success(player, down_time, down_point, score, left_score) do
    data = %{
      # 用户位置
      "wChairID" => player.numeric_id,
      # 下车时间
      "nDownTime" => down_time,
      # 下车点数
      "nDownPoint" => down_point,
      # 下车金额
      "lScore" => score,
      # 剩余金币
      "lLeftScore" => left_score
    }

    build_message(@protocol_ids.cash_out_success, data)
  end

  @doc """
  构建飞行开始消息（对应C++的SUB_S_FLY_START）
  """
  def build_fly_start(state) do
    data = %{
      "nStartTime" =>
        Map.get(state.game_data, :flying_start_time, System.system_time(:millisecond))
    }

    build_message(@protocol_ids.fly_start, data)
  end

  @doc """
  构建游戏结束消息（对应C++的CMD_S_GameEnd）
  """
  def build_game_end(explode_time, explode_point, time_leave) do
    data = %{
      # 爆炸时间
      "nExplodeTime" => explode_time,
      # 爆炸点数
      "nExplodePoint" => explode_point,
      # 剩余时间
      "cbTimeLeave" => time_leave
    }

    build_message(@protocol_ids.game_end, data)
  end

  @doc """
  构建时间通知消息（对应C++的CMD_S_TimeNotify）
  """
  def build_time_notify(start_time) do
    data = %{
      # 游戏开始时间（时间戳）
      "nStartTime" => start_time
    }

    build_message(@protocol_ids.time_notify, data)
  end

  # 注意：下注同步通过 place_bet_success 协议实现，错误处理通过 game_error 协议实现

  # 注意：倍数更新通过 fly_start 协议实现，无需单独的 multiplier_update 协议

  @doc """
  构建下车成功消息
  """
  def build_cash_out_success(player, cash_out_multiplier, payout, current_money) do
    data = %{
      "code" => 1,
      "playerId" => player.numeric_id,
      "cashOutMultiplier" => cash_out_multiplier,
      "payout" => payout,
      "currentMoney" => current_money,
      "timestamp" => System.system_time(:millisecond)
    }

    build_message(@protocol_ids.cash_out_success, data)
  end

  @doc """
  构建游戏结束消息 (类似LongHu的settlement) - 重载版本
  """
  def build_game_settlement(state, crash_multiplier, player_results) do
    data = %{
      "roundId" => Map.get(state.game_data, :round, 1),
      "crashMultiplier" => crash_multiplier,
      "crashTime" => Map.get(state.game_data, :crash_time, 0),
      "playerResults" => player_results,
      "nextRoundIn" => Map.get(state.game_data.config, :settling_time, 5),
      "timestamp" => System.system_time(:millisecond)
    }

    build_message(@protocol_ids.game_end, data)
  end

  @doc """
  构建游戏记录消息
  """
  def build_game_record(records) do
    data = %{
      "records" =>
        Enum.map(records, fn record ->
          %{
            "roundId" => record.round_id,
            "crashMultiplier" => record.crash_multiplier,
            "timestamp" => record.timestamp,
            "playerCount" => record.player_count
          }
        end)
    }

    build_message(@protocol_ids.game_record, data)
  end

  @doc """
  构建玩家数量变化消息（SUB_S_PLAYER_COUNT）
  """
  def build_player_count_change(total_players) do
    data = %{
      "totalplayernum" => total_players
    }

    build_message(@protocol_ids.player_count, data)
  end

  # 注意：玩家列表通过 place_bet_success 和 cash_out_success 协议实现，无需单独的 player_list 协议

  # ==================== 私有辅助函数 ====================

  # 计算剩余时间
  defp calculate_time_leave(state) do
    case state.game_data.phase do
      :waiting -> Map.get(state.game_data.config, :free_time, 5)
      :betting -> Map.get(state.game_data.config, :betting_time, 15)
      :settling -> Map.get(state.game_data.config, :settling_time, 5)
      # 飞行阶段没有固定时间限制
      :flying -> 0
      _ -> 0
    end
  end

  # 获取玩家下注分数数组（对应C++的lBetScore[GAME_PLAYER]）
  defp get_player_bet_scores(state) do
    # 创建一个固定大小的数组，索引对应玩家位置
    # 最多100个玩家位置
    bet_scores = List.duplicate(0, 100)

    state.players
    |> Enum.reduce(bet_scores, fn {_user_id, player}, acc ->
      chair_id = get_player_chair_id(player)
      bet_amount = Map.get(player, :bet_amount, 0)
      List.replace_at(acc, chair_id, bet_amount)
    end)
  end

  # 获取玩家下车点数数组（对应C++的wDownPoint[GAME_PLAYER]）
  defp get_player_down_points(state) do
    # 创建一个固定大小的数组，索引对应玩家位置
    # 最多100个玩家位置
    down_points = List.duplicate(0, 100)

    state.players
    |> Enum.reduce(down_points, fn {_user_id, player}, acc ->
      chair_id = get_player_chair_id(player)

      down_point =
        if Map.get(player, :cashed_out, false) do
          Map.get(player, :cash_out_multiplier, 0)
        else
          0
        end

      List.replace_at(acc, chair_id, down_point)
    end)
  end

  # 获取倍数历史记录
  defp get_multiplier_history(state) do
    Map.get(state.game_data, :multiplier_history, [])
    # 最近10次记录
    |> Enum.take(-10)
  end

  # 获取玩家座位ID
  defp get_player_chair_id(player) do
    # 使用玩家ID的模运算来分配座位
    rem(abs(player.numeric_id), 100)
  end

  defp get_time_left(state) do
    case state.game_data.phase do
      :waiting -> Map.get(state.game_data, :free_time_left, 0)
      :betting -> Map.get(state.game_data, :betting_time_left, 0)
      :flying -> Map.get(state.game_data, :max_fly_time, 110_460)
      :settling -> Map.get(state.game_data, :settling_time_left, 0)
      _ -> 0
    end
  end

  defp get_game_state_code(phase) do
    case phase do
      :waiting -> 0
      :betting -> 1
      :flying -> 2
      :settling -> 3
      _ -> 0
    end
  end

  defp get_current_multiplier(state) do
    case state.game_data.phase do
      :flying ->
        current_time = System.system_time(:millisecond)
        flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
        elapsed_time = current_time - flying_start_time
        config = state.game_data.config
        CrashLogic.get_current_multiplier(elapsed_time, config)

      # 1.00x
      _ ->
        100
    end
  end

  @doc """
  获取协议ID映射
  """
  def protocol_ids, do: @protocol_ids

  @doc """
  获取客户端协议ID
  """
  def client_protocols do
    %{
      place_bet: @protocol_ids.client_place_bet,
      cash_out: @protocol_ids.client_cash_out
    }
  end
end
