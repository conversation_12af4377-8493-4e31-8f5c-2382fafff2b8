
defmodule <PERSON>prid<PERSON>.Teen.GameSystem.Games.Crash.CrashRoom do
  @moduledoc """
  Crash游戏房间实现

  ## 游戏流程
  1. **等待阶段** - 房间等待玩家加入
  2. **下注阶段** - 玩家下注
  3. **飞行阶段** - 倍数上升，玩家可以下车
  4. **结算阶段** - 计算输赢，准备下一轮

  ## 游戏规则
  - Crash是一个百人场游戏
  - 玩家在下注阶段下注
  - 飞行阶段倍数不断上升，玩家可以随时下车获得收益
  - 游戏会在随机时刻爆炸，未下车的玩家失去下注金额
  """

  use Cypridina.Teen.GameSystem.RoomBase, game_type: :crash

  alias Cypridina.Teen.GameSystem.Games.Crash.{
    CrashGame,
    CrashLogic,
    CrashMessageBuilder,
    CrashAI
  }

  require Logger

  # ==================== 工具宏定义 ====================

  defmacro log_info(tag, message, opts \\ []) do
    quote do
      Logger.info("🎰 [CRASH_#{unquote(tag)}] #{unquote(message)}", unquote(opts))
    end
  end

  defmacro log_error(tag, message, opts \\ []) do
    quote do
      Logger.error("🎰 [CRASH_#{unquote(tag)}] #{unquote(message)}", unquote(opts))
    end
  end

  # ==================== RoomBehaviour 回调函数实现 ====================

  @doc """
  获取最小玩家数 - Crash是百人场游戏，最小1人即可开始
  """
  @impl true
  def min_players, do: 1

  @doc """
  获取最大玩家数 - Crash是百人场游戏，支持大量玩家
  """
  @impl true
  def max_players, do: 100

  @doc """
  判断游戏是否结束 - Crash是持续运行的百人场游戏，不会结束
  """
  @impl true
  def game_over?(_state), do: false

  @doc """
  获取游戏胜者 - Crash每局都有胜负，但不是传统意义的玩家对战
  """
  @impl true
  def get_winner(state) do
    case state.game_data.phase do
      :settling ->
        # 在结算阶段，可以返回本局的爆炸倍数
        case Map.get(state.game_data, :crash_multiplier) do
          nil -> :none
          multiplier -> {:ok, "#{multiplier / 100}x"}
        end

      _ ->
        :none
    end
  end

  # ==================== 1. 初始化阶段 ====================

  @doc """
  初始化游戏逻辑
  """
  @impl true
  def init_game_logic(state) do
    unified_config = CrashGame.full_config(state.config)

    state
    |> build_initial_game_state(unified_config)
    |> start_game_loop()
  end

  @doc """
  开始游戏循环
  """
  @impl true
  def on_game_start(state) do
    log_info("GAME_START", "开始新一轮游戏", room: state.id, round: state.game_data.round + 1)

    # 重置游戏数据
    game_data = %{
      state.game_data
      | phase: :waiting,
        round: state.game_data.round + 1,
        total_bets: 0,
        betting_players: [],
        crash_time: nil,
        crash_multiplier: nil,
        crashed: false,
        start_time: System.system_time(:millisecond),
        phase_timer: nil
    }

    # 重置玩家状态
    reset_players =
      state.players
      |> Enum.map(fn {user_id, player} ->
        reset_player =
          Map.merge(player, %{
            bet_amount: 0,
            cashed_out: false,
            cash_out_multiplier: nil,
            crashed: false,
            payout: 0,
            profit: 0
          })

        {user_id, reset_player}
      end)
      |> Enum.into(%{})

    # 启动游戏tick
    schedule_game_tick()

    updated_state = %{state | game_data: game_data, players: reset_players}

    # 启动等待阶段
    updated_state
    |> start_waiting_phase()
  end

  # ==================== 2. 玩家管理 ====================

  @doc """
  玩家加入房间
  """
  @impl true
  def on_player_joined(state, player) do
    log_info("PLAYER_JOIN", "玩家加入百人场", player_id: player.numeric_id)

    state
    |> add_player_to_state(player)
    |> send_initial_data_to_player(player)
    |> broadcast_player_count_change()
    |> check_game_start()
  end

  @doc """
  玩家离开房间
  """
  @impl true
  def on_player_left(state, player) do
    log_info("PLAYER_LEAVE", "玩家离开百人场", player_id: player.numeric_id)

    state
    |> remove_player_from_state(player)
    |> broadcast_player_count_change()
  end

  @doc """
  玩家重连
  """
  @impl true
  def on_player_rejoined(state, player) do
    log_info("PLAYER_REJOIN", "玩家重连", player_id: player.numeric_id)

    state
    |> send_initial_data_to_player(player)
  end

  # ==================== 3. 消息处理 ====================

  @doc """
  处理游戏消息
  """
  @impl true
  def handle_game_message(state, player, message) do
    client_protocols = CrashMessageBuilder.client_protocols()

    case message do
      %{"mainId" => 5, "subId" => subId} when subId == client_protocols.place_bet ->
        # 玩家下注
        handle_place_bet(state, player, message)

      %{"mainId" => 5, "subId" => subId} when subId == client_protocols.cash_out ->
        # 玩家下车
        handle_cash_out(state, player, message)

      _ ->
        log_info("UNKNOWN_MESSAGE", "未知消息", message: message)
        state
    end
  end

  # ==================== 4. 游戏阶段处理 ====================

  # ==================== 私有函数 ====================

  defp build_initial_game_state(state, config) do
    game_data = %{
      phase: :waiting,
      round: 0,
      config: config,
      total_bets: 0,
      betting_players: [],
      crash_time: nil,
      crash_multiplier: nil,
      crashed: false,
      start_time: nil,
      betting_time_left: Map.get(config, :betting_time, 15),
      flying_start_time: nil,
      multiplier_history: [],
      # 添加阶段定时器管理
      phase_timer: nil
    }

    %{state | game_data: game_data}
  end

  defp start_game_loop(state) do
    # 百人场游戏自动开始，无需等待玩家数量
    log_info("GAME_LOOP", "启动游戏循环")

    # 直接开始第一轮游戏
    on_game_start(state)
  end

  defp start_waiting_phase(state) do
    config = state.game_data.config
    free_time = Map.get(config, :free_time, 5)

    log_info("WAITING_PHASE", "开始等待阶段", time: free_time)

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 设置新的阶段定时器
    timer = Process.send_after(self(), :phase_timeout, free_time * 1000)

    # 更新游戏状态
    updated_game_data = %{state.game_data | phase: :waiting, phase_timer: timer}
    updated_state = %{state | game_data: updated_game_data}

    # 发送游戏状态消息
    message = CrashMessageBuilder.build_game_state(updated_state)
    broadcast_to_all(updated_state, message)

    updated_state
  end

  defp start_betting_phase(state) do
    config = state.game_data.config
    betting_time = Map.get(config, :betting_time, 15)

    log_info("BETTING_PHASE", "开始下注阶段", time: betting_time)

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 设置新的阶段定时器
    timer = Process.send_after(self(), :phase_timeout, betting_time * 1000)

    # 更新游戏状态
    updated_game_data =
      Map.merge(state.game_data, %{
        phase: :betting,
        betting_time_left: betting_time,
        phase_timer: timer
      })

    updated_state = %{state | game_data: updated_game_data}

    # 发送游戏状态消息
    message = CrashMessageBuilder.build_game_state(updated_state)
    broadcast_to_all(updated_state, message)

    updated_state
  end

  defp start_flying_phase(state) do
    log_info("FLYING_PHASE", "开始飞行阶段")

    # 取消之前的计时器
    if state.game_data.phase_timer do
      Process.cancel_timer(state.game_data.phase_timer)
    end

    # 获取下注玩家信息
    betting_players = get_betting_players(state)

    # 计算爆炸点（倍数和时间）
    {crash_multiplier, crash_time} = CrashLogic.calculate_crash_point(state, betting_players)

    # 更新游戏状态（飞行阶段不设置固定定时器，由爆炸逻辑控制）
    updated_game_data =
      Map.merge(state.game_data, %{
        phase: :flying,
        flying_start_time: System.system_time(:millisecond),
        crash_time: crash_time,
        crash_multiplier: crash_multiplier,
        phase_timer: nil
      })

    updated_state = %{state | game_data: updated_game_data}

    # 发送飞行开始消息
    fly_message = CrashMessageBuilder.build_fly_start(updated_state)
    broadcast_to_all(updated_state, fly_message)

    # 启动游戏tick来处理飞行阶段
    schedule_game_tick()

    updated_state
  end

  defp handle_betting_tick(state) do
    # 处理机器人下注行为
    state_with_robot_bets = CrashAI.handle_robot_betting_phase(state)

    # 更新下注倒计时
    time_left = Map.get(state_with_robot_bets.game_data, :betting_time_left, 0) - 1

    if time_left <= 0 do
      # 下注时间结束，进入飞行阶段
      start_flying_phase(state_with_robot_bets)
    else
      updated_game_data = Map.put(state_with_robot_bets.game_data, :betting_time_left, time_left)
      %{state_with_robot_bets | game_data: updated_game_data}
    end
  end

  defp handle_flying_tick(state) do
    current_time = System.system_time(:millisecond)
    flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
    elapsed_time = current_time - flying_start_time

    # 检查是否应该爆炸
    if CrashLogic.should_crash?(state, elapsed_time) do
      handle_crash(state, elapsed_time)
    else
      # 获取当前倍数
      config = state.game_data.config
      current_multiplier = CrashLogic.get_current_multiplier(elapsed_time, config)

      # 处理机器人下车行为
      state_with_robot_cashouts =
        CrashAI.handle_robot_flying_phase(state, current_multiplier, elapsed_time)

      # 只在倍数有显著变化时才发送更新（减少协议发送频率）
      last_sent_multiplier = Map.get(state.game_data, :last_sent_multiplier, 100)
      multiplier_diff = abs(current_multiplier - last_sent_multiplier)

      # 每当倍数变化超过5个点（0.05x）或每秒发送一次更新
      last_update_time = Map.get(state.game_data, :last_update_time, 0)
      should_send_update = multiplier_diff >= 5 or (current_time - last_update_time) >= 500

      updated_state = if should_send_update do
        # 发送倍数更新消息（使用时间通知协议而不是飞行开始协议）
        time_data = %{
          "multiplier" => current_multiplier / 100.0,
          "time" => elapsed_time / 1000.0,
          "timestamp" => current_time
        }

        # 使用时间通知协议（1008）发送倍数更新
        message = CrashMessageBuilder.build_message(
          CrashMessageBuilder.protocol_ids().time_notify,
          time_data
        )

        broadcast_to_all(state_with_robot_cashouts, message)

        # 更新最后发送的倍数和时间
        updated_game_data = Map.merge(state_with_robot_cashouts.game_data, %{
          last_sent_multiplier: current_multiplier,
          last_update_time: current_time
        })
        %{state_with_robot_cashouts | game_data: updated_game_data}
      else
        state_with_robot_cashouts
      end

      updated_state
    end
  end

  defp handle_settling_tick(state) do
    # 结算阶段处理 - 由定时器控制，这里不需要额外处理
    state
  end

  defp handle_crash(state, crash_time) do
    log_info("CRASH", "游戏爆炸", crash_time: crash_time)

    # 处理爆炸逻辑
    {:ok, updated_state} = CrashLogic.handle_crash(state, crash_time)

    # 修复玩家结果数据格式
    player_results =
      Enum.map(updated_state.players, fn {user_id, player} ->
        # 直接从玩家数据中获取金币，避免使用PlayerData.get_points
        player_coin =
          if player.is_robot do
            # 机器人使用balance字段
            Map.get(player, :balance, 1000000)
          else
            # 真实玩家从账户系统获取
            Cypridina.Accounts.get_user_points(player.user_id)
          end

        %{
          "playerid" => player.numeric_id,  # 修正字段名
          "bet_amount" => Map.get(player, :bet_amount, 0),
          "cash_out_multiplier" => Map.get(player, :cash_out_multiplier, nil),
          "win_amount" => Map.get(player, :payout, 0),  # 修正字段名
          "coin" => player_coin,  # 直接使用获取的金币值
          "is_crashed" => not Map.get(player, :cashed_out, false)  # 修正字段名和逻辑
        }
      end)

    # 发送游戏结束消息
    crash_multiplier = Map.get(updated_state.game_data, :crash_multiplier, 100)
    message = CrashMessageBuilder.build_game_end(updated_state, crash_multiplier, player_results)
    broadcast_to_all(updated_state, message)

    # 记录历史
    updated_game_data =
      Map.update(updated_state.game_data, :multiplier_history, [], fn history ->
        new_record = %{
          round_id: Map.get(updated_state.game_data, :round, 1),
          crash_multiplier: crash_multiplier,
          timestamp: System.system_time(:millisecond),
          player_count: map_size(updated_state.players)
        }
        # 保留最近20条
        [new_record | history] |> Enum.take(20)
      end)

    # 转换到结算阶段
    transition_to_settling(%{updated_state | game_data: updated_game_data})
  end

  defp transition_to_settling(state) do
    config = state.game_data.config
    settling_time = Map.get(config, :settling_time, 5)

    log_info("SETTLING_PHASE", "开始结算阶段", time: settling_time)

    # 设置结算阶段定时器
    timer = Process.send_after(self(), :phase_timeout, settling_time * 1000)

    # 更新游戏状态
    updated_game_data = %{state.game_data | phase: :settling, phase_timer: timer}
    updated_state = %{state | game_data: updated_game_data}

    # 发送结算状态消息
    message = CrashMessageBuilder.build_game_state(updated_state)
    broadcast_to_all(updated_state, message)

    updated_state
  end

  # ==================== 5. 消息处理函数 ====================

  defp handle_place_bet(state, player, message) do
    bet_amount = get_in(message, ["data", "betAmount"]) || 0

    case CrashLogic.place_bet(state, player, bet_amount) do
      {:ok, updated_state, updated_player} ->
        log_info("PLACE_BET", "玩家下注成功",
          player_id: player.numeric_id,
          amount: bet_amount
        )

        # 获取玩家当前金币
        current_money = get_player_points(updated_state, updated_player.user_id)
        total_bets = Map.get(updated_state.game_data, :total_bets, 0)

        # 发送下注成功消息给玩家
        success_message =
          CrashMessageBuilder.build_place_bet_success(
            updated_player,
            bet_amount,
            current_money - bet_amount,
            total_bets
          )

        send_to_player(updated_state, updated_player, success_message)

        # 发送下注成功消息给其他玩家（实现下注同步）
        sync_message =
          CrashMessageBuilder.build_place_bet_success(
            updated_player,
            bet_amount,
            current_money,
            updated_state.game_data.total_bets
          )

        broadcast_to_all_except(updated_state, sync_message, updated_player.user_id)

        # 扣除玩家积分
        subtract_player_points(updated_state, updated_player.user_id, bet_amount)

        updated_state

      {:error, error_code} ->
        log_info("PLACE_BET_FAIL", "玩家下注失败",
          player_id: player.numeric_id,
          amount: bet_amount,
          error: error_code
        )

        # 发送下注失败消息
        fail_message = CrashMessageBuilder.build_place_bet_fail(error_code)
        send_to_player(state, player, fail_message)

        state
    end
  end

  defp handle_cash_out(state, player, _message) do
    current_time = System.system_time(:millisecond)
    flying_start_time = Map.get(state.game_data, :flying_start_time, current_time)
    elapsed_time = current_time - flying_start_time

    case CrashLogic.cash_out(state, player, elapsed_time) do
      {:ok, updated_state, updated_player, payout_info} ->
        log_info("CASH_OUT", "玩家下车成功",
          player_id: player.numeric_id,
          multiplier: updated_player.cash_out_multiplier,
          payout: payout_info.gross_payout
        )

        # 获取玩家当前金币
        current_money = get_player_points(updated_state, updated_player.user_id)

        # 发送下车成功消息
        cash_out_message =
          CrashMessageBuilder.build_cash_out_success(
            updated_player,
            updated_player.cash_out_multiplier,
            payout_info.gross_payout,
            current_money + payout_info.net_payout
          )

        broadcast_to_all(updated_state, cash_out_message)

        # 增加玩家积分
        add_player_points(updated_state, updated_player.user_id, payout_info.net_payout)

        updated_state

      {:error, error_code} ->
        log_info("CASH_OUT_FAIL", "玩家下车失败",
          player_id: player.numeric_id,
          error: error_code
        )

        # 发送下车失败消息（使用下注失败协议，错误码不同）
        fail_message = CrashMessageBuilder.build_place_bet_fail(error_code)
        send_to_player(state, player, fail_message)

        state
    end
  end

  # ==================== 6. 辅助函数 ====================

  defp add_player_to_state(state, player) do
    # 初始化玩家游戏数据
    game_player =
      Map.merge(player, %{
        bet_amount: 0,
        cashed_out: false,
        cash_out_multiplier: nil,
        crashed: false,
        payout: 0,
        profit: 0
      })

    %{state | players: Map.put(state.players, player.user_id, game_player)}
  end

  defp remove_player_from_state(state, player) do
    %{state | players: Map.delete(state.players, player.user_id)}
  end

  defp send_initial_data_to_player(state, player) do
    # 发送游戏配置
    config_message = CrashMessageBuilder.build_game_config(state)
    send_to_player(state, player, config_message)

    # 发送游戏状态
    state_message = CrashMessageBuilder.build_game_state(state)
    send_to_player(state, player, state_message)

    # 发送游戏记录（包含历史记录）
    history = Map.get(state.game_data, :multiplier_history, [])

    if length(history) > 0 do
      history_message = CrashMessageBuilder.build_game_record(history)
      send_to_player(state, player, history_message)
    end

    state
  end

  defp broadcast_player_count_change(state) do
    total_players = map_size(state.players)
    log_info("BROADCAST_PLAYER_COUNT", "广播玩家数量变化", total: total_players)

    message = CrashMessageBuilder.build_player_count_change(total_players)
    broadcast_to_room(state, message)

    state
  end

  defp check_game_start(state) do
    player_count = map_size(state.players)
    min_players = Map.get(state.game_data.config, :min_players, 1)

    # 如果玩家不足，添加机器人
    state_with_robots =
      if player_count < 5 do
        CrashAI.add_robots_to_room(state, 5 - player_count)
      else
        state
      end

    updated_player_count = map_size(state_with_robots.players)

    if state_with_robots.room_state == @room_states.waiting and
         updated_player_count >= min_players do
      log_info("ROOM", "房间满足开始条件", room: state_with_robots.id, players: updated_player_count)
      start_game(state_with_robots)
    else
      state_with_robots
    end
  end

  defp broadcast_to_all(state, message) do
    Enum.each(state.players, fn {_user_id, player} ->
      send_to_player(state, player, message)
    end)
  end

  defp broadcast_to_all_except(state, message, exclude_user_id) do
    Enum.each(state.players, fn {user_id, player} ->
      if user_id != exclude_user_id do
        send_to_player(state, player, message)
      end
    end)
  end

  # 注意：send_to_player 函数已经由 RoomBase 提供

  defp schedule_game_tick() do
    # 每100ms一次tick
    Process.send_after(self(), :game_tick, 100)
  end

  defp get_betting_players(state) do
    state.players
    |> Enum.filter(fn {_user_id, player} ->
      Map.get(player, :bet_amount, 0) > 0
    end)
    |> Enum.map(fn {_user_id, player} -> player end)
  end

  # ==================== 7. GenServer消息处理 ====================

  # 处理阶段超时
  def handle_info(:phase_timeout, state) do
    log_info("PHASE_TIMEOUT", "阶段超时触发",
      phase: state.game_data.phase,
      round: state.game_data.round
    )

    new_state =
      case state.game_data.phase do
        :waiting ->
          log_info("PHASE_TIMEOUT", "等待阶段超时，转换到下注阶段")
          start_betting_phase(state)

        :betting ->
          log_info("PHASE_TIMEOUT", "下注阶段超时，转换到飞行阶段")
          start_flying_phase(state)

        :settling ->
          log_info("PHASE_TIMEOUT", "结算阶段超时，开始新一轮")
          start_new_round(state)

        _ ->
          log_info("PHASE_TIMEOUT", "未知阶段超时", phase: state.game_data.phase)
          state
      end

    {:noreply, new_state}
  end

  # 处理开始新一轮消息
  def handle_info(:start_new_round, state) do
    log_info("START_NEW_ROUND", "开始新一轮游戏")
    new_state = start_new_round(state)
    {:noreply, new_state}
  end

  # 处理游戏tick
  def handle_info(:game_tick, state) do
    # 处理当前阶段的tick逻辑
    new_state = handle_game_tick(state)
    {:noreply, new_state}
  end

  # 游戏tick处理逻辑
  @impl true
  def handle_game_tick(state) do
    # 处理当前阶段的tick
    new_state = case state.game_data.phase do
      :betting ->
        handle_betting_tick(state)

      :flying ->
        handle_flying_tick(state)

      :settling ->
        handle_settling_tick(state)

      _ ->
        state
    end

    # 继续调度下一次tick（只有在飞行阶段需要持续tick）
    if new_state.game_data.phase == :flying do
      schedule_game_tick()
    end

    new_state
  end

  # 开始新一轮游戏
  defp start_new_round(state) do
    # 百人场游戏自动循环，直接开始新一轮
    on_game_start(state)
  end

  # 注意：handle_info 函数已经由 RoomBase 提供，这里只需要实现特定的回调
end
