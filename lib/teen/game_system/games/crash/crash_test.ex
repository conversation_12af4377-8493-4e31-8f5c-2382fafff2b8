defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Crash.CrashTest do
  @moduledoc """
  Crash游戏测试模块

  用于测试Crash游戏的各种功能
  """

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Crash.{
    CrashG<PERSON>,
    CrashLogic,
    Crash<PERSON><PERSON>age<PERSON><PERSON><PERSON>,
    CrashA<PERSON>,
    CrashConfig
  }

  require Logger

  @doc """
  测试核心功能实现
  """
  def test_core_implementation do
    Logger.info("🧪 [CRASH_TEST] 开始测试核心功能实现")

    # 测试爆炸点计算
    test_crash_point_calculation()

    # 测试库存控制
    test_stock_control_system()

    # 测试玩家下注和下车
    test_player_betting_and_cashout()

    # 测试机器人行为
    test_robot_behavior()

    Logger.info("🧪 [CRASH_TEST] 核心功能测试完成")
  end

  @doc """
  测试爆炸点计算算法
  """
  def test_crash_point_calculation do
    Logger.info("🧪 [CRASH_TEST] 测试爆炸点计算算法")

    config = CrashConfig.get_default_config()
    state = create_test_state(config)

    # 测试无玩家下注情况
    Logger.info("🧪 [CRASH_TEST] 测试无玩家下注情况")

    for _i <- 1..10 do
      {multiplier, crash_time} = CrashLogic.calculate_crash_point(state, [])
      Logger.info("🧪 [CRASH_TEST] 无玩家下注 - 倍数: #{multiplier / 100}x, 时间: #{crash_time}ms")
    end

    # 测试有玩家下注情况
    Logger.info("🧪 [CRASH_TEST] 测试有玩家下注情况")
    betting_player = create_test_player(1001, false)

    for _i <- 1..5 do
      {multiplier, crash_time} = CrashLogic.calculate_crash_point(state, [betting_player])
      Logger.info("🧪 [CRASH_TEST] 有玩家下注 - 倍数: #{multiplier / 100}x, 时间: #{crash_time}ms")
    end
  end

  @doc """
  测试库存控制系统
  """
  def test_stock_control_system do
    Logger.info("🧪 [CRASH_TEST] === 测试库存控制系统 ===")

    # 测试不同房间的库存状态
    test_rooms = [
      {"crash_room_1", "安全状态"},
      {"crash_room_2", "高库存状态"},
      {"crash_room_3", "低库存状态"}
    ]

    Enum.each(test_rooms, fn {room_id, description} ->
      Logger.info("🧪 [CRASH_TEST] 测试房间: #{room_id} (#{description})")

      # 创建测试状态
      state = %{id: room_id, game_data: %{config: CrashGame.default_config()}}

      # 测试爆炸点计算
      betting_players = [
        create_test_player(1001, false, 10000),
        create_test_player(1002, false, 20000)
      ]

      {multiplier, crash_time} = CrashLogic.calculate_crash_point(state, betting_players)
      Logger.info("🧪 [CRASH_TEST] #{description} - 倍数: #{multiplier / 100}x, 时间: #{crash_time}ms")
    end)

    Logger.info("🧪 [CRASH_TEST] 库存控制系统测试完成")
    :ok
  end

  @doc """
  测试玩家下注和下车
  """
  def test_player_betting_and_cashout do
    Logger.info("🧪 [CRASH_TEST] 测试玩家下注和下车")

    config = CrashConfig.get_default_config()
    state = create_test_state(config)
    player = create_test_player(1001, false)

    # 测试下注
    Logger.info("🧪 [CRASH_TEST] 测试玩家下注")
    betting_state = %{state | game_data: Map.put(state.game_data, :phase, :betting)}

    case CrashLogic.place_bet(betting_state, player, 5000) do
      {:ok, updated_state, updated_player} ->
        Logger.info(
          "🧪 [CRASH_TEST] 下注成功 - 玩家: #{updated_player.nickname}, 金额: #{updated_player.bet_amount}"
        )

        # 测试下车
        Logger.info("🧪 [CRASH_TEST] 测试玩家下车")

        flying_state = %{
          updated_state
          | game_data: Map.put(updated_state.game_data, :phase, :flying)
        }

        case CrashLogic.cash_out(flying_state, updated_player, 2000) do
          {:ok, _final_state, final_player, payout_info} ->
            Logger.info(
              "🧪 [CRASH_TEST] 下车成功 - 玩家: #{final_player.nickname}, 倍数: #{final_player.cash_out_multiplier / 100}x, 收益: #{payout_info.gross_payout}"
            )

          {:error, error_code} ->
            Logger.error("🧪 [CRASH_TEST] 下车失败 - 错误码: #{error_code}")
        end

      {:error, error_code} ->
        Logger.error("🧪 [CRASH_TEST] 下注失败 - 错误码: #{error_code}")
    end
  end

  @doc """
  测试机器人行为
  """
  def test_robot_behavior do
    Logger.info("🧪 [CRASH_TEST] 测试机器人行为")

    config = CrashConfig.get_default_config()
    state = create_test_state(config)

    # 添加机器人
    state_with_robots = CrashAI.add_robots_to_room(state, 3)
    robot_count = count_robots(state_with_robots)
    Logger.info("🧪 [CRASH_TEST] 添加了 #{robot_count} 个机器人")

    # 测试机器人下注
    betting_state = %{
      state_with_robots
      | game_data: Map.put(state_with_robots.game_data, :phase, :betting)
    }

    state_after_betting = CrashAI.handle_robot_betting_phase(betting_state)
    betting_robots = count_betting_robots(state_after_betting)
    Logger.info("🧪 [CRASH_TEST] #{betting_robots} 个机器人进行了下注")

    # 测试机器人下车
    flying_state = %{
      state_after_betting
      | game_data: Map.put(state_after_betting.game_data, :phase, :flying)
    }

    # 1.50x
    current_multiplier = 150
    elapsed_time = 1000

    state_after_cashout =
      CrashAI.handle_robot_flying_phase(flying_state, current_multiplier, elapsed_time)

    cashed_out_robots = count_cashed_out_robots(state_after_cashout)
    Logger.info("🧪 [CRASH_TEST] #{cashed_out_robots} 个机器人进行了下车")
  end

  @doc """
  运行所有测试
  """
  def run_all_tests do
    Logger.info("🧪 [CRASH_TEST] 开始运行所有Crash游戏测试")

    test_core_implementation()

    Logger.info("🧪 [CRASH_TEST] 所有测试完成")
  end

  # ==================== 私有辅助函数 ====================

  defp create_test_state(config) do
    %{
      id: "test_room_001",
      players: %{},
      game_data: %{
        phase: :waiting,
        round: 1,
        config: config,
        total_bets: 0,
        betting_players: [],
        crash_time: nil,
        crash_multiplier: nil,
        crashed: false,
        start_time: System.system_time(:millisecond),
        multiplier_history: []
      }
    }
  end

  defp create_test_player(user_id, is_robot \\ false, bet_amount \\ 0) do
    if is_robot do
      CrashAI.create_robot_player(user_id)
    else
      %{
        user_id: user_id,
        numeric_id: user_id,
        nickname: "TestPlayer#{user_id}",
        is_robot: false,
        bet_amount: bet_amount,
        total_recharge: 100_000,
        total_withdraw: 20000,
        total_score: 50000,
        total_revenue: 5000,
        control_value: 10,
        cashed_out: false,
        cash_out_multiplier: nil,
        crashed: false,
        payout: 0,
        profit: 0
      }
    end
  end

  defp count_robots(state) do
    Enum.count(state.players, fn {_id, player} ->
      Map.get(player, :is_robot, false)
    end)
  end

  defp count_betting_robots(state) do
    Enum.count(state.players, fn {_id, player} ->
      Map.get(player, :is_robot, false) and Map.get(player, :bet_amount, 0) > 0
    end)
  end

  defp count_cashed_out_robots(state) do
    Enum.count(state.players, fn {_id, player} ->
      Map.get(player, :is_robot, false) and Map.get(player, :cashed_out, false)
    end)
  end

  @doc """
  测试详细的库存控制功能
  """
  def test_detailed_stock_control do
    Logger.info("🧪 [CRASH_TEST] 详细库存控制测试开始")

    room_id = "test_room_detailed"
    state = %{id: room_id, game_data: %{config: CrashGame.default_config()}}

    # 测试库存状态变化
    Logger.info("🧪 [CRASH_TEST] 1. 测试库存状态变化")

    # 模拟多轮游戏
    Logger.info("🧪 [CRASH_TEST] 2. 模拟多轮游戏的库存变化")

    for round <- 1..5 do
      # 创建测试玩家
      players =
        for i <- 1..3 do
          create_test_player(1000 + i, false, 5000 + i * 1000)
        end

      # 计算爆点
      {multiplier, crash_time} = CrashLogic.calculate_crash_point(state, players)

      # 计算总下注
      total_bet = Enum.sum(Enum.map(players, fn p -> p.bet_amount end))

      Logger.info(
        "🧪 [CRASH_TEST] 第#{round}轮: 爆点=#{multiplier / 100}x, 总下注=#{total_bet}, 时间=#{crash_time}ms"
      )
    end

    # 测试库存控制对爆点的影响
    Logger.info("🧪 [CRASH_TEST] 3. 测试库存控制对爆点的影响")

    # 模拟不同库存状态下的爆点分布
    test_scenarios = [
      {"正常库存", 0},
      {"高库存", 50000},
      {"低库存", -50000}
    ]

    Enum.each(test_scenarios, fn {scenario_name, stock_adjustment} ->
      Logger.info("🧪 [CRASH_TEST] #{scenario_name}状态下的爆点分布:")

      # 创建调整后的状态（暂时跳过库存调整，因为库存控制还未完全实现）
      adjusted_state = state

      # 生成多个爆点样本
      multipliers =
        for _i <- 1..5 do
          players = [create_test_player(1001, false, 10000)]
          {multiplier, _crash_time} = CrashLogic.calculate_crash_point(adjusted_state, players)
          multiplier / 100
        end

      avg_multiplier = Enum.sum(multipliers) / length(multipliers)
      Logger.info("🧪 [CRASH_TEST]   平均倍数: #{Float.round(avg_multiplier, 2)}x")

      Logger.info(
        "🧪 [CRASH_TEST]   倍数范围: #{Float.round(Enum.min(multipliers), 2)}x - #{Float.round(Enum.max(multipliers), 2)}x"
      )
    end)

    Logger.info("🧪 [CRASH_TEST] 详细库存控制测试完成")
    :ok
  end

  @doc """
  测试玩家数量变化广播功能
  """
  def test_player_count_broadcast do
    Logger.info("🧪 [CRASH_TEST] 玩家数量变化广播测试开始")

    # 测试消息构建器
    Logger.info("🧪 [CRASH_TEST] 1. 测试消息构建器")

    # 测试不同玩家数量的消息构建
    for player_count <- [0, 1, 5, 10, 50, 100] do
      message = CrashMessageBuilder.build_player_count_change(player_count)
      Logger.info("🧪 [CRASH_TEST] 玩家数量#{player_count}的消息: #{inspect(message)}")

      # 验证消息格式
      if message["mainId"] != 5 do
        Logger.error("🧪 [CRASH_TEST] 错误: mainId应为5，实际为#{message["mainId"]}")
      end

      if message["subId"] != 1010 do
        Logger.error("🧪 [CRASH_TEST] 错误: subId应为1010，实际为#{message["subId"]}")
      end

      if message["data"]["totalplayernum"] != player_count do
        Logger.error(
          "🧪 [CRASH_TEST] 错误: totalplayernum应为#{player_count}，实际为#{message["data"]["totalplayernum"]}"
        )
      end
    end

    # 测试协议ID获取
    Logger.info("🧪 [CRASH_TEST] 2. 测试协议ID获取")
    protocol_ids = CrashMessageBuilder.protocol_ids()
    Logger.info("🧪 [CRASH_TEST] 协议ID映射: #{inspect(protocol_ids)}")

    if protocol_ids.player_count != 1010 do
      Logger.error("🧪 [CRASH_TEST] 错误: player_count协议ID应为1010，实际为#{protocol_ids.player_count}")
    end

    Logger.info("🧪 [CRASH_TEST] 玩家数量变化广播测试完成")
    :ok
  end
end
