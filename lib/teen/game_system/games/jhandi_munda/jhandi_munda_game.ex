defmodule <PERSON>pridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaGame do
  @moduledoc """
  Jhandi Munda 印度骰子游戏定义模块

  实现游戏工厂行为，定义 Jhandi Munda 游戏的基本信息和配置
  这是一个传统的印度骰子游戏，使用6个骰子，每个骰子有6个不同的符号
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaConstants

  @impl true
  def game_type, do: :jhandi_munda

  @impl true
  def game_name, do: "Jhandi Munda 印度骰子"

  @impl true
  def room_module, do: Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaRoom

  @impl true
  def default_config do
    %{
      # 最大玩家数
      max_players: JhandiMundaConstants.max_players(),
      # 最小玩家数
      min_players: JhandiMundaConstants.min_players(),
      # 1秒后自动开始
      auto_start_delay: 1000,
      # 启用机器人
      enable_robots: true,
      robot_count: 3,
      # 游戏配置
      game_config: %{
        # 骰子配置
        dice_count: JhandiMundaConstants.dice_count(),
        # 下注区域数量
        area_count: JhandiMundaConstants.area_count(),
        # 符号数量
        symbol_count: JhandiMundaConstants.symbol_count(),
        # 最小下注
        min_bet: JhandiMundaConstants.min_bet(),
        # 最大下注
        max_bet: JhandiMundaConstants.max_bet(),
        # 可下注筹码面额
        bet_chips: JhandiMundaConstants.bet_chips(),
        # 赔率表
        odds_table: JhandiMundaConstants.odds_table(),
        # 时间配置
        time_config: %{
          betting_time: JhandiMundaConstants.betting_time(),
          revealing_time: JhandiMundaConstants.revealing_time()
        },
        # 房间配置
        room_config: %{
          max_silence_rounds: JhandiMundaConstants.max_silence_rounds(),
          player_list_limit: JhandiMundaConstants.player_list_limit()
        },
        # 符号配置
        symbols: %{
          clubs: JhandiMundaConstants.symbol_clubs(),
          crown: JhandiMundaConstants.symbol_crown(),
          spades: JhandiMundaConstants.symbol_spades(),
          diamonds: JhandiMundaConstants.symbol_diamonds(),
          flag: JhandiMundaConstants.symbol_flag(),
          hearts: JhandiMundaConstants.symbol_hearts()
        },
        # 游戏状态配置
        game_states: %{
          none: JhandiMundaConstants.game_state_none(),
          betting: JhandiMundaConstants.game_state_betting(),
          revealing: JhandiMundaConstants.game_state_revealing()
        },
        # 协议配置
        protocols: %{
          config: JhandiMundaConstants.protocol_config(),
          opt_time: JhandiMundaConstants.protocol_opt_time(),
          bet_request: JhandiMundaConstants.protocol_bet_request(),
          oper_error: JhandiMundaConstants.protocol_oper_error(),
          bet_success: JhandiMundaConstants.protocol_bet_success(),
          bet_sync: JhandiMundaConstants.protocol_bet_sync(),
          settlement: JhandiMundaConstants.protocol_settlement(),
          history_request: JhandiMundaConstants.protocol_history_request(),
          history_response: JhandiMundaConstants.protocol_history_response(),
          player_list_request: JhandiMundaConstants.protocol_player_list_request(),
          player_list_response: JhandiMundaConstants.protocol_player_list_response()
        }
      }
    }
  end

  @impl true
  def is_lobby_game?, do: false

  @impl true
  def supported_game_ids do
    [
      # Jhandi Munda 游戏ID
      21
    ]
  end

  @doc """
  获取游戏统计信息
  """
  def get_game_stats do
    %{
      total_rooms: get_total_rooms(),
      active_players: get_active_players(),
      game_type: :jhandi_munda,
      game_name: game_name(),
      supported_game_ids: supported_game_ids()
    }
  end

  @doc """
  获取当前活跃房间数
  """
  def get_total_rooms do
    # 通过 RoomManager 获取当前游戏类型的房间数
    try do
      Cypridina.RoomSystem.RoomManager.get_rooms_by_game_type(:jhandi_munda)
      |> length()
    rescue
      _ -> 0
    end
  end

  @doc """
  获取当前活跃玩家数
  """
  def get_active_players do
    # 通过 RoomManager 获取当前游戏类型的玩家数
    try do
      Cypridina.RoomSystem.RoomManager.get_rooms_by_game_type(:jhandi_munda)
      |> Enum.reduce(0, fn room_id, acc ->
        case Cypridina.RoomSystem.RoomManager.get_room_state(room_id) do
          {:ok, state} -> acc + map_size(state.players)
          _ -> acc
        end
      end)
    rescue
      _ -> 0
    end
  end

  @doc """
  验证游戏配置
  """
  def validate_config(config) do
    required_keys = [:max_players, :min_players, :game_config]

    case Enum.all?(required_keys, &Map.has_key?(config, &1)) do
      true ->
        # 验证游戏特定配置
        case validate_game_specific_config(config.game_config) do
          :ok -> {:ok, config}
          {:error, reason} -> {:error, reason}
        end

      false ->
        {:error, "Missing required configuration keys"}
    end
  end

  defp validate_game_specific_config(game_config) do
    required_game_keys = [:dice_count, :area_count, :min_bet, :max_bet, :bet_chips, :odds_table]

    case Enum.all?(required_game_keys, &Map.has_key?(game_config, &1)) do
      true ->
        # 验证具体数值
        cond do
          game_config.dice_count != JhandiMundaConstants.dice_count() ->
            {:error, "Invalid dice count"}

          game_config.area_count != JhandiMundaConstants.area_count() ->
            {:error, "Invalid area count"}

          game_config.min_bet <= 0 ->
            {:error, "Invalid min bet"}

          game_config.max_bet <= game_config.min_bet ->
            {:error, "Invalid max bet"}

          length(game_config.bet_chips) == 0 ->
            {:error, "Empty bet chips"}

          length(game_config.odds_table) != JhandiMundaConstants.dice_count() + 1 ->
            {:error, "Invalid odds table length"}

          true ->
            :ok
        end

      false ->
        {:error, "Missing required game configuration keys"}
    end
  end

  @doc """
  获取游戏版本信息
  """
  def version_info do
    %{
      version: "1.0.0",
      build_date: "2024-12-16",
      features: [
        "传统印度骰子游戏",
        "6个骰子6个符号",
        "多区域下注",
        "实时赔率计算",
        "历史记录追踪",
        "机器人支持",
        "多语言支持"
      ]
    }
  end

  @doc """
  获取游戏规则说明
  """
  def get_game_rules do
    %{
      title: "Jhandi Munda 游戏规则",
      description: "传统印度骰子游戏，使用6个骰子进行游戏",
      rules: [
        "游戏使用6个骰子，每个骰子有6个不同符号：梅花、皇冠、黑桃、方块、国旗、红桃",
        "玩家可以在6个符号区域中选择下注",
        "投掷骰子后，根据出现相同符号的数量计算赔率",
        "赔率表：1个=1倍，2个=3倍，3个=5倍，4个=10倍，5个=20倍，6个=100倍",
        "下注时间为#{JhandiMundaConstants.betting_time()}秒",
        "开奖时间为#{JhandiMundaConstants.revealing_time()}秒"
      ],
      betting_limits: %{
        min_bet: JhandiMundaConstants.min_bet(),
        max_bet: JhandiMundaConstants.max_bet(),
        available_chips: JhandiMundaConstants.bet_chips()
      }
    }
  end

  @doc """
  健康检查
  """
  def health_check do
    %{
      game_module: :healthy,
      total_rooms: get_total_rooms(),
      active_players: get_active_players(),
      timestamp: DateTime.utc_now()
    }
  end
end
