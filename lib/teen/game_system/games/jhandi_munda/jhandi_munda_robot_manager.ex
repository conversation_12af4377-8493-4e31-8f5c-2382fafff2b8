defmodule Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaRobotManager do
  @moduledoc """
  JhandiMunda游戏机器人管理器

  负责管理JhandiMunda游戏中的机器人功能，包括：
  - 机器人创建和配置
  - 机器人AI决策系统（基于历史数据和概率分析）
  - 机器人下注行为管理
  - 机器人轮换机制
  - 机器人积分管理
  """

  require Logger
  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaGame
  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaConstants
  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaHistory
  alias Cypridina.Teen.GameSystem.{PlayerData, PlayerDataBuilder}

  # ==================== 机器人游戏配置结构体 ====================

  # 机器人游戏配置结构体 - 动态配置机器人数量和胜率 (比龙虎更强大!)
  defstruct [
    # 机器人数量配置
    # 默认机器人数量
    robot_count: 3,
    # 最小机器人数量
    min_robot_count: 1,
    # 最大机器人数量
    max_robot_count: 5,

    # 机器人胜率配置 (比龙虎更精细的胜率控制)
    # 机器人整体胜率 (45%)
    win_rate: 0.45,
    # 保守型机器人胜率
    conservative_win_rate: 0.40,
    # 平衡型机器人胜率
    balanced_win_rate: 0.45,
    # 激进型机器人胜率
    aggressive_win_rate: 0.50,
    # 趋势跟随者胜率
    trend_follower_win_rate: 0.48,
    # 逆向思维者胜率
    contrarian_win_rate: 0.42,

    # 下注行为配置 (合并@robot_behavior_config的值)
    # 机器人下注概率 (95% - 临时提高用于测试)
    bet_probability: 0.95,
    # 多区域下注概率 (40%)
    multi_bet_probability: 0.80,
    # 跟随历史趋势概率 (60%)
    follow_trend_probability: 0.60,

    # 积分配置 (合并@robot_behavior_config的值)
    # 机器人初始积分最小值
    initial_points_min: 50000,
    # 机器人初始积分最大值
    initial_points_max: 200_000,
    # 默认积分
    default_robot_points: 100_000,
    # 机器人最低积分（低于此值会被移除）
    min_robot_points: 5000,
    # 最高积分
    max_robot_points: 500_000,

    # 轮换配置 (合并@robot_behavior_config的值)
    # 轮换概率 (8%)
    rotation_probability: 0.08,
    # 机器人最大存活时间 (60分钟，毫秒)
    max_robot_age: 3_600_000,
    # 轮换检查间隔 (10分钟，毫秒)
    rotation_interval: 600_000,

    # 思考时间配置 (合并@robot_behavior_config的值)
    # 最小思考时间 (1秒)
    min_think_time: 1000,
    # 最大思考时间 (10秒)
    max_think_time: 9000
  ]

  @type config_robot_game :: %__MODULE__{
          robot_count: integer(),
          min_robot_count: integer(),
          max_robot_count: integer(),
          win_rate: float(),
          conservative_win_rate: float(),
          balanced_win_rate: float(),
          aggressive_win_rate: float(),
          trend_follower_win_rate: float(),
          contrarian_win_rate: float(),
          bet_probability: float(),
          multi_bet_probability: float(),
          follow_trend_probability: float(),
          initial_points_min: integer(),
          initial_points_max: integer(),
          default_robot_points: integer(),
          min_robot_points: integer(),
          max_robot_points: integer(),
          rotation_probability: float(),
          max_robot_age: integer(),
          rotation_interval: integer(),
          min_think_time: integer(),
          max_think_time: integer()
        }

  # 获取默认配置
  def get_default_config, do: %__MODULE__{}

  # 获取自定义配置
  def get_config(overrides \\ %{}) do
    Map.merge(get_default_config(), overrides)
  end

  # 获取当前配置 (可以从房间状态或其他地方获取，暂时使用默认配置)
  defp get_current_config(_state \\ nil) do
    get_default_config()
  end

  # ==================== 机器人配置 ====================

  # 现在使用结构体配置，不再需要@robot_behavior_config

  # 机器人性格类型
  @robot_personalities [:conservative, :balanced, :aggressive, :trend_follower, :contrarian]

  # ==================== 机器人基础功能 ====================

  @doc """
  判断是否为机器人（公共函数，供房间模块调用）
  现在使用PlayerData结构体的is_robot字段，而不是依赖负数ID
  """
  def is_robot?(%PlayerData{} = player) do
    # 确保总是返回布尔值
    result = PlayerData.is_robot?(player)
    is_boolean(result) and result
  end

  def is_robot?(player_data) when is_map(player_data) do
    # 确保总是返回布尔值
    case Map.get(player_data, :is_robot, false) do
      true -> true
      false -> false
      _ -> false
    end
  end

  def is_robot?(_), do: false

  # 注意：机器人创建功能已移至 SimpleRobotProvider
  # 这里只保留机器人AI决策和下注逻辑

  # ==================== 机器人AI决策和下注逻辑 ====================

  # 注意：机器人创建功能已移至 SimpleRobotProvider
  # 旧的 maybe_add_robots_for_real_player 和 add_robots_to_room 函数已移除

  # 注意：create_single_robot 和 generate_robot_initial_points 函数已移除
  # 机器人创建功能现在由 SimpleRobotProvider 处理

  @doc """
  生成机器人AI数据
  """
  defp generate_robot_ai_data do
    personality = Enum.random(@robot_personalities)

    %{
      personality: personality,
      risk_tolerance: generate_risk_tolerance(personality),
      bet_style: generate_bet_style(personality),
      trend_sensitivity: generate_trend_sensitivity(personality),
      favorite_symbols: generate_favorite_symbols(personality),
      bet_patterns: generate_bet_patterns(personality),
      created_at: DateTime.utc_now(),
      games_played: 0,
      total_wins: 0,
      total_losses: 0,
      last_bet_time: nil,
      consecutive_losses: 0,
      hot_streak: 0
    }
  end

  # ==================== 机器人决策系统 ====================

  @doc """
  执行机器人下注决策
  """
  def execute_robot_betting(state, robot_id, room_pid \\ nil) do
    robot_player = Map.get(state.players, robot_id)

    Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 执行机器人下注决策 - 机器人ID: #{robot_id}")
    Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 机器人存在: #{robot_player != nil}")

    Logger.debug(
      "🤖 [JHANDI_MUNDA_ROBOT] 是机器人: #{robot_player != nil and is_robot?(robot_player)}"
    )

    Logger.debug(
      "🤖 [JHANDI_MUNDA_ROBOT] 游戏状态: #{state.game_data.game_state}, 下注状态: #{JhandiMundaConstants.game_state_betting()}"
    )

    if robot_player != nil and is_robot?(robot_player) and
         state.game_data.game_state == JhandiMundaConstants.game_state_betting() do
      # 获取机器人AI特性
      robot_ai = Map.get(robot_player.user, :robot_ai, %{})
      Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 机器人AI特性: #{inspect(robot_ai)}")

      # 分析游戏历史和当前状态
      game_context = analyze_game_context(state, robot_id)
      Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 游戏上下文: #{inspect(game_context)}")

      # 生成下注决策
      betting_decision = make_betting_decision(robot_ai, game_context, state)
      Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 机器人 #{robot_id} 下注决策: #{inspect(betting_decision)}")

      # 添加思考时间
      think_time = calculate_think_time(robot_ai)
      Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 机器人 #{robot_id} 思考时间: #{think_time}ms")

      # 延迟执行下注决策 - 采用LongHu的简洁模式
      # 使用传入的房间PID，如果没有传入则使用当前进程
      target_pid = room_pid || self()
      Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 当前进程PID: #{inspect(self())}")
      Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 目标房间PID: #{inspect(target_pid)}")
      Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 发送延迟消息: {:robot_bet, #{robot_id}}")

      # 检查目标进程是否存在
      if Process.alive?(target_pid) do
        Process.send_after(target_pid, {:robot_bet, robot_id}, think_time)
        Logger.info("🤖 [JHANDI_MUNDA_ROBOT] ✅ 成功发送延迟消息到进程 #{inspect(target_pid)}")
        Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 机器人 #{robot_id} 将在 #{think_time}ms 后执行下注")
        {:ok, :scheduled}
      else
        Logger.error("🤖 [JHANDI_MUNDA_ROBOT] ❌ 目标进程 #{inspect(target_pid)} 不存在或已死亡")
        {:error, :dead_process}
      end
    else
      Logger.warning("🤖 [JHANDI_MUNDA_ROBOT] 机器人下注条件不满足 - 机器人ID: #{robot_id}")
      {:error, :invalid_robot_or_state}
    end
  end

  @doc """
  为机器人生成下注决策 - 在handle_info中调用
  """
  def make_betting_decision_for_robot(robot_player, state) do
    # 获取机器人AI数据
    robot_ai = Map.get(robot_player.user, :robot_ai, %{})
    Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 机器人AI特性: #{inspect(robot_ai)}")

    # 分析游戏历史和当前状态
    game_context = analyze_game_context(state, robot_player.numeric_id)
    Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 游戏上下文: #{inspect(game_context)}")

    # 生成下注决策
    betting_decision = make_betting_decision(robot_ai, game_context, state)

    Logger.info(
      "🤖 [JHANDI_MUNDA_ROBOT] 机器人 #{robot_player.numeric_id} 下注决策: #{inspect(betting_decision)}"
    )

    betting_decision
  end

  @doc """
  机器人管理主逻辑 - 清理破产机器人
  """
  def manage_robots(state) do
    state
    |> cleanup_broke_robots()
  end

  # 注意：rotate_robots 函数已移除
  # 不再自动轮换机器人

  # ==================== 私有辅助函数 ====================

  # 计算目标机器人数量
  defp calculate_target_robot_count(state) do
    # 根据房间配置和当前状态决定机器人数量
    # 基础机器人数量
    base_count = 2
    # 最多4个机器人，至少留2个位置给真实玩家（6个座位 - 2 = 4）
    max_count = min(4, JhandiMundaConstants.max_players() - 2)

    # 可以根据时间段、房间活跃度等因素调整
    current_hour = DateTime.utc_now().hour

    # 高峰时段减少机器人，低峰时段增加机器人
    adjusted_count =
      case current_hour do
        # 高峰时段
        h when h in 8..12 or h in 18..23 -> base_count
        # 低峰时段
        _ -> min(base_count + 1, max_count)
      end

    adjusted_count
  end

  # 计算需要的机器人数量
  defp calculate_needed_robots(real_player_count, current_robot_count, target_robot_count) do
    # 根据真实玩家数量动态调整
    adjusted_target =
      case real_player_count do
        # 1个真实玩家，保持目标数量
        1 -> target_robot_count
        # 2个真实玩家，稍微减少
        2 -> max(target_robot_count - 1, 1)
        # 3个真实玩家，进一步减少
        3 -> max(target_robot_count - 2, 1)
        # 4+个真实玩家，大幅减少或不要机器人
        _ -> max(target_robot_count - 3, 0)
      end

    max(0, adjusted_target - current_robot_count)
  end

  # 统计机器人数量
  defp count_robots(state) do
    state.players
    |> Enum.count(fn {_player_id, player} -> is_robot?(player) end)
  end

  # 统计真实玩家数量
  defp count_real_players(state) do
    state.players
    |> Enum.count(fn {_player_id, player} -> not is_robot?(player) end)
  end

  # 生成机器人性格特征
  # 0.2-0.5
  defp generate_risk_tolerance(:conservative), do: 0.2 + :rand.uniform() * 0.3
  # 0.4-0.7
  defp generate_risk_tolerance(:balanced), do: 0.4 + :rand.uniform() * 0.3
  # 0.6-1.0
  defp generate_risk_tolerance(:aggressive), do: 0.6 + :rand.uniform() * 0.4
  # 0.3-0.7
  defp generate_risk_tolerance(:trend_follower), do: 0.3 + :rand.uniform() * 0.4
  # 0.5-0.8
  defp generate_risk_tolerance(:contrarian), do: 0.5 + :rand.uniform() * 0.3

  defp generate_bet_style(:conservative), do: :small_safe_bets
  defp generate_bet_style(:balanced), do: :mixed_strategy
  defp generate_bet_style(:aggressive), do: :large_risky_bets
  defp generate_bet_style(:trend_follower), do: :follow_patterns
  defp generate_bet_style(:contrarian), do: :against_trends

  defp generate_trend_sensitivity(:conservative), do: 0.3
  defp generate_trend_sensitivity(:balanced), do: 0.5
  defp generate_trend_sensitivity(:aggressive), do: 0.4
  defp generate_trend_sensitivity(:trend_follower), do: 0.9
  defp generate_trend_sensitivity(:contrarian), do: 0.1

  # 生成机器人偏好的符号
  defp generate_favorite_symbols(personality) do
    all_symbols = JhandiMundaConstants.get_all_symbols()

    case personality do
      :conservative ->
        # 保守型偏好稳定的符号（梅花、红桃）
        Enum.take_random(
          [JhandiMundaConstants.symbol_clubs(), JhandiMundaConstants.symbol_hearts()],
          2
        )

      :aggressive ->
        # 激进型偏好高风险符号（皇冠、国旗）
        Enum.take_random(
          [JhandiMundaConstants.symbol_crown(), JhandiMundaConstants.symbol_flag()],
          2
        )

      _ ->
        # 其他类型随机选择2-3个符号
        Enum.take_random(all_symbols, :rand.uniform(2) + 1)
    end
  end

  # 生成下注模式
  defp generate_bet_patterns(personality) do
    base_amounts = JhandiMundaConstants.bet_chips()

    case personality do
      :conservative ->
        # 保守型偏好小额下注
        Enum.take(base_amounts, 3)

      :aggressive ->
        # 激进型偏好大额下注
        Enum.drop(base_amounts, -3)

      _ ->
        # 其他类型使用中等金额
        Enum.slice(base_amounts, 1, 4)
    end
  end

  # 分析游戏上下文
  defp analyze_game_context(state, robot_id) do
    history = state.game_data.history
    recent_results = JhandiMundaHistory.get_recent_records(history, 10)

    # 获取机器人AI统计数据
    robot_player = Map.get(state.players, robot_id)
    robot_ai = if robot_player, do: Map.get(robot_player.user, :robot_ai, %{}), else: %{}

    %{
      robot_points: get_robot_points(state, robot_id),
      recent_results: recent_results,
      symbol_trends: analyze_symbol_trends(recent_results),
      hot_symbols: find_hot_symbols(recent_results),
      cold_symbols: find_cold_symbols(recent_results),
      current_round: state.game_data.round_id,
      time_left: state.game_data.time_left,
      other_players_count: map_size(state.players) - 1,
      # 添加机器人AI统计信息
      consecutive_losses: Map.get(robot_ai, :consecutive_losses, 0),
      hot_streak: Map.get(robot_ai, :hot_streak, 0),
      games_played: Map.get(robot_ai, :games_played, 0),
      total_wins: Map.get(robot_ai, :total_wins, 0),
      total_losses: Map.get(robot_ai, :total_losses, 0)
    }
  end

  # 获取机器人积分 - 使用与游戏一致的积分获取方式
  defp get_robot_points(state, robot_id) do
    # 使用RoomBase的get_player_points函数，与游戏逻辑保持一致
    # 这个函数会正确处理机器人和真实玩家的积分获取
    try do
      # 调用RoomBase的积分获取函数
      apply(state.__struct__, :get_player_points, [state, robot_id])
    rescue
      _ ->
        # 如果调用失败，尝试从玩家数据中直接获取
        case Map.get(state.players, robot_id) do
          %{user: %{points: points}} when is_integer(points) ->
            points

          %{user: %{money: money}} when is_integer(money) ->
            money

          _ ->
            Logger.warning("🤖 [JHANDI_MUNDA_ROBOT] 无法获取机器人积分: #{robot_id}")
            0
        end
    end
  end

  # 分析符号趋势
  defp analyze_symbol_trends(recent_results) do
    if length(recent_results) < 3 do
      %{}
    else
      recent_results
      |> Enum.reduce(%{}, fn record, acc ->
        record.symbol_counts
        |> Enum.reduce(acc, fn {symbol, count}, inner_acc ->
          Map.update(inner_acc, symbol, count, &(&1 + count))
        end)
      end)
      |> Enum.map(fn {symbol, total_count} ->
        avg_count = total_count / length(recent_results)
        {symbol, avg_count}
      end)
      |> Enum.into(%{})
    end
  end

  # 找出热门符号（最近经常出现）
  defp find_hot_symbols(recent_results) do
    if length(recent_results) < 5 do
      []
    else
      symbol_frequencies = analyze_symbol_trends(recent_results)

      symbol_frequencies
      |> Enum.sort_by(fn {_symbol, freq} -> freq end, :desc)
      |> Enum.take(2)
      |> Enum.map(fn {symbol, _freq} -> symbol end)
    end
  end

  # 找出冷门符号（最近很少出现）
  defp find_cold_symbols(recent_results) do
    if length(recent_results) < 5 do
      []
    else
      symbol_frequencies = analyze_symbol_trends(recent_results)

      symbol_frequencies
      |> Enum.sort_by(fn {_symbol, freq} -> freq end, :asc)
      |> Enum.take(2)
      |> Enum.map(fn {symbol, _freq} -> symbol end)
    end
  end

  # 生成下注决策
  defp make_betting_decision(robot_ai, game_context, state) do
    personality = Map.get(robot_ai, :personality, :balanced)
    risk_tolerance = Map.get(robot_ai, :risk_tolerance, 0.5)
    bet_style = Map.get(robot_ai, :bet_style, :mixed_strategy)
    trend_sensitivity = Map.get(robot_ai, :trend_sensitivity, 0.5)
    favorite_symbols = Map.get(robot_ai, :favorite_symbols, [])

    Logger.debug(
      "🤖 [JHANDI_MUNDA_ROBOT] 机器人AI特性 - 性格: #{personality}, 风险承受: #{risk_tolerance}, 下注风格: #{bet_style}"
    )

    # 检查是否参与下注
    config = get_current_config()
    bet_random = :rand.uniform()
    bet_threshold = config.bet_probability
    Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 下注概率检查 - 随机数: #{bet_random}, 阈值: #{bet_threshold}")

    if bet_random > bet_threshold do
      Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 机器人选择不下注 (随机数 #{bet_random} > 阈值 #{bet_threshold})")
      :no_bet
    else
      Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 机器人决定下注 (随机数 #{bet_random} <= 阈值 #{bet_threshold})")

      # 决定下注策略
      betting_symbols =
        select_betting_symbols(personality, game_context, favorite_symbols, trend_sensitivity)

      Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 选择的下注符号: #{inspect(betting_symbols)}")

      betting_amounts = select_betting_amounts(bet_style, risk_tolerance, game_context)
      Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 选择的下注金额: #{inspect(betting_amounts)}")

      # 生成具体的下注组合
      bet_combination = generate_bet_combination(betting_symbols, betting_amounts, game_context)
      Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 最终下注组合: #{inspect(bet_combination)}")

      bet_combination
    end
  end

  # 选择下注符号
  defp select_betting_symbols(personality, game_context, favorite_symbols, trend_sensitivity) do
    all_symbols = JhandiMundaConstants.get_all_symbols()

    case personality do
      :trend_follower ->
        # 跟随趋势，选择热门符号
        hot_symbols = game_context.hot_symbols

        if length(hot_symbols) > 0 and :rand.uniform() < trend_sensitivity do
          hot_symbols
        else
          Enum.take_random(all_symbols, :rand.uniform(2) + 1)
        end

      :contrarian ->
        # 逆向思维，选择冷门符号
        cold_symbols = game_context.cold_symbols

        if length(cold_symbols) > 0 and :rand.uniform() < trend_sensitivity do
          cold_symbols
        else
          Enum.take_random(all_symbols, :rand.uniform(2) + 1)
        end

      :conservative ->
        # 保守型，偏好自己的符号，少量下注
        if length(favorite_symbols) > 0 and :rand.uniform() < 0.7 do
          Enum.take_random(favorite_symbols, 1)
        else
          Enum.take_random(all_symbols, 1)
        end

      :aggressive ->
        # 激进型，多个符号下注
        config = get_current_config()

        if :rand.uniform() < config.multi_bet_probability do
          # 2-4个符号
          Enum.take_random(all_symbols, :rand.uniform(3) + 2)
        else
          # 1-2个符号
          Enum.take_random(all_symbols, :rand.uniform(2) + 1)
        end

      _ ->
        # 平衡型，混合策略
        symbol_count = if :rand.uniform() < 0.4, do: 1, else: 2
        Enum.take_random(all_symbols, symbol_count)
    end
  end

  # 选择下注金额 - 改进版：智能选择多种筹码，考虑连续性和积分情况
  defp select_betting_amounts(bet_style, risk_tolerance, game_context) do
    available_chips = JhandiMundaConstants.bet_chips()
    robot_points = game_context.robot_points
    consecutive_losses = Map.get(game_context, :consecutive_losses, 0)
    hot_streak = Map.get(game_context, :hot_streak, 0)

    Logger.debug(
      "🤖 [JHANDI_MUNDA_ROBOT] 机器人积分: #{robot_points}, 连败: #{consecutive_losses}, 连胜: #{hot_streak}"
    )

    Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 可用筹码: #{inspect(available_chips)}")

    # 根据积分情况和连胜/连败调整下注策略
    {base_ratio, chip_count} =
      calculate_betting_strategy(robot_points, consecutive_losses, hot_streak, bet_style)

    max_single_bet = max(trunc(robot_points * base_ratio), Enum.min(available_chips))
    Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 最大单次下注: #{max_single_bet}, 筹码种类: #{inspect(chip_count)}")

    # 过滤合适的筹码
    suitable_chips = Enum.filter(available_chips, &(&1 <= max_single_bet))

    # 如果没有合适的筹码，使用最小的筹码
    final_chips =
      if length(suitable_chips) > 0 do
        suitable_chips
      else
        [Enum.min(available_chips)]
      end

    Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 合适的筹码: #{inspect(final_chips)}")

    # 智能选择多种筹码金额
    selected_chips =
      select_smart_chip_combination(final_chips, bet_style, risk_tolerance, chip_count)

    Logger.debug("🤖 [JHANDI_MUNDA_ROBOT] 选择的筹码组合: #{inspect(selected_chips)}")
    selected_chips
  end

  # 计算下注策略：返回 {基础比例, 筹码种类数}
  defp calculate_betting_strategy(robot_points, consecutive_losses, hot_streak, bet_style) do
    # 基础下注比例
    base_ratio =
      case robot_points do
        # 积分少时保守
        p when p < 10000 -> 0.08
        # 中等积分
        p when p < 50000 -> 0.12
        # 较多积分
        p when p < 200_000 -> 0.18
        # 积分很多时可以冒险
        _ -> 0.22
      end

    # 根据连胜/连败调整
    adjusted_ratio =
      cond do
        # 连败时更保守
        consecutive_losses >= 3 -> base_ratio * 0.6
        # 连败时稍保守
        consecutive_losses >= 2 -> base_ratio * 0.8
        # 连胜时更激进
        hot_streak >= 3 -> base_ratio * 1.4
        # 连胜时稍激进
        hot_streak >= 2 -> base_ratio * 1.2
        # 正常情况
        true -> base_ratio
      end

    # 根据下注风格调整筹码种类数
    chip_count =
      case bet_style do
        # 保守型：1-2种筹码
        :small_safe_bets -> 1..2
        # 激进型：2-4种筹码
        :large_risky_bets -> 2..4
        # 混合型：1-3种筹码
        :mixed_strategy -> 1..3
        # 跟随型：2-3种筹码
        :follow_patterns -> 2..3
        # 逆向型：1-2种筹码
        :against_trends -> 1..2
        # 默认：1-2种筹码
        _ -> 1..2
      end

    {adjusted_ratio, chip_count}
  end

  # 智能选择筹码组合
  defp select_smart_chip_combination(available_chips, bet_style, risk_tolerance, chip_count_range) do
    sorted_chips = Enum.sort(available_chips)
    chip_count = Enum.random(chip_count_range)

    case bet_style do
      :small_safe_bets ->
        # 保守型：偏好小额筹码，连续选择
        select_consecutive_small_chips(sorted_chips, chip_count)

      :large_risky_bets ->
        # 激进型：偏好大额筹码，可以跳跃选择
        select_large_chips_combination(sorted_chips, chip_count, risk_tolerance)

      :mixed_strategy ->
        # 混合型：平衡选择小中大筹码
        select_balanced_chips(sorted_chips, chip_count)

      _ ->
        # 其他类型：随机但连续的选择
        select_random_consecutive_chips(sorted_chips, chip_count)
    end
  end

  # 选择连续的小额筹码
  defp select_consecutive_small_chips(sorted_chips, count) do
    max_start_index = max(0, length(sorted_chips) - count)
    start_index = :rand.uniform(max_start_index + 1) - 1
    Enum.slice(sorted_chips, start_index, count)
  end

  # 选择大额筹码组合
  defp select_large_chips_combination(sorted_chips, count, risk_tolerance) do
    # 激进型更倾向于选择后半部分的大额筹码
    large_chips_start = max(0, trunc(length(sorted_chips) * (1 - risk_tolerance)))
    large_chips = Enum.drop(sorted_chips, large_chips_start)

    if length(large_chips) >= count do
      Enum.take_random(large_chips, count)
    else
      # 如果大额筹码不够，补充一些中等筹码
      remaining_count = count - length(large_chips)

      medium_chips =
        sorted_chips
        |> Enum.drop(large_chips_start - remaining_count)
        |> Enum.take(remaining_count)

      large_chips ++ medium_chips
    end
  end

  # 选择平衡的筹码组合
  defp select_balanced_chips(sorted_chips, count) do
    case count do
      1 ->
        # 选择中等金额
        middle_index = div(length(sorted_chips), 2)
        [Enum.at(sorted_chips, middle_index)]

      2 ->
        # 选择一小一大
        small_chip = Enum.at(sorted_chips, :rand.uniform(div(length(sorted_chips), 2)) - 1)

        large_chip =
          Enum.at(
            sorted_chips,
            div(length(sorted_chips), 2) + :rand.uniform(div(length(sorted_chips), 2)) - 1
          )

        [small_chip, large_chip]

      _ ->
        # 选择小中大组合
        small_index = :rand.uniform(div(length(sorted_chips), 3))
        medium_index = div(length(sorted_chips), 3) + :rand.uniform(div(length(sorted_chips), 3))

        large_index =
          div(length(sorted_chips) * 2, 3) + :rand.uniform(div(length(sorted_chips), 3))

        [small_index, medium_index, large_index]
        |> Enum.take(count)
        |> Enum.map(&Enum.at(sorted_chips, &1 - 1))
        |> Enum.filter(&(&1 != nil))
    end
  end

  # 选择随机但连续的筹码
  defp select_random_consecutive_chips(sorted_chips, count) do
    if count >= length(sorted_chips) do
      sorted_chips
    else
      max_start_index = length(sorted_chips) - count
      start_index = :rand.uniform(max_start_index + 1) - 1
      Enum.slice(sorted_chips, start_index, count)
    end
  end

  # 生成具体的下注组合
  defp generate_bet_combination(betting_symbols, betting_amounts, _game_context) do
    if length(betting_symbols) == 0 or length(betting_amounts) == 0 do
      :no_bet
    else
      # 为每个选中的符号分配下注金额
      bets =
        betting_symbols
        |> Enum.map(fn symbol ->
          amount = Enum.random(betting_amounts)
          {symbol, amount}
        end)
        |> Enum.into(%{})

      {:bet, bets}
    end
  end

  # 计算思考时间
  defp calculate_think_time(robot_ai) do
    config = get_current_config()
    base_time = config.min_think_time
    max_time = config.max_think_time
    variation = max_time - base_time

    # 根据机器人性格调整思考时间
    personality_factor =
      case Map.get(robot_ai, :personality, :balanced) do
        # 保守型思考更久
        :conservative -> 1.3
        # 平衡型正常时间
        :balanced -> 1.0
        # 激进型思考较快
        :aggressive -> 0.7
        # 趋势跟随者较快
        :trend_follower -> 0.9
        # 逆向思维者思考较久
        :contrarian -> 1.2
      end

    think_time = base_time + :rand.uniform(variation)
    round(think_time * personality_factor)
  end

  # 清理破产机器人
  defp cleanup_broke_robots(state) do
    config = get_current_config()

    broke_robots =
      state.players
      |> Enum.filter(fn {player_id, player} ->
        is_robot?(player) and player.user.points < config.min_robot_points
      end)
      |> Enum.map(fn {player_id, _player} -> player_id end)

    if length(broke_robots) > 0 do
      Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 清理破产机器人: #{inspect(broke_robots)}")

      updated_players =
        Enum.reduce(broke_robots, state.players, fn robot_id, acc ->
          Map.delete(acc, robot_id)
        end)

      %{state | players: updated_players}
    else
      state
    end
  end

  # 注意：maybe_rotate_robots 函数已移除
  # 不再自动轮换机器人

  # 注意：ensure_robot_count 函数已移除
  # 不再自动确保机器人数量

  # 查找需要轮换的机器人
  defp find_robots_to_rotate(state) do
    config = get_current_config()
    current_time = DateTime.utc_now()

    state.players
    |> Enum.filter(fn {player_id, player} ->
      if is_robot?(player) do
        robot_age = DateTime.diff(current_time, player.user.created_at, :millisecond)

        # 检查是否需要轮换
        robot_age > config.max_robot_age or
          (robot_age > config.rotation_interval and :rand.uniform() < 0.3)
      else
        false
      end
    end)
    |> Enum.map(fn {player_id, _player} -> player_id end)
  end

  @doc """
  从房间移除机器人 - 公开函数供房间调用
  """
  def remove_robot_from_room(state, robot_id) do
    robot_player = Map.get(state.players, robot_id)

    if robot_player != nil and is_robot?(robot_player) do
      Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 移除机器人: #{robot_id}")
      updated_players = Map.delete(state.players, robot_id)
      %{state | players: updated_players}
    else
      state
    end
  end

  # 从房间移除机器人 - 私有函数
  defp remove_robot_from_room_private(state, robot_id) do
    robot_player = Map.get(state.players, robot_id)

    if robot_player != nil and is_robot?(robot_player) do
      Logger.info("🤖 [JHANDI_MUNDA_ROBOT] 移除机器人: #{robot_id}")
      updated_players = Map.delete(state.players, robot_id)
      %{state | players: updated_players}
    else
      state
    end
  end

  # 注意：add_replacement_robots 函数已移除
  # 不再自动添加替换机器人

  @doc """
  更新机器人积分
  """
  def update_robot_points(state, robot_id, amount, reason \\ "游戏结算") do
    robot_player = Map.get(state.players, robot_id)

    if robot_player != nil and is_robot?(robot_player) do
      current_points = robot_player.user.points
      new_points = max(0, current_points + amount)

      updated_user = Map.put(robot_player.user, :points, new_points)
      updated_robot = Map.put(robot_player, :user, updated_user)
      updated_players = Map.put(state.players, robot_id, updated_robot)

      Logger.info(
        "🤖 [JHANDI_MUNDA_ROBOT] 更新机器人积分: #{robot_id} #{amount} (#{reason}) -> #{new_points}"
      )

      %{state | players: updated_players}
    else
      state
    end
  end

  @doc """
  更新机器人AI统计数据
  """
  def update_robot_ai_stats(state, robot_id, result) do
    robot_player = Map.get(state.players, robot_id)

    if robot_player != nil and is_robot?(robot_player) do
      robot_ai = Map.get(robot_player.user, :robot_ai, %{})

      updated_ai =
        robot_ai
        |> Map.update(:games_played, 1, &(&1 + 1))
        |> Map.put(:last_bet_time, DateTime.utc_now())

      updated_ai =
        case result do
          :win ->
            updated_ai
            |> Map.update(:total_wins, 1, &(&1 + 1))
            |> Map.update(:hot_streak, 1, &(&1 + 1))
            |> Map.put(:consecutive_losses, 0)

          :loss ->
            updated_ai
            |> Map.update(:total_losses, 1, &(&1 + 1))
            |> Map.update(:consecutive_losses, 1, &(&1 + 1))
            |> Map.put(:hot_streak, 0)

          _ ->
            updated_ai
        end

      updated_user = Map.put(robot_player.user, :robot_ai, updated_ai)
      updated_robot = Map.put(robot_player, :user, updated_user)
      updated_players = Map.put(state.players, robot_id, updated_robot)

      %{state | players: updated_players}
    else
      state
    end
  end

  @doc """
  获取机器人统计信息
  """
  def get_robot_stats(state) do
    robots =
      state.players
      |> Enum.filter(fn {_player_id, player} -> is_robot?(player) end)
      |> Enum.map(fn {_player_id, player} -> player end)

    %{
      total_robots: length(robots),
      total_robot_points: Enum.sum(Enum.map(robots, & &1.user.points)),
      robot_personalities:
        Enum.map(robots, fn robot ->
          Map.get(robot.user, :robot_ai, %{}) |> Map.get(:personality, :unknown)
        end)
        |> Enum.frequencies(),
      average_robot_points:
        if(length(robots) > 0,
          do: Enum.sum(Enum.map(robots, & &1.user.points)) / length(robots),
          else: 0
        )
    }
  end
end
