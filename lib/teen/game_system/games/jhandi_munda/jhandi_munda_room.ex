defmodule Cypridina.Teen.GameSystem.Games.JhandiMunda.JhandiMundaRoom do
  @moduledoc """
  Jhandi Munda 游戏房间实现

  基于 RoomBase 模板实现的 Jhandi Munda 游戏房间，包含：
  - 游戏状态管理
  - 下注处理
  - 开奖逻辑
  - 结算系统
  - 历史记录
  """

  use Cypridina.Teen.GameSystem.RoomBase, game_type: :jhandi_munda

  require Logger
  import Bitwise

  alias Cypridina.Teen.GameSystem.Games.JhandiMunda.{
    JhandiMundaConstants,
    JhandiMundaGameLogic,
    JhandiMundaHistory,
    JhandiMundaRobotManager
  }

  alias Teen.RobotManagement.{SimpleRobotProvider, RobotStateManager}

  # 协议常量定义
  # JhandiMunda 游戏使用的主协议ID
  @main_proto_xc 5

  # 注意：JhandiMundaRoom 使用 RoomBase 的结构体，不需要单独定义 defstruct
  # 所有游戏特定的数据都存储在 state.game_data 中

  @impl true
  def init_game_logic(state) do
    Logger.info("🎲 [JHANDI_MUNDA] 初始化游戏逻辑: #{state.id}")

    # 合并默认配置和传入的配置
    game_config =
      Map.merge(
        %{
          betting_time: 30,
          min_bet: 10,
          max_bet: 1000,
          min_players: 1
        },
        state.config
      )

    # 添加游戏特定的数据到 game_data 字段
    game_data = %{
      game_state: JhandiMundaConstants.game_state_none(),
      round_id: 1,
      current_bets: %{},
      game_result: nil,
      history: JhandiMundaHistory.init_state(),
      timer_ref: nil,
      time_left: 0,
      player_silence_count: %{},
      total_rounds: 0,
      total_bets: 0,
      total_wins: 0,
      config: game_config,
      # 添加大赢家字段
      big_winners: [],
      # 玩家统计数据（用于betrank显示）
      # %{player_id => %{games_played: 0, total_win_amount: 0}}
      player_stats: %{},
      # 百人场座位系统 - 6个固定座位
      seats: %{
        # 座位1-6，nil表示空座位，player_id表示占用
        1 => nil,
        2 => nil,
        3 => nil,
        4 => nil,
        5 => nil,
        6 => nil
      },
      # 等待队列 - 第7个及以上的玩家进入等待队列
      waiting_queue: [],
      # 玩家到座位的映射
      player_seats: %{},
      # 庄家系统
      banker: %{
        # :system | :player
        type: :system,
        # 玩家庄家ID，nil表示系统庄
        player_id: nil,
        # 系统庄家资金
        system_money: 10_000_000,
        # 本局庄家赢得金额
        win_amount: 0,
        # 本局庄家输掉金额
        lose_amount: 0
      }
    }

    # 启动定期机器人轮换检查（10分钟后开始）
    Process.send_after(self(), {:robot_rotation_check}, 10 * 60 * 1000)

    # 返回更新后的状态，保持 RoomBase 的标准结构
    %{state | game_data: game_data}
  end

  @impl true
  def on_player_joined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎲 [JHANDI_MUNDA] 玩家加入房间: #{user_id}, numeric_id: #{numeric_id}")

    # 获取玩家真实积分 (参考SlotCat实现)
    initial_points = get_player_points(state, numeric_id)
    Logger.info("🎲 [JHANDI_MUNDA] 玩家初始积分: #{initial_points} (机器人: #{player.is_robot})")

    # 如果是机器人，更新其状态为在游戏中
    if player.is_robot do
      Logger.info("🤖 [JHANDI_MUNDA] 更新机器人状态: #{numeric_id} -> :in_game")

      RobotStateManager.update_robot_status(numeric_id, :in_game, %{
        current_room_id: state.id,
        current_game_type: "jhandi_munda"
      })
    end

    # 更新用户信息
    updated_user_info =
      player.user
      |> Map.put(:money, initial_points)

    # 更新state中的玩家信息 (参考LongHu的add_player_to_state)
    updated_player = %{player | user: updated_user_info}
    new_state = %{state | players: Map.put(state.players, numeric_id, updated_player)}

    # 为玩家分配座位
    state_with_seat = allocate_seat_for_player(new_state, numeric_id)

    send_game_config_to_player(state_with_seat, numeric_id)

    # 如果是真实玩家，确保机器人数量
    final_state =
      if not player.is_robot do
        Logger.info("🤖 [JHANDI_MUNDA] 真实玩家加入，检查机器人数量")

        state_with_seat
        |> send_initial_data_to_player(player)
        |> broadcast_player_count_change()
        |> maybe_start_game_if_new_room()
        |> ensure_robot_count()
      else
        state_with_seat
        |> send_initial_data_to_player(player)
        |> broadcast_player_count_change()
        |> maybe_start_game_if_new_room()
      end

    final_state
  end

  @impl true
  def on_player_rejoined(state, player) do
    Logger.info("🎲 [JHANDI_MUNDA] 玩家重连: #{player.numeric_id}")

    # 1. 发送房间信息协议 (mainId=4, subId=2) - 与玩家加入保持一致
    send_room_info_protocol(state, player.numeric_id)

    # 2. 发送重连数据 (mainId=5, subId=0)
    send_reconnect_data_to_player(state, player)
  end

  @impl true
  def on_player_left(state, player) do
    Logger.info("🎲 [JHANDI_MUNDA] 玩家离开: #{player.numeric_id}")

    # 如果是机器人，更新其状态为空闲
    if player.is_robot do
      Logger.info("🤖 [JHANDI_MUNDA] 释放机器人状态: #{player.numeric_id} -> :idle")

      RobotStateManager.update_robot_status(player.numeric_id, :idle, %{
        current_room_id: nil,
        current_game_type: nil
      })
    end

    # 处理游戏特定的清理逻辑
    game_cleaned_state =
      state
      |> remove_player_from_state(player)
      |> deallocate_seat_for_player(player.numeric_id)
      |> remove_player_bets(player.numeric_id)
      |> broadcast_player_count_change()
      |> maybe_manage_robots_after_player_leave()
      |> maybe_reset_room_if_empty()

    # 更新最后活动时间
    %{game_cleaned_state | last_activity: DateTime.utc_now()}
  end

  # 处理游戏消息
  @impl true
  def handle_game_message(state, player, message) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎲 [JHANDI_MUNDA] 收到游戏消息 - 房间: #{state.id}, 用户: #{user_id}, numeric_id: #{numeric_id}, 消息: #{inspect(message)}"
    )

    case message do
      # 处理MainID=4的退出房间协议
      %{"mainId" => 4, "subId" => 40} ->
        data = message["data"] || %{}
        handle_exit_room_protocol(state, player, data)

      # 处理MainID=5的JhandiMunda协议消息
      %{"mainId" => @main_proto_xc, "subId" => sub_id} ->
        data = message["data"] || %{}
        handle_jhandi_munda_protocol(state, player, sub_id, data)

      # 兼容性处理
      %{"cmd" => "get_room_info"} ->
        Logger.info("🏠 [ROOM_INFO_REQUEST] 房间信息请求 - 用户: #{user_id}")
        send_game_config_to_player(state, user_id)
        send_room_state_to_player(state, user_id)
        state

      _ ->
        Logger.info(
          "ℹ️ [GAME_MESSAGE] 消息已通过客户端协议处理或为未知消息 - 用户: #{user_id}, 消息: #{inspect(message)}"
        )

        state
    end
  end

  # 处理JhandiMunda协议消息
  defp handle_jhandi_munda_protocol(state, player, sub_id, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎲 [JHANDI_MUNDA_PROTOCOL] 处理协议 - SubID: #{sub_id}, 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}"
    )

    # 获取协议常量
    bet_request = JhandiMundaConstants.protocol_bet_request()
    history_request = JhandiMundaConstants.protocol_history_request()
    player_list_request = JhandiMundaConstants.protocol_player_list_request()

    case sub_id do
      # 游戏配置协议
      0 ->
        handle_game_config_request(state, player, data)

      # 离开房间
      2 ->
        handle_leave_room_request(state, player, data)

      # 下注请求
      ^bet_request ->
        handle_bet_request(state, numeric_id, data)

      # 历史记录请求
      ^history_request ->
        handle_history_request(state, numeric_id, data)

      # 玩家列表请求
      ^player_list_request ->
        handle_player_list_request(state, numeric_id, data)

      # 其他未实现的协议
      _ ->
        Logger.warning("🎲 [JHANDI_MUNDA] 未实现的子协议: #{sub_id}")
        send_error_response(state, player, sub_id, "未实现的协议")
        state
    end
  end

  # 处理退出房间协议
  defp handle_exit_room_protocol(state, player, _data) do
    user_id = player.user_id
    Logger.info("🎲 [JHANDI_MUNDA] 处理退出房间请求: #{user_id}")

    # 移除玩家
    {:ok, new_state} = handle_player_leave(state, user_id)
    new_state
  end

  # 处理游戏配置请求
  defp handle_game_config_request(state, player, _data) do
    send_game_config_to_player(state, player.user_id)
    state
  end

  # 处理离开房间请求
  defp handle_leave_room_request(state, player, _data) do
    user_id = player.user_id
    Logger.info("🎲 [JHANDI_MUNDA] 处理离开房间请求: #{user_id}")

    # 移除玩家
    {:ok, new_state} = handle_player_leave(state, user_id)
    new_state
  end

  # 发送错误响应
  defp send_error_response(state, player, sub_id, message) do
    error_response = %{
      "mainId" => @main_proto_xc,
      "subId" => sub_id,
      "data" => %{
        "error" => true,
        "message" => message
      }
    }

    send_to_player(state, player, error_response)
  end

  @impl true
  def handle_call({:handle_message, player, message}, _from, state) do
    {result, new_state} = handle_game_message(state, player, message)
    {:reply, result, new_state}
  end

  # ==================== 定时器消息处理 ====================

  @impl true
  def handle_info({:game_preparation_timeout}, state) do
    Logger.info("🎲 [JHANDI_MUNDA] 游戏准备时间结束，开始下注阶段")
    new_state = transition_to_betting_phase(state)
    {:noreply, new_state}
  end

  @impl true
  def handle_info({:betting_timeout}, state) do
    Logger.info("🎲 [JHANDI_MUNDA] 下注时间结束，开始亮牌阶段")
    new_state = transition_to_revealing_phase(state)
    {:noreply, new_state}
  end

  @impl true
  def handle_info({:revealing_timeout}, state) do
    Logger.info("🎲 [JHANDI_MUNDA] 亮牌时间结束，开始结算")
    new_state = process_settlement(state)
    {:noreply, new_state}
  end

  @impl true
  def handle_info({:start_new_round}, state) do
    Logger.info("🎲 [JHANDI_MUNDA] 开始新一轮游戏")

    # 检查是否还有真人玩家
    if has_real_players?(state) do
      new_state =
        state
        |> start_game_preparation()
        # 在新一轮开始时检查机器人轮换
        |> manage_robot_rotation()

      {:noreply, new_state}
    else
      # 没有真人玩家，关闭房间
      Logger.info("🎲 [JHANDI_MUNDA] 房间中没有真人玩家，准备关闭房间: #{state.id}")
      
      # 先清理所有机器人 - 使用统一标准
      state_without_robots = clear_all_robots(state)
      
      # 通知房间管理器销毁房间
      Task.start(fn ->
        Logger.info("🎲 [JHANDI_MUNDA] 请求销毁房间: #{state.id}")
        RoomManager.destroy_room(state.id)
      end)
      
      {:noreply, state_without_robots}
    end
  end

  # 定期机器人轮换检查
  @impl true
  def handle_info({:robot_rotation_check}, state) do
    Logger.debug("🤖 [JHANDI_MUNDA] 定期机器人轮换检查")

    new_state = manage_robot_rotation(state)

    # 设置下次检查（10分钟后）
    Process.send_after(self(), {:robot_rotation_check}, 10 * 60 * 1000)

    {:noreply, new_state}
  end

  @impl true
  def handle_info({:send_game_start_protocols}, state) do
    Logger.info("🎲 [JHANDI_MUNDA] 发送游戏开始协议")

    # 1. 发送游戏配置协议 (mainId=5, subId=1000)
    broadcast_config_protocol(state)

    # 2. 转换到下注阶段并发送操作时间协议
    new_state = transition_to_betting_phase(state)
    {:noreply, new_state}
  end

  # 处理机器人下注消息 - 采用LongHu的简洁模式
  @impl true
  def handle_info({:robot_bet, robot_id}, state) do
    Logger.info("🤖 [JHANDI_MUNDA] 收到机器人下注消息 - 机器人: #{robot_id}")

    # 检查游戏状态是否还在下注阶段
    if state.game_data.game_state == JhandiMundaConstants.game_state_betting() do
      # 获取机器人信息
      robot_player = Map.get(state.players, robot_id)

      if robot_player && JhandiMundaRobotManager.is_robot?(robot_player) do
        # 检查机器人积分是否足够
        robot_points = get_player_points(state, robot_id)

        if robot_points < JhandiMundaConstants.min_bet() do
          # 积分不足，移除机器人
          Logger.info("🤖 [JHANDI_MUNDA] 机器人积分不足，移除: #{robot_id} (积分: #{robot_points})")
          new_state = JhandiMundaRobotManager.remove_robot_from_room(state, robot_id)
          {:noreply, new_state}
        else
          # 在这里生成智能机器人下注决策
          betting_decision =
            JhandiMundaRobotManager.make_betting_decision_for_robot(robot_player, state)

          Logger.info("🤖 [JHANDI_MUNDA] 机器人下注决策: #{robot_id} -> #{inspect(betting_decision)}")

          new_state =
            case betting_decision do
              {:bet, bet_map} ->
                Logger.info("🤖 [JHANDI_MUNDA] 执行机器人 #{robot_id} 的下注: #{inspect(bet_map)}")
                # 执行机器人的下注
                result_state = execute_robot_bets(state, robot_id, bet_map)
                Logger.info("🤖 [JHANDI_MUNDA] 机器人 #{robot_id} 下注执行完成")
                result_state

              :no_bet ->
                Logger.info("🤖 [JHANDI_MUNDA] 机器人 #{robot_id} 选择不下注")
                state

              _ ->
                Logger.warning(
                  "🤖 [JHANDI_MUNDA] 机器人 #{robot_id} 未知下注决策: #{inspect(betting_decision)}"
                )

                state
            end

          {:noreply, new_state}
        end
      else
        Logger.warning("🤖 [JHANDI_MUNDA] 机器人不存在或无效: #{robot_id}")
        {:noreply, state}
      end
    else
      Logger.warning(
        "🤖 [JHANDI_MUNDA] 游戏状态不是下注阶段，忽略机器人下注 - 机器人: #{robot_id}, 当前状态: #{state.game_data.game_state}"
      )

      {:noreply, state}
    end
  end

  # 捕获所有未匹配的消息，用于调试
  @impl true
  def handle_info(msg, state) do
    Logger.warning("🔍 [JHANDI_MUNDA] 收到未匹配的消息: #{inspect(msg)}")
    {:noreply, state}
  end

  # ==================== 游戏流程控制函数 ====================

  # 判断是否为新房间
  defp is_new_room?(state) do
    # game_state_none
    state.game_data.game_state == 0 and
      state.game_data.round_id == 1 and
      state.game_data.timer_ref == nil
  end

  # 如果是新房间则启动游戏
  defp maybe_start_game_if_new_room(state) do
    if is_new_room?(state) and has_real_players?(state) do
      start_game_preparation(state)
    else
      state
    end
  end

  # 检查是否有真实玩家（非机器人）
  defp has_real_players?(state) do
    state.players
    |> Enum.any?(fn {_numeric_id, player} -> not is_robot?(player) end)
  end

  # 开始游戏准备阶段
  defp start_game_preparation(state) do
    Logger.info("🎲 [JHANDI_MUNDA] 开始游戏准备阶段")

    # 首先确保有足够的机器人
    state_with_robots = ensure_robot_count(state)

    # 启动准备定时器，3秒后发送游戏开始协议
    timer_ref = Process.send_after(self(), {:send_game_start_protocols}, 1000)

    new_game_data = %{
      state_with_robots.game_data
      | # game_state_none
        game_state: 0,
        timer_ref: timer_ref,
        time_left: 1
    }

    %{state_with_robots | game_data: new_game_data, room_state: :playing}
  end

  # 转换到下注阶段
  defp transition_to_betting_phase(state) do
    Logger.info("🎲 [JHANDI_MUNDA] 转换到下注阶段")

    # 取消之前的定时器
    if state.game_data.timer_ref do
      Process.cancel_timer(state.game_data.timer_ref)
    end

    # 设置下注阶段
    betting_time = JhandiMundaConstants.betting_time()
    timer_ref = Process.send_after(self(), {:betting_timeout}, betting_time * 1000)

    new_game_data = %{
      state.game_data
      | # game_state_betting
        game_state: 1,
        timer_ref: timer_ref,
        time_left: betting_time,
        current_bets: %{},
        game_result: nil
    }

    new_state = %{state | game_data: new_game_data}

    # 发送操作时间协议，通知下注开始
    # game_state_betting
    broadcast_operation_time_protocol(new_state, 1, betting_time)

    # 启动机器人下注
    start_robot_betting(new_state)

    new_state
  end

  # 转换到亮牌阶段
  defp transition_to_revealing_phase(state) do
    Logger.info("🎲 [JHANDI_MUNDA] 转换到亮牌阶段")

    # 取消下注定时器
    if state.game_data.timer_ref do
      Process.cancel_timer(state.game_data.timer_ref)
    end

    # 生成游戏结果（摇骰子）
    game_result = JhandiMundaGameLogic.roll_dice()

    # 设置亮牌阶段
    revealing_time = JhandiMundaConstants.revealing_time()
    timer_ref = Process.send_after(self(), {:revealing_timeout}, revealing_time * 1000)

    new_game_data = %{
      state.game_data
      | # game_state_revealing
        game_state: 2,
        timer_ref: timer_ref,
        time_left: revealing_time,
        game_result: game_result
    }

    new_state = %{state | game_data: new_game_data}

    # 1. 发送操作时间协议 - 亮牌阶段
    # game_state_revealing
    broadcast_operation_time_protocol(new_state, 2, revealing_time)

    # 2. 发送游戏结果协议（包含完整结果和结算信息）
    {settlement_results, banker_result} = broadcast_game_result_protocol(new_state)

    # 3. 收集大赢家信息（在积分变化之前）
    big_winners = collect_big_winners(new_state, settlement_results)

    # 4. 处理玩家金币变化
    new_state = apply_settlement_results(new_state, settlement_results)

    # 5. 更新机器人AI统计数据
    new_state = update_robot_ai_statistics(new_state, settlement_results)

    # 6. 发送积分更新通知给所有玩家 (参考SlotCat实现)
    send_money_update_notifications(new_state)

    # 7. 更新庄家数据
    new_state = update_banker_result(new_state, banker_result)

    # 8. 存储大赢家信息到状态中，供process_settlement使用
    new_game_data = %{new_state.game_data | big_winners: big_winners}
    new_state = %{new_state | game_data: new_game_data}

    new_state
  end

  # 处理结算
  defp process_settlement(state) do
    Logger.info("🎲 [JHANDI_MUNDA] 处理结算")

    # 取消亮牌定时器
    if state.game_data.timer_ref do
      Process.cancel_timer(state.game_data.timer_ref)
    end

    # 获取已经收集的大赢家信息（在transition_to_revealing_phase中收集）
    big_winners = Map.get(state.game_data, :big_winners, [])

    # 更新历史记录（包含大赢家信息）
    game_record =
      JhandiMundaGameLogic.format_game_record(
        state.game_data.game_result,
        state.game_data.round_id,
        big_winners
      )

    new_history = JhandiMundaHistory.add_record(state.game_data.history, game_record)

    # 准备下一轮，重置庄家本局数据
    new_game_data = %{
      state.game_data
      | # game_state_none
        game_state: 0,
        round_id: state.game_data.round_id + 1,
        history: new_history,
        timer_ref: nil,
        time_left: 0,
        banker: %{state.game_data.banker | win_amount: 0, lose_amount: 0}
    }

    final_state = %{state | game_data: new_game_data}

    # 使用统一的机器人管理 - 在游戏结束时确保机器人状态正确
    final_state = ensure_robot_states_updated(final_state)

    # 3秒后开始新一轮
    Process.send_after(self(), {:start_new_round}, 3000)

    final_state
  end

  # 重置房间到等待状态
  defp reset_room_to_waiting(state) do
    Logger.info("🎲 [JHANDI_MUNDA] 重置房间到等待状态")

    # 取消计时器
    if state.game_data.timer_ref do
      Process.cancel_timer(state.game_data.timer_ref)
    end

    # 重置游戏数据
    new_game_data = %{
      state.game_data
      | # game_state_none
        game_state: 0,
        current_bets: %{},
        game_result: nil,
        timer_ref: nil,
        time_left: 0,
        round_id: 1,
        # 重置座位系统
        seats: %{1 => nil, 2 => nil, 3 => nil, 4 => nil, 5 => nil, 6 => nil},
        waiting_queue: [],
        player_seats: %{}
    }

    %{state | room_state: :waiting, game_data: new_game_data}
  end

  # 应用结算结果 (使用正确的积分处理)
  defp apply_settlement_results(state, settlement_results) do
    settlement_results
    |> Enum.reduce(state, fn {player_id, result}, acc_state ->
      case get_numeric_id(player_id) do
        {:ok, numeric_id} ->
          # 更新玩家统计数据
          new_state = update_player_stats(acc_state, numeric_id, result)

          # 如果玩家有中奖，增加积分
          if result.total_win > 0 do
            final_state = add_player_points(new_state, numeric_id, result.total_win)
            Logger.info("🎲 [JHANDI_MUNDA] 玩家中奖: #{player_id}, 金额: #{result.total_win}")
            final_state
          else
            Logger.info("🎲 [JHANDI_MUNDA] 玩家未中奖: #{player_id}, 下注: #{result.total_bet}")
            new_state
          end

        {:error, _reason} ->
          Logger.error("🎲 [JHANDI_MUNDA] 获取numeric_id失败: #{player_id}")
          acc_state
      end
    end)
  end

  # 发送积分更新通知给所有玩家 (参考SlotCat实现)
  defp send_money_update_notifications(state) do
    # 向房间内所有玩家发送积分更新通知
    Enum.each(state.players, fn {numeric_id, player_data} ->
      player = %{user_id: player_data.user.id, numeric_id: numeric_id}
      send_money_update_notification(state, player)
    end)

    Logger.info("💰 [JHANDI_MUNDA_MONEY_UPDATE] 发送积分更新通知给所有玩家，玩家数量: #{map_size(state.players)}")
  end

  # 发送积分更新通知给单个玩家 (完全参考SlotCat实现)
  defp send_money_update_notification(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    current_points = get_player_points(state, numeric_id)

    money_update_data = %{
      "playerid" => numeric_id,
      "coin" => current_points
    }

    response_money_update = %{
      # MainProto.Game
      "mainId" => 4,
      # Game.SC_ROOM_RESET_COIN_P
      "subId" => 8,
      "data" => money_update_data
    }

    Logger.info(
      "💰 [JHANDI_MUNDA_MONEY_UPDATE] 发送积分更新协议 - 用户: #{user_id}, numeric_id: #{numeric_id}, 积分: #{current_points}"
    )

    send_to_player(state, player, response_money_update)
  end

  # 消息处理函数

  defp handle_bet_request(state, player_id, data) do
    # 验证游戏状态
    # game_state_betting
    if state.game_data.game_state != 1 do
      send_error(state, player_id, JhandiMundaConstants.error_state_error())
      state
    else
      # 处理下注逻辑
      process_bet(state, player_id, data)
    end
  end

  defp handle_history_request(state, player_id, _data) do
    send_history_to_player(state, player_id, state.game_data.history)
    state
  end

  defp handle_player_list_request(state, player_id, _data) do
    send_player_list(state, player_id, state.players)
    state
  end

  # 下注处理函数

  defp process_bet(state, player_id, data) do
    # 根据前端FishPCGameCore.ts的requestBet实现，使用正确的字段名
    # info["bet"] = 下注金额, info["direction"] = 下注区域
    symbol = Map.get(data, "direction", Map.get(data, "pos", 0))
    amount = Map.get(data, "bet", Map.get(data, "money", 0))

    # 验证下注参数
    case JhandiMundaGameLogic.validate_bet(symbol, amount) do
      :ok ->
        # 检查玩家余额
        case check_player_balance(state, player_id, amount) do
          {:ok, _balance} ->
            # 处理下注
            execute_bet(state, player_id, symbol, amount)

          {:error, reason} ->
            send_error(state, player_id, JhandiMundaConstants.error_not_money_to_bet())
            state
        end

      {:error, :invalid_symbol} ->
        send_error(state, player_id, JhandiMundaConstants.error_buy_pos_error())
        state

      {:error, :bet_too_small} ->
        send_error(state, player_id, JhandiMundaConstants.error_not_money_to_bet())
        state

      {:error, :bet_too_large} ->
        send_error(state, player_id, JhandiMundaConstants.error_bet_too_more())
        state

      {:error, :invalid_chip} ->
        send_error(state, player_id, JhandiMundaConstants.error_illegal_bet())
        state
    end
  end

  defp execute_bet(state, player_id, symbol, amount) do
    Logger.info("🎲 [JHANDI_MUNDA] 玩家下注: #{player_id}, 符号: #{symbol}, 金额: #{amount}")

    # 按照SlotCat的逻辑处理numeric_id (参考SlotCat的handle_game_start_request)
    numeric_id =
      if is_integer(player_id) do
        # player_id就是numeric_id
        player_id
      else
        # 如果player_id是UUID，需要转换为numeric_id
        case get_numeric_id(player_id) do
          {:ok, id} ->
            id

          {:error, _} ->
            Logger.error("🎲 [JHANDI_MUNDA] 获取numeric_id失败: #{player_id}")
            send_error(state, player_id, JhandiMundaConstants.error_not_money_to_bet())
            # 返回一个特殊值表示错误
            nil
        end
      end

    # 如果numeric_id获取失败，直接返回
    if numeric_id == nil do
      state
    else
      # 检查玩家是否在房间中 (参考SlotCat的逻辑)
      case Map.get(state.players, numeric_id) do
        nil ->
          Logger.error("🎲 [JHANDI_MUNDA] 玩家不在房间中: #{player_id}")
          send_error(state, player_id, JhandiMundaConstants.error_not_money_to_bet())
          state

        _player ->
          # 检查玩家余额 (参考SlotCat的execute_game_logic)
          current_points = get_player_points(state, numeric_id)

          if current_points < amount do
            Logger.warning(
              "🎲 [JHANDI_MUNDA] 用户余额不足: #{player_id}, 余额: #{current_points}, 需要: #{amount}"
            )

            send_error(state, player_id, JhandiMundaConstants.error_not_money_to_bet())
            state
          else
            # 扣除下注金额 (参考SlotCat的process_game_round)
            new_state = subtract_player_points(state, numeric_id, amount)

            # 检查扣除后的余额
            remaining_points = get_player_points(new_state, numeric_id)

            if remaining_points < 0 do
              Logger.error("🎲 [JHANDI_MUNDA] 扣除下注金额后余额不足: #{remaining_points}")
              send_error(state, player_id, JhandiMundaConstants.error_not_money_to_bet())
              state
            else
              # 更新下注记录
              player_bets = Map.get(new_state.game_data.current_bets, player_id, %{})
              new_player_bets = Map.update(player_bets, symbol, amount, &(&1 + amount))

              new_current_bets =
                Map.put(new_state.game_data.current_bets, player_id, new_player_bets)

              # 更新游戏数据
              new_game_data = %{new_state.game_data | current_bets: new_current_bets}
              final_state = %{new_state | game_data: new_game_data}

              # 发送下注成功消息
              send_bet_success(final_state, player_id, symbol, amount, remaining_points)

              # 广播下注同步消息
              broadcast_bet_sync(final_state, player_id, symbol, amount)

              final_state
            end
          end
      end
    end
  end

  defp process_player_settlements(state, player_results) do
    # 这个函数已经被apply_settlement_results替代，保持兼容性
    apply_settlement_results(state, player_results)
  end

  # 添加缺失的辅助函数
  defp player_id_to_key(player) do
    # 根据玩家对象返回用于下注记录的key
    # 可以是user_id或numeric_id，根据实际使用情况
    player.user_id
  end

  # ==================== 玩家加入和重连处理 ====================

  # 发送初始数据给玩家
  defp send_initial_data_to_player(state, player) do
    player_id = player.numeric_id

    # 1. 发送房间信息协议 (mainId=4, subId=2) - 必须发送
    send_room_info_protocol(state, player_id)

    # 2. 广播玩家加入 (mainId=4, subId=12) - 必须发送，在协议2后面
    broadcast_player_join_protocol(state, player)

    # 3. 根据房间状态发送相应协议
    send_current_state_protocol(state, player_id)
    # 发送断线重连协议 (mainId=5, subId=0) - 必须发送
    send_to_other_room_protocol(state, player_id)
    state
  end

  # 发送重连数据给玩家
  defp send_reconnect_data_to_player(state, player) do
    player_id = player.numeric_id

    # 发送断线重连协议 (mainId=5, subId=0) - 必须发送
    send_to_other_room_protocol(state, player_id)

    Logger.info("📤 [RECONNECT] 玩家重连: #{player_id}, 当前状态: #{state.game_data.game_state}")

    state
  end

  # ==================== 协议发送函数 ====================

  # 房间信息协议 (mainId=4, subId=2)
  defp send_room_info_protocol(state, player_id) do
    # 构建动态玩家列表 (按座位号索引)
    dynamic_player_list = build_player_list(state, :map_by_seat)

    message = %{
      "mainId" => 4,
      "subId" => 2,
      "data" => %{
        "room_id" => to_string(state.id),
        "roundid" => state.game_data.round_id,
        "roomstate" => get_room_state_code(state.room_state),
        "game_type" => "jhandi_munda",
        "playerlist" => dynamic_player_list
      }
    }

    send_to_player_by_id(state, player_id, message)
    Logger.info("📤 [ROOM_INFO] 发送房间信息给玩家: #{player_id}, 玩家数量: #{map_size(state.players)}")
  end

  # 统一的玩家列表构建函数
  defp build_player_list(state, format \\ :map_by_playerid) do
    player_data_list =
      state.players
      |> Enum.map(fn {numeric_id, player} ->
        current_money = get_player_points(state, numeric_id)
        display_name = get_safe_display_name(player)
        # 使用新的座位系统获取座位号
        seat_number = get_player_seat_id(state, numeric_id)

        %{
          "coin" => current_money,
          "headid" => get_player_head_id(player),
          "money" => current_money,
          "name" => display_name,
          # undefined 在 Elixir 中用 nil 表示
          "nickname" => display_name,
          "playerid" => numeric_id,
          "seat" => seat_number,
          "status" => 1,
          "wxheadurl" => ""
        }
      end)

    case format do
      :map_by_seat ->
        # 房间信息协议格式：按座位号索引（只包含有座位的玩家）
        player_data_list
        |> Enum.filter(fn player_info -> player_info["seat"] > 0 end)
        |> Enum.reduce(%{}, fn player_info, acc ->
          Map.put(acc, to_string(player_info["seat"]), player_info)
        end)

      :map_by_playerid ->
        # 断线重连协议格式：按玩家ID索引
        player_data_list
        |> Enum.reduce(%{}, fn player_info, acc ->
          Map.put(acc, to_string(player_info["playerid"]), player_info)
        end)

      :map_by_index ->
        # 玩家列表协议格式：按索引号索引（前端期望格式）
        player_data_list
        |> Enum.with_index(1)
        |> Enum.reduce(%{}, fn {player_info, index}, acc ->
          Map.put(acc, to_string(index), player_info)
        end)

      :list ->
        # 玩家列表协议格式：直接返回数组
        player_data_list
    end
  end

  # 玩家加入广播 (mainId=4, subId=12)
  defp broadcast_player_join_protocol(state, player) do
    # 安全获取显示名称，支持新旧格式
    display_name = get_safe_display_name(player)

    message = %{
      "mainId" => 4,
      "subId" => 12,
      "data" => %{
        "playerid" => player.numeric_id,
        "name" => display_name,
        "money" => get_player_points(state, player.numeric_id),
        "action" => "join",
        "seat" => get_player_seat_id(state, player.numeric_id)
      }
    }

    broadcast_to_room(state, message)
    Logger.info("📤 [PLAYER_JOIN] 广播玩家加入: #{player.numeric_id}")
  end

  # 游戏配置协议 (mainId=5, subId=1000)
  defp broadcast_config_protocol(state) do
    banker_info = get_banker_info(state)

    message = %{
      "mainId" => 5,
      "subId" => 1000,
      "data" => %{
        "difen" => JhandiMundaConstants.min_bet(),
        "roundid" => state.game_data.round_id,
        "banker" => banker_info
      }
    }

    broadcast_to_room(state, message)
    Logger.info("📤 [CONFIG] 广播游戏配置（含庄家信息）")
  end

  # 操作时间协议 (mainId=5, subId=1001)
  defp broadcast_operation_time_protocol(state, game_state, time_left) do
    message = %{
      "mainId" => 5,
      "subId" => 1001,
      "data" => %{
        "waittime" => time_left,
        "roomstate" => game_state,
        "roundid" => state.game_data.round_id
      }
    }

    broadcast_to_room(state, message)
    Logger.info("📤 [OPERATION_TIME] 广播操作时间 - 状态: #{game_state}, 时间: #{time_left}")
  end

  # 游戏结果协议 (mainId=5, subId=1006) - 包含完整的游戏结果和结算信息
  defp broadcast_game_result_protocol(state) do
    # 计算玩家输赢
    settlement_results =
      JhandiMundaGameLogic.calculate_settlements(
        state.game_data.current_bets,
        state.game_data.game_result
      )

    # 计算庄家盈亏
    banker_result = calculate_banker_result(state, settlement_results)

    # 获取获胜区域
    winning_positions = calculate_winning_positions(state.game_data.game_result)

    # 获取骰子结果中的点数统计
    dice_counts = format_dice_result_for_frontend(state.game_data.game_result)

    # 计算白色点数和红色点数
    # 白色符号：梅花(1)、皇冠(2)、黑桃(3)
    # 红色符号：方块(4)、国旗(5)、红桃(6)
    white_point =
      Map.get(dice_counts, "1", 0) + Map.get(dice_counts, "2", 0) + Map.get(dice_counts, "3", 0)

    red_point =
      Map.get(dice_counts, "4", 0) + Map.get(dice_counts, "5", 0) + Map.get(dice_counts, "6", 0)

    # 构建庄家数据
    zhuang_data = %{
      # 系统庄家ID为0
      "playerid" => 0,
      "playercoin" => state.game_data.banker.system_money,
      "changemoney" => banker_result.net_amount
    }

    message = %{
      "mainId" => 5,
      "subId" => 1006,
      "data" => %{
        "roundid" => state.game_data.round_id,
        "posnums" => dice_counts,
        # 🔥 新增：前端走势图需要的字段
        "sposnums" => dice_counts,
        "winpos" => winning_positions,
        "whitepoint" => white_point,
        "redpoint" => red_point,
        "zhuang" => zhuang_data,
        "other" => format_settlement_results_for_frontend(state, settlement_results)
      }
    }

    broadcast_to_room(state, message)
    Logger.info("📤 [GAME_RESULT] 广播游戏结果 - 包含完整结果和结算信息")

    # 返回结算结果供后续处理使用
    {settlement_results, banker_result}
  end

  # 断线重连协议 (mainId=5, subId=0)
  defp send_to_other_room_protocol(state, player_id) do
    {:ok, numeric_id} = get_numeric_id(player_id)
    player_name = get_player_display_name(state, player_id)
    current_money = get_player_balance(state, player_id)
    banker_info = get_banker_info(state)

    message = %{
      "mainId" => @main_proto_xc,
      "subId" => 0,
      "data" => %{
        "difen" => JhandiMundaConstants.min_bet(),
        "roundid" => state.game_data.round_id,
        "state" => state.game_data.game_state,
        "waittime" => state.game_data.time_left,
        "allbet" => format_all_bets_for_frontend(state.game_data.current_bets),
        "mybet" => format_player_bets_for_frontend(state.game_data.current_bets, player_id),
        "leftbet" => get_player_remaining_bets(player_id),
        "config" => %{
          "betmax" => JhandiMundaConstants.max_bet(),
          "betneed" => JhandiMundaConstants.min_bet(),
          "bet" => JhandiMundaConstants.bet_chips(),
          "odds" => JhandiMundaConstants.odds_table()
        },
        "playerlist" => build_player_list(state, :map_by_playerid),
        "data" => format_history_for_frontend(state.game_data.history),
        "banker" => banker_info
      }
    }

    send_to_player_by_id(state, player_id, message)
    Logger.info("📤 [TO_OTHER_ROOM] 发送断线重连信息给玩家: #{player_id}（含庄家信息）")
  end

  # 根据当前状态发送协议
  defp send_current_state_protocol(state, player_id) do
    case state.game_data.game_state do
      # game_state_betting
      1 ->
        # 下注阶段：发送1001协议
        send_operation_time_to_player(state, player_id, 1, state.game_data.time_left)

      # game_state_revealing
      2 ->
        # 亮牌阶段：发送1001协议和当前结果
        send_operation_time_to_player(state, player_id, 2, state.game_data.time_left)

        if state.game_data.game_result do
          send_current_result_to_player(state, player_id)
        end

      _ ->
        # NONE状态：不发送额外协议
        Logger.info("📤 [CURRENT_STATE] 玩家加入NONE状态房间，等待下一轮开始")
    end
  end

  # 发送操作时间给单个玩家
  defp send_operation_time_to_player(state, player_id, game_state, time_left) do
    message = %{
      "mainId" => 5,
      "subId" => 1001,
      "data" => %{
        "waittime" => time_left,
        "roomstate" => game_state,
        "roundid" => state.game_data.round_id
      }
    }

    send_to_player_by_id(state, player_id, message)
    Logger.info("📤 [OPERATION_TIME_PLAYER] 发送操作时间给玩家: #{player_id}")
  end

  # 发送当前结果给单个玩家
  defp send_current_result_to_player(state, player_id) do
    message = %{
      "mainId" => 5,
      "subId" => 1006,
      "data" => %{
        "roundid" => state.game_data.round_id,
        "posnums" => format_dice_result_for_frontend(state.game_data.game_result)
      }
    }

    send_to_player_by_id(state, player_id, message)
    Logger.info("📤 [CURRENT_RESULT_PLAYER] 发送当前结果给玩家: #{player_id}")
  end

  # ==================== 辅助函数 ====================

  # 格式化骰子结果给前端
  defp format_dice_result_for_frontend(game_result) do
    # game_result 是一个包含 dice_results, symbol_counts, winning_symbols 的map
    # 我们需要返回 symbol_counts 给前端
    case game_result do
      %{symbol_counts: symbol_counts} ->
        # 将 symbol_counts 转换为前端期望的格式 %{"1" => count1, "2" => count2, ...}
        symbol_counts
        |> Enum.reduce(%{}, fn {symbol, count}, acc ->
          Map.put(acc, to_string(symbol), count)
        end)

      _ ->
        # 如果格式不对，返回空map
        Logger.warning("🎲 [JHANDI_MUNDA] 游戏结果格式错误: #{inspect(game_result)}")
        %{}
    end
  end

  # 计算获胜位置
  defp calculate_winning_positions(game_result) do
    # game_result 包含 winning_symbols 字段
    case game_result do
      %{winning_symbols: winning_symbols} ->
        winning_symbols

      _ ->
        Logger.warning("🎲 [JHANDI_MUNDA] 游戏结果格式错误: #{inspect(game_result)}")
        []
    end
  end

  # 格式化所有下注信息给前端
  defp format_all_bets_for_frontend(current_bets) do
    # 将下注信息转换为前端期望的数组格式
    JhandiMundaConstants.get_all_symbols()
    |> Enum.map(fn symbol ->
      total_bet =
        current_bets
        |> Enum.reduce(0, fn {_player_id, player_bets}, acc ->
          acc + Map.get(player_bets, symbol, 0)
        end)

      total_bet
    end)
  end

  # 格式化玩家下注信息给前端
  defp format_player_bets_for_frontend(current_bets, player_id) do
    player_bets = Map.get(current_bets, player_id, %{})

    JhandiMundaConstants.get_all_symbols()
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {symbol, index}, acc ->
      bet_amount = Map.get(player_bets, symbol, 0)

      if bet_amount > 0 do
        Map.put(acc, to_string(index), bet_amount)
      else
        acc
      end
    end)
  end

  # 格式化结算结果给前端
  defp format_settlement_results_for_frontend(state, settlement_results) do
    build_settlement_other_data(state, settlement_results)
  end

  @doc """
  构建结算时的 other 数据，符合客户端期望格式（参照LongHu实现）

  客户端格式参考：
  if (msg.other) {
    let count = 0;
    for (let key in msg.other) {
      let item = msg.other[key];
      let tmp = item.split(",");
      let userData: any = {};
      userData.playercoin = parseInt(tmp[0]);
      userData.nChange = parseInt(tmp[1]);
      userData.playerid = key;
      count += 1;
      resultMsg.others.betrank[count.toString()] = userData;
    }
  }
  """
  defp build_settlement_other_data(state, settlement_results) do
    settlement_results
    |> Enum.filter(fn {player_id, result} ->
      # 只包含有效的玩家数据：用户ID不为空且结算结果不为nil
      player_id != nil and result != nil
    end)
    |> Enum.into(%{}, fn {player_id, result} ->
      # 获取玩家的数字ID
      {:ok, numeric_id} = get_numeric_id(player_id)

      # 获取玩家结算后的真实余额
      final_balance = get_player_balance(state, player_id)

      # 计算输赢金额（总赢得 - 总下注）
      win_amount = result.total_win - result.total_bet

      Logger.info(
        "🎲 [SETTLEMENT_OTHER] 构建玩家结算数据 - 玩家: #{numeric_id}, 输赢: #{win_amount}, 余额: #{final_balance}"
      )

      # 客户端期望格式: "playerid" => "剩余积分,输赢积分"
      # 其中 playerid 必须是字符串格式
      player_id_str = to_string(numeric_id)
      settlement_str = "#{final_balance},#{win_amount}"

      {player_id_str, settlement_str}
    end)
  end

  # 格式化历史记录给前端（断线重连专用）
  defp format_history_for_frontend(history) do
    # 前端期望的格式：{ "reclist": [{"1": count1, "2": count2, ...}, ...] }
    reclist =
      JhandiMundaHistory.get_recent_records(history, 20)
      |> Enum.map(fn record ->
        # 转换为前端期望的格式 {"1" => count1, "2" => count2, ...}
        case record do
          %{symbol_counts: symbol_counts} ->
            # 如果有symbol_counts字段，直接转换
            JhandiMundaConstants.get_all_symbols()
            |> Enum.reduce(%{}, fn symbol, acc ->
              count = Map.get(symbol_counts, symbol, 0)
              Map.put(acc, to_string(symbol), count)
            end)

          %{dice_result: dice_result} when is_map(dice_result) ->
            # 如果有dice_result字段且是map格式，转换它
            JhandiMundaConstants.get_all_symbols()
            |> Enum.reduce(%{}, fn symbol, acc ->
              count = Map.get(dice_result, symbol, 0)
              Map.put(acc, to_string(symbol), count)
            end)

          _ ->
            # 其他情况，返回空记录
            JhandiMundaConstants.get_all_symbols()
            |> Enum.reduce(%{}, fn symbol, acc ->
              Map.put(acc, to_string(symbol), 0)
            end)
        end
      end)

    %{"reclist" => reclist}
  end

  # 获取玩家剩余下注额度
  defp get_player_remaining_bets(player_id) do
    # 返回玩家在各个区域的剩余下注额度
    # 这里简化处理，返回空数组
    []
  end

  # 获取房间状态代码
  defp get_room_state_code(room_state) do
    case room_state do
      :waiting -> 0
      :playing -> 1
      :settling -> 2
      :finished -> 3
      _ -> 0
    end
  end

  defp send_history_to_player(state, player_id, history) do
    history_data = JhandiMundaHistory.format_for_client(history, count: 20, include_trend: true)

    # 构建前端期望的历史数据格式，包含大赢家信息
    formatted_data = %{
      "reclist" =>
        history_data.data
        |> Enum.map(fn record ->
          # 转换为前端期望的格式 {"1" => count1, "2" => count2, ...}
          JhandiMundaConstants.get_all_symbols()
          |> Enum.reduce(%{}, fn symbol, acc ->
            count = Map.get(record.posnums, symbol, 0)
            Map.put(acc, to_string(symbol), count)
          end)
        end),
      "big_winners" =>
        history_data.data
        |> Enum.flat_map(fn record ->
          Map.get(record, :big_winners, [])
        end)
        # 只取最近20个大赢家
        |> Enum.take(20)
    }

    message = %{
      "mainId" => @main_proto_xc,
      "subId" => JhandiMundaConstants.protocol_history_response(),
      "data" => formatted_data
    }

    send_to_player_by_id(state, player_id, message)
  end

  defp send_player_list(state, player_id, players) do
    # 使用对象格式的玩家列表（前端期望的格式）
    player_list = build_player_list(state, :map_by_index)

    # 构建下注排行榜数据（前端桌面显示用）
    betrank_data = build_betrank_data(state)

    # 构建赢分排行榜数据
    winscorerank_data = build_winscorerank_data(state)

    message = %{
      "mainId" => @main_proto_xc,
      "subId" => JhandiMundaConstants.protocol_player_list_response(),
      "data" => %{
        "totalplayernum" => map_size(state.players),
        "betrank" => betrank_data,
        "playerlist" => player_list,
        "winscorerank" => winscorerank_data
      }
    }

    send_to_player_by_id(state, player_id, message)

    Logger.info(
      "📤 [SEND_PLAYER_LIST] 发送玩家列表 - 请求者: #{player_id}, 玩家数量: #{map_size(state.players)}"
    )
  end

  defp send_error(state, player_id, error_code) do
    message = %{
      "mainId" => @main_proto_xc,
      "subId" => JhandiMundaConstants.protocol_oper_error(),
      "data" => %{
        "errorCode" => error_code
      }
    }

    send_to_player_by_id(state, player_id, message)
  end

  defp send_bet_success(state, player_id, symbol, amount, new_balance) do
    # 根据前端 FishPCGameCore.ts 的 onBetSuccess 和 myBetSuccess 方法需要的数据格式
    # onBetSuccess 需要: leftbet, chouma, _playerid
    # myBetSuccess 需要: bet, direction, chouma, totalbet, allbet, mybet

    # 获取玩家的所有下注信息
    player_bets = Map.get(state.game_data.current_bets, player_id, %{})

    # 构建 mybet 数据 - 玩家在各个区域的总下注
    mybet = build_player_bet_data(player_bets)

    # 构建 allbet 数据 - 所有区域的总下注
    allbet = build_all_bets_info(state.game_data.current_bets)

    # 计算剩余可下注限制 (leftbet)
    leftbet = calculate_remaining_bet_limits(state)

    # 获取玩家numeric_id用于_playerid字段
    numeric_id = get_player_numeric_id(state, player_id)

    message = %{
      "mainId" => @main_proto_xc,
      "subId" => JhandiMundaConstants.protocol_bet_success(),
      "data" => %{
        # 本次下注信息
        "bet" => amount,
        "direction" => symbol,

        # 玩家金币信息
        "chouma" => new_balance,
        "_playerid" => numeric_id,

        # 下注统计信息
        "totalbet" => get_total_bets_for_symbol(state.game_data.current_bets, symbol),
        "allbet" => allbet,
        "mybet" => mybet,

        # 剩余下注限制
        "leftbet" => leftbet
      }
    }

    Logger.info("📤 [SEND_BET_SUCCESS] 发送下注成功协议 - 玩家: #{player_id}, 区域: #{symbol}, 金额: #{amount}")
    send_to_player_by_id(state, player_id, message)
  end

  # 广播函数

  defp broadcast_game_state_change(state) do
    message = %{
      "mainId" => @main_proto_xc,
      "subId" => JhandiMundaConstants.protocol_opt_time(),
      "data" => %{
        "gameState" => state.game_data.game_state,
        "timeLeft" => state.game_data.time_left,
        "roundId" => state.game_data.round_id
      }
    }

    broadcast_to_room(state, message)
  end

  defp broadcast_bet_sync(state, player_id, symbol, amount) do
    # 根据前端 FishPCGameCore.ts 的 onBetSYNC 函数，构建符合前端期望的数据格式
    # 前端期望格式：
    # {
    #   "playerlist": {
    #     "玩家ID": {
    #       "playerid": 玩家ID,
    #       "seatid": 座位号,
    #       "betnum": { "区域": 编码后的筹码增量 },
    #       "chouma": 玩家金币
    #     }
    #   },
    #   "roundid": 回合ID,
    #   "allbet": { "区域": 总下注额 }
    # }

    # 获取玩家信息
    player = Map.get(state.players, player_id)
    player_balance = get_player_balance(state, player_id)

    # 编码筹码增量信息 - 参考LongHu的encode_bet_for_sync实现
    encoded_bet_amount = encode_bet_for_sync(amount, symbol)

    # 构建玩家下注增量信息
    player_bet_info = %{
      "playerid" => player_id,
      "seatid" => get_player_seat_id(state, player_id),
      "betnum" => %{
        to_string(symbol) => encoded_bet_amount
      },
      "chouma" => player_balance
    }

    # 构建所有区域的总下注信息
    all_bets = build_all_bets_info(state.game_data.current_bets)

    message = %{
      "mainId" => @main_proto_xc,
      "subId" => JhandiMundaConstants.protocol_bet_sync(),
      "data" => %{
        "playerlist" => %{
          to_string(player_id) => player_bet_info
        },
        "roundid" => state.game_data.round_id,
        "allbet" => all_bets
      }
    }

    Logger.info(
      "📤 [BROADCAST_BET_SYNC] 发送筹码增量广播 - 玩家: #{player_id}, 区域: #{symbol}, 金额: #{amount}"
    )

    Logger.info("📤 [BROADCAST_BET_SYNC] 编码后的筹码数据: #{inspect(encoded_bet_amount)}")

    # 使用标准的房间广播函数，与其他协议保持一致
    broadcast_to_room(state, message)
  end

  # defp broadcast_game_result(state) do
  #   message = %{
  #     "mainId" => @main_proto_xc,
  #     "subId" => JhandiMundaConstants.protocol_settlement(),
  #     "data" => %{
  #       "roundId" => state.game_data.round_id,
  #       "diceResults" => state.game_data.game_result.dice_results,
  #       "symbolCounts" => state.game_data.game_result.symbol_counts,
  #       "winningSymbols" => state.game_data.game_result.winning_symbols
  #     }
  #   }
  #   broadcast_to_all_players(state, message)
  # end

  defp broadcast_settlement(state, player_results) do
    # 为每个玩家发送个人结算信息
    state.players
    |> Enum.each(fn {player_id, _player_info} ->
      player_result = Map.get(player_results, player_id, %{total_win: 0, win_details: %{}})

      message = %{
        "mainId" => @main_proto_xc,
        "subId" => JhandiMundaConstants.protocol_settlement(),
        "data" => %{
          "roundId" => state.game_data.round_id,
          "totalWin" => player_result.total_win,
          "winDetails" => player_result.win_details,
          "balance" => get_player_balance(state, player_id)
        }
      }

      send_to_player_by_id(state, player_id, message)
    end)
  end

  # 辅助函数

  defp format_current_bets(current_bets) do
    current_bets
    |> Enum.map(fn {player_id, player_bets} ->
      %{
        "playerId" => player_id,
        "bets" => player_bets
      }
    end)
  end

  defp get_total_bets_for_symbol(current_bets, symbol) do
    current_bets
    |> Enum.reduce(0, fn {_player_id, player_bets}, acc ->
      acc + Map.get(player_bets, symbol, 0)
    end)
  end

  # 编码筹码增量信息 - 参考LongHu的实现
  defp encode_bet_for_sync(amount, area) do
    # 获取筹码面额配置
    bet_chips = JhandiMundaConstants.bet_chips()

    # 找到对应的筹码面额索引
    chip_index = find_chip_index(amount, bet_chips)

    # 使用位运算编码，参考前端 onBetSYNC 的解码逻辑
    # tabArr: [0, 4, 8, 12, 16, 20] - 每个筹码面值占4位
    if chip_index >= 0 and chip_index < 6 do
      # 将筹码数量编码到对应位置
      1 <<< (chip_index * 4)
    else
      0
    end
  end

  # 查找筹码面额对应的索引
  defp find_chip_index(amount, bet_chips) do
    bet_chips
    |> Enum.with_index()
    |> Enum.find_value(-1, fn {chip_value, index} ->
      if chip_value == amount, do: index, else: nil
    end)
  end

  # 获取玩家座位ID
  defp get_player_seat_id(state, player_id) do
    # 使用新的座位系统：从 game_data.player_seats 中获取座位号
    Map.get(state.game_data.player_seats, player_id, 0)
  end

  # 构建所有区域的总下注信息
  defp build_all_bets_info(current_bets) do
    # 计算每个区域的总下注
    1..6
    |> Enum.reduce(%{}, fn area, acc ->
      total = get_total_bets_for_symbol(current_bets, area)
      Map.put(acc, to_string(area), total)
    end)
  end

  # 构建玩家下注数据 - 用于mybet字段
  defp build_player_bet_data(player_bets) do
    # 返回玩家在各个区域的下注映射 %{"1" => amount1, "2" => amount2, ...}
    player_bets
    |> Enum.reduce(%{}, fn {symbol, amount}, acc ->
      Map.put(acc, to_string(symbol), amount)
    end)
  end

  # 计算剩余下注限制
  defp calculate_remaining_bet_limits(state) do
    # 简单实现：返回每个区域的剩余可下注额度
    # 这里可以根据实际需求实现更复杂的限制逻辑
    max_bet = JhandiMundaConstants.max_bet()

    1..6
    |> Enum.reduce(%{}, fn area, acc ->
      current_total = get_total_bets_for_symbol(state.game_data.current_bets, area)
      remaining = max(0, max_bet - current_total)
      Map.put(acc, to_string(area), remaining)
    end)
  end

  # 获取玩家的numeric_id
  defp get_player_numeric_id(state, player_id) do
    case Map.get(state.players, player_id) do
      # 如果找不到玩家，返回player_id
      nil -> player_id
      player -> player.numeric_id
    end
  end

  # ==================== 积分处理函数 (参考SlotCat实现) ====================

  # 注意：get_player_points, add_player_points, subtract_player_points
  # 这些函数已经在RoomBase中定义，我们直接使用即可
  #
  # 积分获取和处理完全参考SlotCat的实现方式：
  # - numeric_id就是player_id，user_id是UUID
  # - 机器人玩家(numeric_id < 0)使用虚拟积分
  # - 真实玩家使用PlayerData.get_points()通过RoomBase系统查询
  # - 完全不直接调用Cypridina.Accounts.get_user_points，而是使用RoomBase的积分管理

  # 兼容性函数 - 检查玩家余额 (完全参考SlotCat实现，使用RoomBase积分系统)
  defp check_player_balance(state, player_id, amount) do
    # 按照SlotCat的逻辑，player_id应该就是numeric_id
    # 如果player_id是UUID，则需要转换为numeric_id
    numeric_id =
      if is_integer(player_id) do
        player_id
      else
        case get_numeric_id(player_id) do
          {:ok, id} -> id
          {:error, _} -> player_id
        end
      end

    # 使用RoomBase的get_player_points函数，完全参考SlotCat
    current_balance = get_player_points(state, numeric_id)

    if current_balance >= amount do
      {:ok, current_balance}
    else
      {:error, :insufficient_balance}
    end
  end

  # 兼容性函数 - 扣除玩家金币
  defp deduct_player_money(player_id, amount) do
    # 这个函数需要在state上下文中调用，暂时返回成功
    # 实际扣除在execute_bet中通过subtract_player_points完成
    {:ok, amount}
  end

  # 兼容性函数 - 增加玩家金币
  defp add_player_money(player_id, amount) do
    # 这个函数需要在state上下文中调用，暂时返回成功
    # 实际增加在apply_settlement_results中通过add_player_points完成
    {:ok, amount}
  end

  # 兼容性函数 - 获取玩家余额 (完全参考SlotCat，使用RoomBase积分系统)
  defp get_player_balance(state, player_id) do
    # 按照SlotCat的逻辑，player_id应该就是numeric_id
    numeric_id =
      if is_integer(player_id) do
        player_id
      else
        case get_numeric_id(player_id) do
          {:ok, id} ->
            id

          {:error, _} ->
            Logger.error("🎲 [JHANDI_MUNDA] 获取numeric_id失败: #{player_id}")
            0
        end
      end

    # 使用RoomBase的get_player_points函数，完全参考SlotCat
    get_player_points(state, numeric_id)
  end

  defp get_player_display_name(state, player_id) do
    {:ok, numeric_id} = get_numeric_id(player_id)

    case Map.get(state.players, numeric_id) do
      nil -> "玩家#{numeric_id}"
      player_data -> get_safe_display_name(player_data)
    end
  end

  # 安全获取玩家显示名称，支持新旧格式
  defp get_safe_display_name(player) do
    cond do
      # 新格式：PlayerData结构体
      is_struct(player, Cypridina.Teen.GameSystem.PlayerData) ->
        Cypridina.Teen.GameSystem.PlayerData.get_display_name(player)

      # 旧格式：Map格式，有user字段
      is_map(player) and Map.has_key?(player, :user) ->
        Map.get(player.user, :nickname, "玩家#{Map.get(player, :numeric_id, 0)}")

      # 旧格式：Map格式，直接有nickname字段
      is_map(player) and Map.has_key?(player, :nickname) ->
        Map.get(player, :nickname, "玩家#{Map.get(player, :numeric_id, 0)}")

      # 其他情况
      true ->
        numeric_id = Map.get(player, :numeric_id, 0)
        "玩家#{numeric_id}"
    end
  end

  defp get_numeric_id(user_id) do
    try do
      if is_integer(user_id) do
        {:ok, user_id}
      else
        case Ash.get(Cypridina.Accounts.User, user_id) do
          {:ok, user} ->
            {:ok, user.numeric_id}

          {:error, reason} ->
            Logger.warning(
              "⚠️ [GET_NUMERIC_ID] 获取用户numeric_id失败 - user_id: #{user_id}, 原因: #{inspect(reason)}"
            )

            virtual_numeric_id = generate_virtual_numeric_id(user_id)
            {:ok, virtual_numeric_id}
        end
      end
    rescue
      error ->
        Logger.error(
          "❌ [GET_NUMERIC_ID] 获取numeric_id异常 - user_id: #{user_id}, 错误: #{inspect(error)}"
        )

        virtual_numeric_id = generate_virtual_numeric_id(user_id)
        {:ok, virtual_numeric_id}
    end
  end

  defp generate_virtual_numeric_id(user_id) do
    cond do
      is_binary(user_id) and String.length(user_id) == 36 ->
        user_id
        |> String.replace("-", "")
        |> String.slice(0, 8)
        |> String.to_integer(16)
        |> rem(100_000_000)
        |> Kernel.+(10_000_000)

      is_binary(user_id) ->
        hash = :erlang.phash2(user_id, 100_000_000)
        hash + 10_000_000

      is_integer(user_id) ->
        user_id

      true ->
        :rand.uniform(90_000_000) + 10_000_000
    end
  end

  # 断线重连相关辅助函数

  defp get_all_bets_array(current_bets) do
    # 返回每个符号位置的总下注数组 [symbol1_total, symbol2_total, ..., symbol6_total]
    Enum.map(1..6, fn symbol ->
      get_total_bets_for_symbol(current_bets, symbol)
    end)
  end

  defp get_player_bets_map(current_bets, player_id) do
    # 返回玩家在各个符号上的下注映射 %{"1" => amount1, "2" => amount2, ...}
    case Map.get(current_bets, player_id) do
      nil ->
        %{}

      player_bets ->
        player_bets
        |> Enum.reduce(%{}, fn {symbol, amount}, acc ->
          Map.put(acc, to_string(symbol), amount)
        end)
    end
  end

  defp get_symbol_odds() do
    # 返回各个符号的赔率数组
    # JhandiMunda 游戏中，所有符号的赔率都是1:1（即赔率为2）
    [2, 2, 2, 2, 2, 2]
  end

  defp send_to_player_by_id(state, player_id, message) do
    # 根据 player_id 找到对应的玩家对象
    case Map.get(state.players, player_id) do
      nil ->
        Logger.warning("🎲 [JHANDI_MUNDA] 玩家不在房间中: #{player_id}")
        # 🔥 修复：确保总是返回state
        state

      player ->
        send_to_player(state, player, message)
    end
  end

  # ==================== 玩家管理函数 (参考 LongHu 实现) ====================

  # 添加玩家到状态
  defp add_player_to_state(state, player) do
    %{state | players: Map.put(state.players, player.numeric_id, player)}
  end

  # 从状态中移除玩家
  defp remove_player_from_state(state, player) do
    %{state | players: Map.delete(state.players, player.numeric_id)}
  end

  # 这个函数已经在前面实现了，删除重复定义

  # ==================== 机器人获取和管理 ====================

  @doc """
  从后台管理系统获取机器人并添加到房间
  """
  def ensure_robot_count(state) do
    # 计算需要的机器人数量
    current_real_players = count_real_players(state)
    current_robots = count_robots(state)

    # 根据真实玩家数量动态调整机器人数量
    target_robots = calculate_target_robot_count(current_real_players)
    needed_robots = max(0, target_robots - current_robots)

    Logger.info(
      "🤖 [JHANDI_MUNDA] 机器人管理 - 真实玩家: #{current_real_players}, 当前机器人: #{current_robots}, 目标机器人: #{target_robots}, 需要: #{needed_robots}"
    )

    if needed_robots > 0 do
      add_robots_from_backend(state, needed_robots)
    else
      state
    end
  end

  # 从后台管理系统添加机器人
  defp add_robots_from_backend(state, count) do
    Logger.info("🤖 [JHANDI_MUNDA] 从后台管理系统获取 #{count} 个机器人")

    case SimpleRobotProvider.get_robots_for_game("jhandi_munda", state.id, count) do
      {:ok, robot_players} ->
        Logger.info("🤖 [JHANDI_MUNDA] 成功获取 #{length(robot_players)} 个机器人")

        # 将机器人添加到房间
        new_state =
          Enum.reduce(robot_players, state, fn robot_player, acc_state ->
            add_robot_to_room(acc_state, robot_player)
          end)

        # 广播玩家数量变化
        broadcast_player_count_change(new_state)

      {:error, reason} ->
        Logger.error("🤖 [JHANDI_MUNDA] 获取机器人失败: #{inspect(reason)}")
        state
    end
  end

  # 添加机器人到房间
  defp add_robot_to_room(state, robot_player) do
    Logger.info("🤖 [JHANDI_MUNDA] 添加机器人到房间: #{robot_player.numeric_id}")

    # 将机器人添加到玩家列表
    new_state = %{state | players: Map.put(state.players, robot_player.numeric_id, robot_player)}
    
    # 为机器人分配座位
    state_with_seat = allocate_seat_for_player(new_state, robot_player.numeric_id)

    # 发送玩家加入广播
    broadcast_player_joined(state_with_seat, robot_player)

    state_with_seat
  end

  # 广播玩家加入
  defp broadcast_player_joined(state, player) do
    Logger.info("🎲 [JHANDI_MUNDA] 广播玩家加入: #{player.numeric_id}")

    # 发送玩家加入广播（如果需要的话）
    # 在JHANDI_MUNDA中，玩家列表的更新通过broadcast_player_count_change处理
    state
  end

  # 计算目标机器人数量
  defp calculate_target_robot_count(real_player_count) do
    case real_player_count do
      # 没有真实玩家时不需要机器人
      0 -> 0
      # 1个真实玩家，3个机器人
      1 -> 3
      # 2个真实玩家，2个机器人
      2 -> 2
      # 3个真实玩家，1个机器人
      3 -> 1
      # 4+个真实玩家，不需要机器人
      _ -> 0
    end
  end

  # 统计真实玩家数量
  defp count_real_players(state) do
    state.players
    |> Enum.count(fn {_player_id, player} -> not player.is_robot end)
  end

  # 统计机器人数量
  defp count_robots(state) do
    state.players
    |> Enum.count(fn {_player_id, player} -> player.is_robot end)
  end

  # 广播玩家数量变化 (使用与send_player_list相同的数据格式)
  defp broadcast_player_count_change(state) do
    player_count = map_size(state.players)
    Logger.info("🎲 [JHANDI_MUNDA] 玩家数量变化: #{player_count}")

    # 使用动态玩家列表 (返回数组格式)
    player_list = build_player_list(state, :list)

    # 构建下注排行榜数据（前端桌面显示用）
    betrank_data = build_betrank_data(state)

    # 构建赢分排行榜数据
    winscorerank_data = build_winscorerank_data(state)

    # 广播玩家列表更新 - 使用与send_player_list相同的数据格式
    message = %{
      "mainId" => @main_proto_xc,
      "subId" => JhandiMundaConstants.protocol_player_list_response(),
      "data" => %{
        "totalplayernum" => player_count,
        "betrank" => betrank_data,
        "playerlist" => player_list,
        "winscorerank" => winscorerank_data
      }
    }

    broadcast_to_room(state, message)
    state
  end

  # 这个函数已经在前面实现了，删除重复定义

  # 移除玩家下注
  defp remove_player_bets(state, player_id) do
    # 移除玩家的下注
    new_current_bets = Map.delete(state.game_data.current_bets, player_id)

    # 移除玩家沉默计数
    new_silence_count = Map.delete(state.game_data.player_silence_count, player_id)

    # 更新游戏数据
    new_game_data = %{
      state.game_data
      | current_bets: new_current_bets,
        player_silence_count: new_silence_count
    }

    %{state | game_data: new_game_data}
  end

  # 如果房间为空则重置
  defp maybe_reset_room_if_empty(state) do
    # 如果房间没有玩家了，重置房间状态
    if map_size(state.players) == 0 do
      reset_room_to_waiting(state)
    else
      state
    end
  end

  # ==================== 数据构建辅助函数 ====================

  # 游戏状态转换为数字（对应 C++ 枚举）
  defp game_state_to_number(game_state) do
    case game_state do
      # EM_YXX_GAMESTATE_NONE
      0 -> 0
      # EM_YXX_GAMESTATE_BUYHORSE
      1 -> 1
      # EM_YXX_GAMESTATE_COMBINE
      2 -> 2
      _ -> 0
    end
  end

  # 构建游戏配置信息
  defp build_game_config do
    %{
      # 所有符号的赔率都是1:1（即赔率为2）
      "odds" => [2, 2, 2, 2, 2, 2],
      # 使用 bet_chips 而不是 bet_amounts
      "bet" => JhandiMundaConstants.bet_chips(),
      "betneed" => JhandiMundaConstants.min_bet(),
      "betmax" => JhandiMundaConstants.max_bet()
    }
  end

  # 构建全部下注信息
  defp build_all_bet_info(state) do
    # 统计每个位置的总下注
    total_bets =
      Enum.reduce(state.game_data.current_bets, %{}, fn {_player_id, player_bets}, acc ->
        Enum.reduce(player_bets, acc, fn {symbol, amount}, inner_acc ->
          Map.update(inner_acc, symbol, amount, &(&1 + amount))
        end)
      end)

    # 转换为前端期望的格式（位置索引 -> 金额）
    JhandiMundaConstants.symbols()
    |> Enum.reduce(%{}, fn {symbol, index}, acc ->
      amount = Map.get(total_bets, symbol, 0)
      Map.put(acc, to_string(index), amount)
    end)
  end

  # 构建玩家下注信息
  defp build_player_bet_info(state, player_id) do
    player_bets = Map.get(state.game_data.current_bets, player_id, %{})

    # 转换为前端期望的格式（位置索引 -> 金额）
    JhandiMundaConstants.symbols()
    |> Enum.reduce(%{}, fn {symbol, index}, acc ->
      amount = Map.get(player_bets, symbol, 0)
      Map.put(acc, to_string(index), amount)
    end)
  end

  # 构建历史记录数据
  defp build_history_data(state) do
    %{
      "reclist" =>
        Enum.map(state.game_data.history.records, fn result ->
          # 转换为前端期望的格式（位置索引 -> 数量）
          JhandiMundaConstants.symbols()
          |> Enum.reduce(%{}, fn {symbol, index}, acc ->
            # 修复：使用 symbol_counts 而不是 result
            count = Map.get(result.symbol_counts, symbol, 0)
            Map.put(acc, to_string(index), count)
          end)
        end)
    }
  end

  # 构建下注限制信息
  defp build_bet_limit_info(_state) do
    # TODO: 实现下注限制逻辑
    JhandiMundaConstants.symbols()
    |> Enum.reduce(%{}, fn {_symbol, index}, acc ->
      Map.put(acc, to_string(index), JhandiMundaConstants.max_bet())
    end)
  end

  # 构建开奖结果信息
  defp build_result_info(state) do
    case state.game_data.game_result do
      nil ->
        %{}

      result ->
        # 转换为前端期望的格式（位置索引 -> 数量）
        JhandiMundaConstants.symbols()
        |> Enum.reduce(%{}, fn {symbol, index}, acc ->
          count = Map.get(result, symbol, 0)
          Map.put(acc, to_string(index), count)
        end)
    end
  end

  # 构建结算信息
  defp build_settlement_info(_state) do
    # TODO: 实现结算信息逻辑
    %{}
  end

  # ==================== 缺失的辅助函数 ====================

  # 发送完整游戏配置给玩家（对应 C++ GetSVar 函数）
  defp send_game_config_to_player(state, player_id) do
    # 构建游戏配置数据（参考 C++ GetSVar 实现）
    config_data = %{
      # 底分
      "difen" => JhandiMundaConstants.min_bet(),
      # 游戏状态
      "state" => game_state_to_number(state.game_data.game_state),
      # 游戏配置
      "config" => build_game_config(),
      # 剩余时间
      "waittime" => get_remaining_time(state),
      # 全部下注信息
      "allbet" => build_all_bet_info(state),
      # 玩家下注信息
      "mybet" => build_player_bet_info(state, player_id),
      # 历史记录
      "data" => build_history_data(state),
      # 回合ID
      "roundid" => state.game_data.round_id
    }

    # 根据游戏状态添加额外信息
    config_data =
      case state.game_data.game_state do
        # game_state_betting
        1 ->
          # 下注阶段：添加下注限制信息
          Map.put(config_data, "buylimit", build_bet_limit_info(state))

        # game_state_revealing
        2 ->
          # 开奖阶段：添加开奖结果和结算信息
          config_data
          |> Map.put("posnums", build_result_info(state))
          |> Map.put("other", build_settlement_info(state))

        _ ->
          config_data
      end

    message = %{
      "mainId" => @main_proto_xc,
      "subId" => JhandiMundaConstants.protocol_config(),
      "data" => config_data
    }

    send_to_player_by_id(state, player_id, message)
  end

  # 发送房间状态给玩家
  defp send_room_state_to_player(state, player_id) do
    player_money = get_player_balance(state, player_id)

    message = %{
      "mainId" => 4,
      "subId" => 3,
      "data" => %{
        "status" => state.game_data.game_state,
        "round" => state.game_data.round_id,
        "money" => player_money,
        "time_left" => state.game_data.time_left
      }
    }

    send_to_player_by_id(state, player_id, message)

    Logger.info(
      "📤 [SEND_ROOM_STATE] 发送房间状态给玩家 - 用户: #{player_id}, 状态: #{state.game_data.game_state}"
    )
  end

  # 获取剩余时间（秒）
  defp get_remaining_time(state) do
    state.game_data.time_left
  end

  # ==================== 庄家系统函数 ====================

  # 计算庄家盈亏
  defp calculate_banker_result(state, settlement_results) do
    # 庄家与所有闲家对赌
    # 庄家赢得 = 所有闲家输的钱（下注但没中奖的钱）
    # 庄家输掉 = 所有闲家赢的钱（中奖金额 - 下注金额）

    total_player_bets = calculate_total_player_bets(state.game_data.current_bets)
    total_player_wins = calculate_total_player_wins(settlement_results)

    # 庄家赢得的净额
    banker_win = total_player_bets - total_player_wins
    banker_lose = if banker_win < 0, do: abs(banker_win), else: 0
    banker_win = if banker_win > 0, do: banker_win, else: 0

    Logger.info(
      "🏦 [BANKER] 庄家结算: 总下注=#{total_player_bets}, 总赔付=#{total_player_wins}, 庄家净赢=#{banker_win - banker_lose}"
    )

    %{
      win_amount: banker_win,
      lose_amount: banker_lose,
      net_amount: banker_win - banker_lose,
      total_player_bets: total_player_bets,
      total_player_wins: total_player_wins
    }
  end

  # 计算所有玩家的总下注金额
  defp calculate_total_player_bets(current_bets) do
    current_bets
    |> Enum.reduce(0, fn {_player_id, player_bets}, acc ->
      player_total = player_bets |> Map.values() |> Enum.sum()
      acc + player_total
    end)
  end

  # 计算所有玩家的总中奖金额
  defp calculate_total_player_wins(settlement_results) do
    settlement_results
    |> Enum.reduce(0, fn {_player_id, result}, acc ->
      acc + result.total_win
    end)
  end

  # 更新庄家结果
  defp update_banker_result(state, banker_result) do
    new_banker = %{
      state.game_data.banker
      | win_amount: banker_result.win_amount,
        lose_amount: banker_result.lose_amount
    }

    new_game_data = %{state.game_data | banker: new_banker}
    %{state | game_data: new_game_data}
  end

  # 获取庄家信息
  defp get_banker_info(state) do
    banker = state.game_data.banker

    case banker.type do
      :system ->
        %{
          "type" => "system",
          "playerid" => 0,
          "name" => "系统庄家",
          "money" => banker.system_money,
          "win_amount" => banker.win_amount,
          "lose_amount" => banker.lose_amount,
          "net_amount" => banker.win_amount - banker.lose_amount
        }

      :player ->
        # 如果是玩家庄家，获取玩家信息
        if banker.player_id do
          player_balance = get_player_balance(state, banker.player_id)
          player_name = get_player_display_name(state, banker.player_id)

          %{
            "type" => "player",
            "playerid" => banker.player_id,
            "name" => player_name,
            "money" => player_balance,
            "win_amount" => banker.win_amount,
            "lose_amount" => banker.lose_amount,
            "net_amount" => banker.win_amount - banker.lose_amount
          }
        else
          # 如果没有玩家庄家，回退到系统庄
          %{
            "type" => "system",
            "playerid" => 0,
            "name" => "系统庄家",
            "money" => banker.system_money,
            "win_amount" => banker.win_amount,
            "lose_amount" => banker.lose_amount,
            "net_amount" => banker.win_amount - banker.lose_amount
          }
        end
    end
  end

  # ==================== 机器人管理相关函数 ====================

  # 注意：自动添加机器人功能已移除
  # 现在只使用后台管理手动创建的机器人

  # 玩家离开后管理机器人
  defp maybe_manage_robots_after_player_leave(state) do
    state
    # 清理积分不足的机器人
    |> cleanup_insufficient_funds_robots()
    # 确保机器人数量
    |> ensure_robot_count()
  end

  @doc """
  机器人轮换管理
  """
  def manage_robot_rotation(state) do
    # 找出需要轮换的机器人
    robots_to_rotate = find_robots_to_rotate(state)

    if length(robots_to_rotate) > 0 do
      Logger.info("🤖 [JHANDI_MUNDA] 需要轮换机器人: #{inspect(robots_to_rotate)}")

      # 移除需要轮换的机器人
      new_state =
        Enum.reduce(robots_to_rotate, state, fn robot_id, acc_state ->
          robot_player = Map.get(acc_state.players, robot_id)

          if robot_player do
            # 更新机器人状态为空闲
            RobotStateManager.update_robot_status(robot_id, :idle, %{
              current_room_id: nil,
              current_game_type: nil
            })

            # 释放机器人的座位
            state_after_seat_dealloc = deallocate_seat_for_player(acc_state, robot_id)
            
            # 从房间移除
            %{state_after_seat_dealloc | players: Map.delete(state_after_seat_dealloc.players, robot_id)}
          else
            acc_state
          end
        end)

      # 确保机器人数量
      ensure_robot_count(new_state)
    else
      state
    end
  end

  # 找出需要轮换的机器人
  defp find_robots_to_rotate(state) do
    current_time = DateTime.utc_now()
    # 30分钟，毫秒
    max_robot_age = 30 * 60 * 1000

    state.players
    |> Enum.filter(fn {_player_id, player} ->
      if player.is_robot do
        # 检查机器人是否在游戏中（下注阶段不能踢出）
        in_betting = state.game_data.game_state == JhandiMundaConstants.game_state_betting()
        has_bet = Map.has_key?(state.game_data.current_bets, player.numeric_id)

        # 如果在下注阶段且已下注，不能轮换
        if in_betting and has_bet do
          false
        else
          # 检查机器人年龄
          robot_age =
            case Map.get(player.user, :created_at) do
              nil -> 0
              created_at -> DateTime.diff(current_time, created_at, :millisecond)
            end

          # 30分钟后有概率轮换
          robot_age > max_robot_age and :rand.uniform() < 0.1
        end
      else
        false
      end
    end)
    |> Enum.map(fn {player_id, _player} -> player_id end)
  end

  # 启动机器人下注
  defp start_robot_betting(state) do
    robot_players = get_robot_players(state)

    Logger.info("🤖 [JHANDI_MUNDA] 房间内总玩家数: #{map_size(state.players)}")
    Logger.info("🤖 [JHANDI_MUNDA] 机器人玩家列表: #{inspect(robot_players)}")

    if length(robot_players) == 0 do
      Logger.warning("🤖 [JHANDI_MUNDA] 警告：房间内没有机器人！")
    end

    # 让所有机器人进入下注回合
    Enum.each(robot_players, fn robot_id ->
      RobotStateManager.robot_enter_round(robot_id)
    end)

    # 启动机器人下注决策
    Enum.each(robot_players, fn robot_id ->
      Logger.info("🤖 [JHANDI_MUNDA] 启动机器人 #{robot_id} 的下注决策")
      # 使用现有的机器人管理器执行下注逻辑
      JhandiMundaRobotManager.execute_robot_betting(state, robot_id, self())
      Logger.info("🤖 [JHANDI_MUNDA] 机器人 #{robot_id} 下注决策已启动")
    end)

    Logger.info("🤖 [JHANDI_MUNDA] 启动了 #{length(robot_players)} 个机器人的下注决策")
  end

  # 执行机器人下注
  defp execute_robot_bets(state, robot_id, bet_map) do
    # 检查游戏状态
    if state.game_data.game_state != JhandiMundaConstants.game_state_betting() do
      Logger.warning("🤖 [JHANDI_MUNDA] 机器人下注时游戏不在下注阶段: #{state.game_data.game_state}")
      state
    else
      Logger.info("🤖 [JHANDI_MUNDA] 开始执行机器人 #{robot_id} 的下注，下注映射: #{inspect(bet_map)}")

      # 逐个执行下注
      final_state =
        Enum.reduce(bet_map, state, fn {symbol, amount}, acc_state ->
          Logger.info("🤖 [JHANDI_MUNDA] 处理机器人 #{robot_id} 在符号 #{symbol} 下注 #{amount}")

          case process_robot_bet(acc_state, robot_id, symbol, amount) do
            {:ok, updated_state} ->
              Logger.info("✅ [JHANDI_MUNDA] 机器人 #{robot_id} 在符号 #{symbol} 下注 #{amount} 成功")
              updated_state

            {:error, reason} ->
              Logger.warning(
                "❌ [JHANDI_MUNDA] 机器人 #{robot_id} 在符号 #{symbol} 下注 #{amount} 失败: #{reason}"
              )

              acc_state
          end
        end)

      Logger.info("🤖 [JHANDI_MUNDA] 机器人 #{robot_id} 所有下注处理完成")
      final_state
    end
  end

  # 统计房间内机器人数量
  defp count_robots(state) do
    state.players
    |> Enum.count(fn {_id, player} -> player.is_robot end)
  end

  # 统计房间内真实玩家数量
  defp count_human_players(state) do
    state.players
    |> Enum.count(fn {_id, player} -> not player.is_robot end)
  end

  # 注意：calculate_target_robot_count 函数已移除
  # 不再自动计算和添加机器人

  # 注意：broadcast_robots_joined 函数已移除
  # 不再自动广播机器人加入消息

  # 清理积分不足的机器人
  defp cleanup_insufficient_funds_robots(state) do
    Logger.info("🤖 [JHANDI_MUNDA] 清理积分不足的机器人")

    insufficient_robots =
      state.players
      |> Enum.filter(fn {_id, player} ->
        player.is_robot and player.user.points < state.game_data.config.min_bet
      end)
      |> Enum.map(fn {id, _player} -> id end)

    if length(insufficient_robots) > 0 do
      Logger.info("🤖 [JHANDI_MUNDA] 发现 #{length(insufficient_robots)} 个积分不足的机器人")

      # 清理这些机器人
      updated_state =
        Enum.reduce(insufficient_robots, state, fn robot_id, acc_state ->
          Logger.info("🤖 [JHANDI_MUNDA] 清理积分不足的机器人: #{robot_id}")

          # 标记机器人为积分不足状态
          RobotStateManager.check_insufficient_funds(robot_id, acc_state.game_data.config.min_bet)

          # 释放机器人的座位
          state_after_seat_dealloc = deallocate_seat_for_player(acc_state, robot_id)
          
          # 从房间中移除
          remove_player_from_state(state_after_seat_dealloc, %{numeric_id: robot_id})
        end)

      updated_state
    else
      state
    end
  end

  # 获取房间内所有机器人玩家ID列表
  defp get_robot_players(state) do
    state.players
    |> Enum.filter(fn {_id, player} -> player.is_robot end)
    |> Enum.map(fn {id, _player} -> id end)
  end

  # 处理机器人下注
  defp process_robot_bet(state, robot_id, symbol, amount) do
    Logger.debug("🤖 [PROCESS_ROBOT_BET] 开始处理机器人 #{robot_id} 下注 - 符号: #{symbol}, 金额: #{amount}")

    # 验证下注
    case JhandiMundaGameLogic.validate_bet(symbol, amount) do
      :ok ->
        # 检查机器人积分
        robot_points = get_player_points(state, robot_id)
        Logger.debug("🤖 [PROCESS_ROBOT_BET] 机器人 #{robot_id} 当前积分: #{robot_points}")

        if robot_points >= amount do
          Logger.info("🤖 [PROCESS_ROBOT_BET] 机器人 #{robot_id} 积分充足，开始扣除积分并记录下注")

          # 扣除积分并记录下注
          new_state =
            subtract_player_points(state, robot_id, amount)
            |> add_player_bet(robot_id, symbol, amount)
            |> broadcast_robot_bet_sync(robot_id, symbol, amount)

          Logger.info("🤖 [PROCESS_ROBOT_BET] 机器人 #{robot_id} 下注处理完成，已广播筹码增量")
          {:ok, new_state}
        else
          Logger.warning(
            "🤖 [PROCESS_ROBOT_BET] 机器人 #{robot_id} 积分不足: #{robot_points} < #{amount}"
          )

          {:error, :insufficient_points}
        end

      error ->
        Logger.warning("🤖 [PROCESS_ROBOT_BET] 机器人 #{robot_id} 下注验证失败: #{inspect(error)}")
        error
    end
  end

  # 广播机器人下注同步 - 使用与真实玩家相同的筹码增量格式
  defp broadcast_robot_bet_sync(state, robot_id, symbol, amount) do
    # 获取机器人信息
    robot_player = Map.get(state.players, robot_id)

    # 重要：使用更新后的state获取机器人余额，确保余额是扣除下注后的最新值
    robot_balance = get_player_balance(state, robot_id)

    # 编码筹码增量信息 - 与真实玩家下注保持一致
    encoded_bet_amount = encode_bet_for_sync(amount, symbol)

    # 构建机器人下注增量信息 - 与真实玩家格式完全一致
    player_bet_info = %{
      "playerid" => robot_id,
      "seatid" => get_player_seat_id(state, robot_id),
      "betnum" => %{
        to_string(symbol) => encoded_bet_amount
      },
      # 这里是扣除下注后的余额
      "chouma" => robot_balance
    }

    # 构建所有区域的总下注信息 - 包含机器人的下注
    all_bets = build_all_bets_info(state.game_data.current_bets)

    message = %{
      "mainId" => @main_proto_xc,
      "subId" => JhandiMundaConstants.protocol_bet_sync(),
      "data" => %{
        "playerlist" => %{
          to_string(robot_id) => player_bet_info
        },
        "roundid" => state.game_data.round_id,
        "allbet" => all_bets
      }
    }

    Logger.info("🤖 [ROBOT_BET_SYNC] 机器人筹码增量广播 - 机器人: #{robot_id}, 区域: #{symbol}, 金额: #{amount}")
    Logger.info("🤖 [ROBOT_BET_SYNC] 编码后的筹码数据: #{inspect(encoded_bet_amount)}")
    Logger.info("🤖 [ROBOT_BET_SYNC] 机器人余额: #{robot_balance}, 所有区域下注: #{inspect(all_bets)}")

    # 使用标准的房间广播函数，与其他协议保持一致
    broadcast_to_room(state, message)
    state
  end

  # 获取玩家总下注金额
  defp get_player_total_bets(state, player_id) do
    player_bets = Map.get(state.game_data.current_bets, player_id, %{})
    Enum.reduce(player_bets, 0, fn {_symbol, amount}, acc -> acc + amount end)
  end

  # 添加玩家下注记录
  defp add_player_bet(state, player_id, symbol, amount) do
    current_bets = state.game_data.current_bets
    player_bets = Map.get(current_bets, player_id, %{})
    updated_player_bets = Map.update(player_bets, symbol, amount, &(&1 + amount))
    updated_current_bets = Map.put(current_bets, player_id, updated_player_bets)

    new_game_data = %{state.game_data | current_bets: updated_current_bets}
    %{state | game_data: new_game_data}
  end

  # 更新机器人AI统计数据
  defp update_robot_ai_statistics(state, settlement_results) do
    # 遍历所有结算结果，更新机器人的AI统计
    Enum.reduce(settlement_results, state, fn {player_id, result}, acc_state ->
      case get_numeric_id(player_id) do
        {:ok, numeric_id} ->
          player = Map.get(state.players, numeric_id)

          if player != nil and is_robot?(player) do
            # 判断机器人是赢还是输
            ai_result = if result.net_win > 0, do: :win, else: :loss

            # 更新机器人AI统计数据
            JhandiMundaRobotManager.update_robot_ai_stats(acc_state, numeric_id, ai_result)
          else
            acc_state
          end

        {:error, _} ->
          acc_state
      end
    end)
  end

  # 工具函数：判断是否为机器人（使用机器人管理器的函数）
  defp is_robot?(player), do: JhandiMundaRobotManager.is_robot?(player)

  # 从win_details中提取中奖符号
  defp extract_winning_symbols_from_details(win_details) do
    win_details
    |> Enum.filter(fn {_symbol, details} ->
      # 只保留有中奖的符号（win > 0）
      Map.get(details, :win, 0) > 0
    end)
    |> Enum.map(fn {symbol, _details} -> symbol end)
    |> Enum.sort()
  end

  # 更新玩家统计数据
  defp update_player_stats(state, numeric_id, result) do
    current_stats =
      Map.get(state.game_data.player_stats, numeric_id, %{
        games_played: 0,
        total_win_amount: 0
      })

    # 计算净赢利（总中奖 - 总下注）
    net_win = result.total_win - result.total_bet

    new_stats = %{
      games_played: current_stats.games_played + 1,
      total_win_amount: current_stats.total_win_amount + net_win
    }

    new_player_stats = Map.put(state.game_data.player_stats, numeric_id, new_stats)
    new_game_data = %{state.game_data | player_stats: new_player_stats}

    Logger.info(
      "📊 [PLAYER_STATS] 更新玩家统计 - 玩家: #{numeric_id}, 局数: #{new_stats.games_played}, 累计赢分: #{new_stats.total_win_amount}"
    )

    %{state | game_data: new_game_data}
  end

  # 收集大赢家信息
  defp collect_big_winners(state, settlement_results) do
    # 设置大赢家的最低中奖金额阈值（可以根据需要调整）
    # 最低下注的10倍
    min_win_threshold = JhandiMundaConstants.min_bet() * 10

    settlement_results
    |> Enum.filter(fn {_player_id, result} ->
      result.total_win > min_win_threshold
    end)
    |> Enum.map(fn {player_id, result} ->
      # 获取玩家信息
      case get_numeric_id(player_id) do
        {:ok, numeric_id} ->
          player = Map.get(state.players, numeric_id)

          if player do
            # 从win_details中提取中奖符号
            winning_symbols = extract_winning_symbols_from_details(result.win_details)

            %{
              player_id: numeric_id,
              player_name: get_safe_display_name(player),
              head_id: get_player_head_id(player),
              bet_amount: result.total_bet,
              win_amount: result.total_win,
              net_win: result.net_win,
              winning_symbols: winning_symbols
            }
          else
            nil
          end

        {:error, _} ->
          nil
      end
    end)
    # 过滤掉nil值
    |> Enum.filter(&(&1 != nil))
    # 按中奖金额降序排列
    |> Enum.sort_by(& &1.win_amount, :desc)
    # 只保留前10名大赢家
    |> Enum.take(10)
  end

  # ==================== 1010协议数据构建函数 ====================

  # 构建下注排行榜数据（前端桌面显示用）- 百人场座位系统
  defp build_betrank_data(state) do
    # 获取6个座位的玩家数据，按座位号排序
    1..6
    |> Enum.reduce(%{}, fn seat_number, acc ->
      case Map.get(state.game_data.seats, seat_number) do
        nil ->
          # 空座位，不添加到betrank
          acc

        player_id ->
          # 获取座位上的玩家信息
          case Map.get(state.players, player_id) do
            nil ->
              # 玩家不存在，不添加
              acc

            player ->
              # 计算玩家当前回合的总下注
              total_bet =
                case Map.get(state.game_data.current_bets, player_id) do
                  nil ->
                    0

                  player_bets ->
                    player_bets
                    |> Map.values()
                    |> Enum.sum()
                end

              # 获取玩家的真实统计数据
              player_stats =
                Map.get(state.game_data.player_stats, player_id, %{
                  games_played: 0,
                  total_win_amount: 0
                })

              player_data = %{
                playerid: player.numeric_id,
                name: get_safe_display_name(player),
                coin: get_player_balance(state, player_id),
                headid: get_player_head_id(player),
                # 前端显示为分数（累计赢分）
                winnum: player_stats.total_win_amount / 100,
                # 前端显示为局数（游戏局数）
                winscore: player_stats.games_played * 100
              }

              # 使用座位号作为betrank的key（座位1对应betrank["1"]）
              Map.put(acc, to_string(seat_number), player_data)
          end
      end
    end)
  end

  # 构建赢分排行榜数据（大赢家列表）
  defp build_winscorerank_data(state) do
    # 从历史记录中获取大赢家数据
    history_records = JhandiMundaHistory.get_recent_records(state.game_data.history, 20)

    # 收集所有历史记录中的大赢家
    historical_big_winners =
      history_records
      |> Enum.flat_map(fn record ->
        Map.get(record, :big_winners, [])
      end)

    # 如果历史记录中没有大赢家数据，使用当前房间玩家的统计数据
    big_winners_data =
      if Enum.empty?(historical_big_winners) do
        build_current_players_winscorerank(state)
      else
        historical_big_winners
        # 按中奖金额降序排列
        |> Enum.sort_by(& &1.win_amount, :desc)
        # 取前10名
        |> Enum.take(10)
        # 添加排名索引
        |> Enum.with_index(1)
        |> Enum.reduce(%{}, fn {winner, rank}, acc ->
          # 格式化为前端期望的数据格式
          winner_data = %{
            playerid: winner.player_id,
            name: winner.player_name,
            headid: Map.get(winner, :head_id, 1),
            # 暂时为空
            wxheadurl: "",
            # 使用当前时间
            time: format_winner_time(),
            # 中奖金额
            winscore: winner.win_amount,
            # 下注金额
            bet: winner.bet_amount,
            # 中奖位置图标
            winpos: get_winner_position_icon(winner.winning_symbols),
            # 中奖倍数
            winodd: calculate_winner_odds(winner.bet_amount, winner.win_amount)
          }

          Map.put(acc, to_string(rank), winner_data)
        end)
      end

    big_winners_data
  end

  # 构建当前玩家的winscorerank数据（当历史记录为空时使用）
  defp build_current_players_winscorerank(state) do
    state.players
    |> Enum.map(fn {player_id, player} ->
      # 获取玩家的统计数据
      player_stats =
        Map.get(state.game_data.player_stats, player_id, %{
          games_played: 0,
          total_win_amount: 0
        })

      # 只显示有正赢分的玩家
      if player_stats.total_win_amount > 0 do
        %{
          playerid: player.numeric_id,
          name: get_safe_display_name(player),
          headid: get_player_head_id(player),
          wxheadurl: "",
          time: format_winner_time(),
          # 累计赢分
          winscore: player_stats.total_win_amount,
          # 默认下注金额
          bet: 1000,
          # 默认位置
          winpos: 1,
          # 计算倍数
          winodd: Float.round(player_stats.total_win_amount / 1000, 1)
        }
      else
        nil
      end
    end)
    # 过滤掉nil值
    |> Enum.filter(&(&1 != nil))
    # 按赢分降序排列
    |> Enum.sort_by(& &1.winscore, :desc)
    # 取前10名
    |> Enum.take(10)
    # 添加排名索引
    |> Enum.with_index(1)
    |> Enum.reduce(%{}, fn {winner_data, rank}, acc ->
      Map.put(acc, to_string(rank), winner_data)
    end)
  end

  # 格式化大赢家时间
  defp format_winner_time() do
    DateTime.utc_now()
    |> DateTime.to_time()
    |> Time.to_string()
    # 只显示 HH:MM
    |> String.slice(0, 5)
  end

  # 获取中奖位置图标
  defp get_winner_position_icon(winning_symbols) do
    # 返回第一个中奖符号作为图标
    case winning_symbols do
      [first_symbol | _] -> first_symbol
      # 默认图标
      [] -> 1
    end
  end

  # 计算中奖倍数
  defp calculate_winner_odds(bet_amount, win_amount) do
    if bet_amount > 0 do
      Float.round(win_amount / bet_amount, 1)
    else
      0
    end
  end

  # 获取玩家头像ID
  defp get_player_head_id(player) do
    cond do
      # 新格式：PlayerData结构体
      is_struct(player, Cypridina.Teen.GameSystem.PlayerData) ->
        # 如果PlayerData有头像字段，使用它，否则使用默认值
        Map.get(player, :head_id, 1)

      # 旧格式：Map格式，有user字段
      is_map(player) and Map.has_key?(player, :user) ->
        Map.get(player.user, :head_id, 1)

      # 旧格式：Map格式，直接有head_id字段
      is_map(player) and Map.has_key?(player, :head_id) ->
        Map.get(player, :head_id, 1)

      # 机器人或其他情况，使用随机头像
      true ->
        # 随机1-10的头像ID
        :rand.uniform(10)
    end
  end

  # ==================== 百人场座位管理系统 ====================

  # 分配座位给新加入的玩家
  defp allocate_seat_for_player(state, player_id) do
    # 查找第一个空闲的座位（1-6）
    empty_seat = find_empty_seat(state.game_data.seats)

    case empty_seat do
      nil ->
        # 没有空座位，加入等待队列
        Logger.info("🪑 [SEAT] 座位已满，玩家 #{player_id} 加入等待队列")
        new_waiting_queue = state.game_data.waiting_queue ++ [player_id]
        new_game_data = %{state.game_data | waiting_queue: new_waiting_queue}
        %{state | game_data: new_game_data}

      seat_number ->
        # 分配座位
        Logger.info("🪑 [SEAT] 为玩家 #{player_id} 分配座位 #{seat_number}")
        new_seats = Map.put(state.game_data.seats, seat_number, player_id)
        new_player_seats = Map.put(state.game_data.player_seats, player_id, seat_number)

        new_game_data = %{
          state.game_data
          | seats: new_seats,
            player_seats: new_player_seats
        }

        %{state | game_data: new_game_data}
    end
  end

  # 释放玩家的座位并重新分配
  defp deallocate_seat_for_player(state, player_id) do
    case Map.get(state.game_data.player_seats, player_id) do
      nil ->
        # 玩家没有座位，可能在等待队列中，从队列中移除
        Logger.info("🪑 [SEAT] 玩家 #{player_id} 没有座位，从等待队列中移除")
        new_waiting_queue = List.delete(state.game_data.waiting_queue, player_id)
        new_game_data = %{state.game_data | waiting_queue: new_waiting_queue}
        %{state | game_data: new_game_data}

      seat_number ->
        # 释放座位
        Logger.info("🪑 [SEAT] 释放玩家 #{player_id} 的座位 #{seat_number}")
        new_seats = Map.put(state.game_data.seats, seat_number, nil)
        new_player_seats = Map.delete(state.game_data.player_seats, player_id)

        # 如果有等待的玩家，分配座位给队列中的第一个玩家
        {next_player, new_waiting_queue} =
          case state.game_data.waiting_queue do
            [next_player_id | rest] ->
              Logger.info("🪑 [SEAT] 为等待玩家 #{next_player_id} 分配座位 #{seat_number}")
              {next_player_id, rest}

            [] ->
              {nil, []}
          end

        final_seats =
          if next_player do
            Map.put(new_seats, seat_number, next_player)
          else
            new_seats
          end

        final_player_seats =
          if next_player do
            Map.put(new_player_seats, next_player, seat_number)
          else
            new_player_seats
          end

        new_game_data = %{
          state.game_data
          | seats: final_seats,
            player_seats: final_player_seats,
            waiting_queue: new_waiting_queue
        }

        %{state | game_data: new_game_data}
    end
  end

  # 查找空闲座位
  defp find_empty_seat(seats) do
    Enum.find(1..6, fn seat_number ->
      Map.get(seats, seat_number) == nil
    end)
  end

  # 获取玩家的座位号，如果没有座位返回nil
  defp get_player_seat_number(state, player_id) do
    Map.get(state.game_data.player_seats, player_id)
  end

  # 获取所有有座位的玩家ID列表
  defp get_seated_players(state) do
    state.game_data.seats
    |> Enum.filter(fn {_seat, player_id} -> player_id != nil end)
    |> Enum.map(fn {_seat, player_id} -> player_id end)
  end

  # 获取等待队列中的玩家数量
  defp get_waiting_count(state) do
    length(state.game_data.waiting_queue)
  end

  # 确保机器人状态在游戏结束时正确更新
  defp ensure_robot_states_updated(state) do
    robot_players =
      state.players
      |> Enum.filter(fn {_id, player} -> is_robot?(player) end)
    
    robot_ids = Enum.map(robot_players, fn {id, _player} -> id end)
    
    if length(robot_ids) > 0 do
      Logger.info("🎲 [JHANDI_MUNDA] 游戏结束，确保 #{length(robot_ids)} 个机器人状态正确")
      
      # 同步处理每个机器人的回收状态
      Enum.each(robot_players, fn {robot_id, _robot} ->
        try do
          Teen.RobotManagement.RobotStateManager.complete_robot_recycling(robot_id, "Jhandi Munda游戏结束")
          Logger.debug("🎲 [JHANDI_MUNDA] 机器人 #{robot_id} 回收状态已更新")
        rescue
          error ->
            Logger.error("🎲 [JHANDI_MUNDA] 机器人 #{robot_id} 回收状态更新失败: #{inspect(error)}")
        end
      end)
    end
    
    state
  end

  # 统一机器人清理 - 使用与Teen Patti相同的标准模式
  defp clear_all_robots(state) do
    robot_players =
      state.players
      |> Enum.filter(fn {_id, player} -> is_robot?(player) end)
    
    robot_ids = Enum.map(robot_players, fn {id, _player} -> id end)
    
    if length(robot_ids) > 0 do
      Logger.info("🎲 [JHANDI_MUNDA] 房间关闭，清理 #{length(robot_ids)} 个机器人: #{inspect(robot_ids)}")
      
      # 先尝试释放真实机器人回到系统池
      try do
        Teen.RobotManagement.SimpleRobotProvider.release_robots(robot_ids)
        Logger.info("🎲 [JHANDI_MUNDA] 真实机器人已释放: #{inspect(robot_ids)}")
      rescue
        error ->
          Logger.warning("🎲 [JHANDI_MUNDA] 释放真实机器人失败: #{inspect(error)}")
      end
      
      # 然后更新每个机器人的状态为空闲 - 使用标准API
      Enum.each(robot_players, fn {robot_id, _robot} ->
        try do
          Teen.RobotManagement.RobotStateManager.update_robot_status(robot_id, :idle, %{
            current_room_id: nil,
            current_game_type: nil,
            reason: "房间关闭"
          })
          Logger.info("🎲 [JHANDI_MUNDA] 机器人 #{robot_id} 已清理完成")
        rescue
          error ->
            Logger.error("🎲 [JHANDI_MUNDA] 清理机器人 #{robot_id} 失败: #{inspect(error)}")
        end
      end)
      
      # 从房间状态中移除所有机器人
      updated_players = 
        state.players
        |> Enum.reject(fn {_id, player} -> is_robot?(player) end)
        |> Map.new()
        
      %{state | players: updated_players}
    else
      state
    end
  end
end
