defmodule Cypridina.Teen.GameSystem.Games.LongHu.LongHuGame do
  @moduledoc """
  龙虎斗游戏定义模块

  实现游戏工厂行为，定义龙虎斗游戏的基本信息和配置
  包含所有游戏相关的配置参数，提供统一的配置管理
  """

  @behaviour Cypridina.RoomSystem.GameFactory

  # ==================== 游戏基础配置 ====================

  # 游戏阶段定义
  @game_phases %{
    # 等待开始
    waiting: :waiting,
    # 下注阶段
    betting: :betting,
    # 发牌阶段
    dealing: :dealing,
    # 亮牌阶段
    revealing: :revealing,
    # 结算阶段
    settling: :settling
  }

  # 下注区域定义
  @bet_areas %{
    # 龙
    long: 1,
    # 虎
    hu: 2,
    # 和
    he: 3
  }

  # 赔率配置 - 使用整数避免浮点数计算问题
  @odds %{
    # 龙 1:1 (实际赔付2倍，包含本金)
    long: 2,
    # 虎 1:1 (实际赔付2倍，包含本金)
    hu: 2,
    # 和 1:8 (实际赔付9倍，包含本金)
    he: 9
  }

  # 筹码面值配置 (服务端按客户端100倍配置)
  # 客户端: [10, 50, 100, 1000, 5000, 10000], 倍率100
  # 服务端: [1000, 5000, 10000, 100000, 500000, 1000000]
  @chip_values [1000, 5000, 10000, 100_000, 500_000, 1_000_000]

  # 筹码编码位移配置 (用于客户端位运算)
  @chip_bit_shifts %{
    # 第1种筹码 (客户端10*100)，位移0位
    1000 => 0,
    # 第2种筹码 (客户端50*100)，位移4位
    5000 => 4,
    # 第3种筹码 (客户端100*100)，位移8位
    10000 => 8,
    # 第4种筹码 (客户端1000*100)，位移12位
    100_000 => 12,
    # 第5种筹码 (客户端5000*100)，位移16位
    500_000 => 16,
    # 第6种筹码 (客户端10000*100)，位移20位
    1_000_000 => 20
  }

  @impl true
  def game_type, do: :longhu

  @impl true
  def game_name, do: "龙虎斗"

  @impl true
  def room_module, do: Cypridina.Teen.GameSystem.Games.LongHu.LongHuRoom

  @impl true
  def default_config do
    %{
      # ==================== 房间基础配置 ====================
      # 最大玩家数 (百人场)
      max_players: 100,
      # 最小玩家数 (无限制)
      min_players: 0,
      # 自动开始延迟 (毫秒)
      auto_start_delay: 1000,

      # ==================== 机器人配置 ====================
      # 启用机器人
      enable_robots: true,
      # 机器人数量
      robot_count: 8,

      # ==================== 游戏时间配置 ====================
      # 下注时间(秒)
      bet_time: 15,
      # 发牌时间(秒)
      deal_time: 3,
      # 亮牌时间(秒)
      reveal_time: 10,
      # 结算时间(秒)
      settle_time: 3,

      # ==================== 下注限制配置 ====================
      # 最小下注
      min_bet: 10,
      # 最大下注 (单次)
      max_bet: 10000_00,
      # 个人总下注限制
      max_total_bet: 50000_00,

      # 区域下注限制
      area_bet_limits: %{
        # 龙区域下注限制
        long: 10000_00,
        # 虎区域下注限制
        hu: 10000_00,
        # 和区域下注限制
        he: 5000_00
      },

      # ==================== 庄家系统配置 ====================
      banker: %{
        # 系统庄家初始金币
        system_banker_money: 1_000_000,
        # 上庄最低金币要求
        min_banker_money: 2_000_000,
        # 最大坐庄局数
        max_turns: 20,
        # 最小坐庄局数
        min_turns: 5,
        # 自动轮换庄家
        auto_rotate: true
      },

      # ==================== 奖池系统配置 ====================
      jackpot: %{
        # 初始总奖池
        initial_total: 1_000_000,
        # 龙区域初始奖池
        initial_long: 300_000,
        # 虎区域初始奖池
        initial_hu: 300_000,
        # 和区域初始奖池
        initial_he: 400_000
      },

      # ==================== VIP系统配置 ====================
      vip_benefits: %{
        level_1: %{min_bet_multiplier: 1.0, max_bet_multiplier: 1.2},
        level_2: %{min_bet_multiplier: 1.0, max_bet_multiplier: 1.5},
        level_3: %{min_bet_multiplier: 1.0, max_bet_multiplier: 2.0}
      }
    }
  end

  # ==================== 机器人AI配置 ====================

  # 机器人名称池
  @robot_names [
    "龙虎大师",
    "幸运玩家",
    "高手在民间",
    "财神爷",
    "金币猎人",
    "运气王",
    "策略大师",
    "赌神传人",
    "机器战士",
    "AI玩家",
    "算法高手",
    "数据分析师",
    "概率专家",
    "统计学家",
    "智能机器人",
    "龙腾虎跃",
    "风云变幻",
    "一掷千金",
    "稳赢不亏",
    "神算子"
  ]

  # 机器人头像配置
  @robot_avatars [
    "robot_1",
    "robot_2",
    "robot_3",
    "robot_4",
    "robot_5",
    "ai_1",
    "ai_2",
    "ai_3",
    "ai_4",
    "ai_5"
  ]

  # 机器人行为配置
  @robot_behavior %{
    # 最小思考时间(毫秒)
    min_think_time: 1000,
    # 最大思考时间(毫秒)
    max_think_time: 8000,
    # 下注概率
    bet_probability: 0.8,
    # 激进下注比例
    aggressive_rate: 0.3,
    # 保守下注比例
    conservative_rate: 0.5,
    # 随机下注比例
    random_rate: 0.2,
    # 机器人最小金币
    min_money: 50_000,
    # 机器人最大金币
    max_money: 5_000_000,
    # 新增字段
    # 最低金币阈值 (低于此值的机器人会被清理)
    min_money_threshold: 1000,
    # 机器人轮换概率
    rotation_probability: 0.1,
    # 机器人最大生存时间(分钟)
    max_lifetime_minutes: 30
  }

  # 机器人筹码权重配置 (对应新的筹码面值)
  @robot_chip_weights %{
    # 1000筹码 (客户端10*100)：40%权重
    1000 => 40,
    # 5000筹码 (客户端50*100)：25%权重
    5000 => 25,
    # 10000筹码 (客户端100*100)：20%权重
    10000 => 20,
    # 100000筹码 (客户端1000*100)：10%权重
    100_000 => 10,
    # 500000筹码 (客户端5000*100)：4%权重
    500_000 => 4,
    # 1000000筹码 (客户端10000*100)：1%权重
    1_000_000 => 1
  }

  # ==================== 协议配置 ====================

  # 主要协议号定义
  @protocols %{
    # 游戏配置协议
    sc_lhd_config: 3900,
    # 游戏状态协议
    sc_lhd_state: 3901,
    # 下注协议
    cs_lhd_bet: 3902,
    # 庄家信息协议
    sc_lhd_banker: 3903,
    # 历史记录协议
    sc_lhd_history: 3916,
    # 玩家列表协议
    sc_lhd_player_list: 3921,
    # 下注成功协议
    sc_lhd_bet_success: 3926,
    # 筹码增量协议
    sc_lhd_bet_sync: 3927,
    # 结算协议
    sc_lhd_settlement: 3928
  }

  # ==================== 统计配置 ====================

  @statistics_config %{
    # 最大历史记录数
    max_history_records: 100,
    # 最大聊天消息数
    max_chat_messages: 50,
    # 玩家列表分页大小
    player_list_page_size: 20,
    # 每日统计重置时间(小时)
    daily_stats_reset_hour: 0
  }

  # ==================== 性能配置 ====================

  @performance_config %{
    # 游戏tick间隔(毫秒)
    game_tick_interval: 1000,
    # 机器人检查间隔(毫秒)
    robot_check_interval: 30000,
    # 数据同步间隔(毫秒)
    data_sync_interval: 500,
    # 清理间隔(毫秒)
    cleanup_interval: 300_000
  }

  @impl true
  def is_lobby_game?, do: true

  @impl true
  def supported_game_ids do
    [
      # 龙虎斗 (客户端使用的ID)
      22,
      # 龙虎斗 (协议中使用的ID)
      201
    ]
  end

  # ==================== 配置访问器函数 ====================

  @doc """
  获取游戏阶段定义
  """
  def game_phases, do: @game_phases

  @doc """
  获取下注区域定义
  """
  def bet_areas, do: @bet_areas

  @doc """
  获取赔率配置
  """
  def odds, do: @odds

  @doc """
  获取筹码面值配置
  """
  def chip_values, do: @chip_values

  @doc """
  获取筹码编码位移配置
  """
  def chip_bit_shifts, do: @chip_bit_shifts

  @doc """
  获取机器人名称池
  """
  def robot_names, do: @robot_names

  @doc """
  获取机器人头像配置
  """
  def robot_avatars, do: @robot_avatars

  @doc """
  获取机器人行为配置
  """
  def robot_behavior, do: @robot_behavior

  @doc """
  获取机器人筹码权重配置
  """
  def robot_chip_weights, do: @robot_chip_weights

  @doc """
  获取协议配置
  """
  def protocols, do: @protocols

  @doc """
  获取统计配置
  """
  def statistics_config, do: @statistics_config

  @doc """
  获取性能配置
  """
  def performance_config, do: @performance_config

  @doc """
  获取指定协议号
  """
  def protocol(name), do: Map.get(@protocols, name)

  @doc """
  获取指定筹码的位移值
  """
  def chip_bit_shift(chip_value), do: Map.get(@chip_bit_shifts, chip_value, 0)

  @doc """
  获取指定区域的赔率
  """
  def odds(area), do: Map.get(@odds, area, 1)

  @doc """
  获取随机机器人名称
  """
  def random_robot_name, do: Enum.random(@robot_names)

  @doc """
  获取随机机器人头像
  """
  def random_robot_avatar, do: Enum.random(@robot_avatars)

  @doc """
  生成机器人初始积分 (适配新筹码面值)
  """
  def generate_robot_initial_money do
    # 根据不同策略生成积分，考虑到新的筹码面值(最小1000)
    strategy = :rand.uniform(100)

    cond do
      strategy <= 30 ->
        # 30% 概率：保守型机器人，积分较少
        # 50k-200k
        50_000 + :rand.uniform(150_000)

      strategy <= 70 ->
        # 40% 概率：中等型机器人，积分中等
        # 150k-500k
        150_000 + :rand.uniform(350_000)

      true ->
        # 30% 概率：富豪型机器人，积分较多
        # 400k-1000k
        400_000 + :rand.uniform(600_000)
    end
  end

  @doc """
  获取随机机器人偏好区域
  """
  def random_robot_favorite_area do
    Enum.random([:long, :hu, :he])
  end

  @doc """
  获取随机机器人下注风格
  """
  def random_robot_bet_style do
    Enum.random([:conservative, :moderate, :aggressive])
  end

  @doc """
  获取机器人下注风格配置
  """
  def robot_bet_style_config do
    %{
      # 保守型：1-3个同种筹码
      conservative: {1, 3},
      # 中庸型：2-5个同种筹码
      moderate: {2, 5},
      # 激进型：3-8个同种筹码
      aggressive: {3, 8}
    }
  end

  @doc """
  根据服务器ID获取特定配置覆盖
  """
  def server_config(server_id) do
    # 可以根据服务器ID返回不同的配置覆盖
    case server_id do
      2201 ->
        %{
          min_bet: 1000,
          chip_values: [1000, 5000, 10000, 100_000, 500_000, 1_000_000],
          banker: %{min_banker_money: 2_000_000}
        }

      _ ->
        %{}
    end
  end

  @doc """
  合并配置覆盖
  """
  def merge_config(base_config, overrides \\ %{}) do
    Map.merge(base_config, overrides, fn
      _key, base_value, override_value when is_map(base_value) and is_map(override_value) ->
        Map.merge(base_value, override_value)

      _key, _base_value, override_value ->
        override_value
    end)
  end

  @doc """
  获取完整的游戏配置 (包含所有常量和默认配置)
  """
  def full_config(overrides \\ %{}) do
    base_config = default_config()

    full_config =
      Map.merge(base_config, %{
        game_phases: @game_phases,
        bet_areas: @bet_areas,
        odds: @odds,
        chip_values: @chip_values,
        chip_bit_shifts: @chip_bit_shifts,
        robot_names: @robot_names,
        robot_avatars: @robot_avatars,
        robot_behavior: @robot_behavior,
        robot_chip_weights: @robot_chip_weights,
        protocols: @protocols,
        statistics_config: @statistics_config,
        performance_config: @performance_config
      })

    merge_config(full_config, overrides)
  end
end
