defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777GameLogic do
  @moduledoc """
  Slot777游戏逻辑引擎
  实现老虎机的核心算法，包括：
  - 图标生成
  - 中奖线计算
  - 赔率计算
  - Jackpot计算
  - 免费游戏触发
  """

  require Logger

  # 引入配置模块
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777Config

  # 辅助函数：获取配置节
  defp get_config_section(config, keys) when is_list(keys) do
    Enum.find_value(keys, fn key ->
      case Map.get(config, key) do
        nil -> nil
        value -> value
      end
    end)
  end

  # 游戏常量
  @rows 3
  @cols 5
  @max_lines 9

  # 图标类型 (对应前端 EM_SLOT777_ICONTYPE) - 值范围0-10
  @icon_types %{
    # WILD字 (NUM_WILD)
    wild: 0,
    # 香蕉 (NUM_1)
    banana: 1,
    # 西瓜 (NUM_2)
    watermelon: 2,
    # 草莓 (NUM_3)
    strawberry: 3,
    # 葡萄 (NUM_4)
    grape: 4,
    # 芒果 (NUM_5)
    mango: 5,
    # 榴莲 (NUM_6)
    durian: 6,
    # 山竹 (NUM_7)
    mangosteen: 7,
    # BAR (NUM_8)
    bar: 8,
    # 苹果 (NUM_apple)
    apple: 9,
    # 7 (NUM_777)
    seven: 10
  }

  # 中奖线定义 (3行5列，按位置索引1-15) - 与前端LINE_POS_EX完全一致
  # 矩阵布局: 1  2  3  4  5
  #          6  7  8  9  10
  #          11 12 13 14 15
  #
  # ⚠️ 重要：必须与前端 SLOT777Define.ts 中的 LINE_POS_EX 完全一致！
  @win_lines [
    # 线1: 中行 (与前端LINE_POS_EX[0]一致)
    [6, 7, 8, 9, 10],
    # 线2: 上行 (与前端LINE_POS_EX[1]一致)
    [1, 2, 3, 4, 5],
    # 线3: 下行 (与前端LINE_POS_EX[2]一致)
    [11, 12, 13, 14, 15],
    # 线4: V形 (与前端LINE_POS_EX[3]一致)
    [1, 7, 13, 9, 5],
    # 线5: 倒V形 (与前端LINE_POS_EX[4]一致)
    [11, 7, 3, 9, 15],
    # 线6: 上升形 (与前端LINE_POS_EX[5]一致)
    [1, 2, 8, 14, 15],
    # 线7: 下降形 (与前端LINE_POS_EX[6]一致)
    [11, 12, 8, 4, 5],
    # 线8: M形 (与前端LINE_POS_EX[7]一致)
    [6, 12, 8, 4, 10],
    # 线9: W形 (与前端LINE_POS_EX[8]一致)
    [6, 2, 8, 14, 10]
  ]

  @doc """
  生成游戏结果

  ## 参数
  - bet_odds: 下注倍率
  - player_money: 玩家金币数量
  - config: 动态游戏配置
  - base_bet: 基础下注金额 (difen)
  - game_config: 游戏配置 (包含 bet_rate_num 和 score_rate)
  """
  def generate_game_result(bet_odds, player_money, config, base_bet \\ nil, game_config \\ nil) do
    # 使用传入的动态配置
    betting_config = get_config_section(config, [:betting, "betting"])

    # 使用配置中的值或传入的值
    actual_base_bet = base_bet || betting_config.difen
    bet_rate_num = if game_config, do: game_config.bet_rate_num, else: betting_config.bet_rate_num
    score_rate = if game_config, do: game_config.score_rate, else: betting_config.score_rate
    Logger.info("🎰 [SLOT777_CONFIG] generate_game_result: #{inspect(config, pretty: true)}")

    # 生成3x5的图标矩阵
    icon_matrix = generate_icon_matrix(config)

    # 转换为前端期望的一维数组格式 (1-15)
    icon_result = matrix_to_array(icon_matrix)

    # 计算中奖线
    {lineresult_obj, win_lines_info, total_multiplier} = calculate_win_lines(icon_matrix, config)

    # 计算实际下注金额 - 使用配置中的值
    # 前端计算: odds × BET_RATE_NUM × difen × SCORE_RATE
    bet_amount = actual_base_bet * bet_odds * score_rate * bet_rate_num

    # 计算奖金 - 得奖不需要乘以bet_rate_num，所以使用不含bet_rate_num的金额计算
    win_bet_amount = actual_base_bet * bet_odds * score_rate

    win_money =
      if total_multiplier > 0 do
        trunc(win_bet_amount * total_multiplier)
      else
        0
      end

    # 计算玩家输赢 (赢金 - 下注)
    change_money = win_money - bet_amount

    # 检查是否触发Jackpot - 使用总下注金额计算单线下注
    {jackpot_cash, seven_count} = calculate_jackpot(icon_matrix, bet_amount, config)

    # 检查是否触发免费游戏
    free_times = calculate_free_times(icon_matrix, config)

    # 构建结果 - 使用前端期望的格式
    %{
      "freetimes" => free_times,
      "sevennum" => seven_count,
      "iconresult" => icon_result,
      "linecount" => map_size(lineresult_obj),
      "lineresult" => lineresult_obj,
      "totalmult" => total_multiplier,
      "winmoney" => win_money,
      "changemoney" => change_money,
      "jackpotcash" => jackpot_cash,
      # 转盘彩金，暂时为0
      "luckyjackpot" => 0,
      # 前端需要的中奖线信息
      "winLinesInfo" => win_lines_info,
      # 倍率金币数
      "multgoldnum" => if(total_multiplier > 0, do: total_multiplier, else: 0)
    }
  end

  @doc """
  生成3x5的图标矩阵
  """
  defp generate_icon_matrix(config) do
    for _row <- 1..@rows do
      for _col <- 1..@cols do
        generate_weighted_icon(config)
      end
    end
  end

  @doc """
  根据权重生成图标
  """
  defp generate_weighted_icon(config) do
    # 获取图标权重配置
    icon_weights = config.win_rate.icon_weights
    # Logger.info("🎰 [SLOT777_CONFIG] icon_weights: #{inspect(icon_weights, pretty: true)}")

    total_weight = icon_weights |> Map.values() |> Enum.sum()
    random_value = :rand.uniform(total_weight)

    find_icon_by_weight(random_value, icon_weights, 0)
  end

  defp find_icon_by_weight(target, weights, current_sum) do
    # 默认返回樱桃（现在是值0）
    Enum.find_value(weights, fn {icon, weight} ->
      new_sum = current_sum + weight

      if target <= new_sum do
        icon
      else
        find_icon_by_weight(target, Map.delete(weights, icon), new_sum)
      end
    end) || 0
  end

  @doc """
  将3x5矩阵转换为1-15的一维数组
  """
  defp matrix_to_array(matrix) do
    matrix
    |> List.flatten()
    |> Enum.with_index(1)
    |> Enum.into(%{}, fn {icon, index} -> {index, icon} end)
  end

  @doc """
  计算中奖线
  """
  defp calculate_win_lines(matrix, config) do
    flat_matrix = List.flatten(matrix)

    Logger.debug("🎰 [SLOT777_WIN_LINES] 开始计算中奖线，图标矩阵: #{inspect(flat_matrix)}")

    # 计算完整的中奖线信息（包含倍率）
    full_win_lines =
      @win_lines
      |> Enum.with_index(1)
      |> Enum.reduce([], fn {line_positions, line_index}, acc ->
        case check_line_win(flat_matrix, line_positions, config) do
          {true, icon_type, count, multiplier} ->
            win_info = %{
              "line" => line_index,
              "icon" => icon_type,
              "num" => count,
              "mult" => multiplier
            }

            Logger.debug("🎰 [SLOT777_WIN_LINES] 线#{line_index}中奖: 图标#{icon_type}, 数量#{count}, 倍率#{multiplier}")
            [win_info | acc]

          {false, _, _, _} ->
            acc
        end
      end)
      |> Enum.reverse()

    # 生成前端期望的格式：lineresult 为对象，winLinesInfo 为数组
    {lineresult_obj, win_lines_info} =
      if length(full_win_lines) > 0 do
        # lineresult: 对象格式 {"1": {line: x, num: y}, "2": {...}}
        lineresult_obj =
          full_win_lines
          |> Enum.with_index(1)
          |> Enum.into(%{}, fn {win_info, index} ->
            {to_string(index),
             %{
               "line" => win_info["line"],
               "num" => win_info["num"]
             }}
          end)

        # winLinesInfo: 数组格式，包含中奖位置信息
        win_lines_info =
          full_win_lines
          |> Enum.map(fn win_info ->
            line_num = win_info["line"]
            count = win_info["num"]

            # 获取该线的位置定义
            line_positions = Enum.at(@win_lines, line_num - 1, [])

            # 取前count个位置作为中奖位置
            prize_icon_list = Enum.take(line_positions, count)

            %{
              "nLine" => line_num,
              "nCount" => count,
              "prizeIconList" => prize_icon_list
            }
          end)

        {lineresult_obj, win_lines_info}
      else
        {%{}, []}
      end

    # 计算总倍率
    multipliers = Enum.map(full_win_lines, & &1["mult"])
    total_multiplier = Enum.sum(multipliers)

    Logger.debug("🎰 [SLOT777_WIN_LINES] 所有倍率: #{inspect(multipliers)}, 总倍率: #{total_multiplier}")

    {lineresult_obj, win_lines_info, total_multiplier}
  end

  @doc """
  检查单条线是否中奖
  """
  defp check_line_win(flat_matrix, line_positions, config) do
    # 获取线上的图标
    line_icons =
      Enum.map(line_positions, fn pos ->
        Enum.at(flat_matrix, pos - 1)
      end)

    # 添加调试日志
    Logger.debug("🎰 [SLOT777_WIN_CHECK] 检查中奖线: 位置=#{inspect(line_positions)}, 图标=#{inspect(line_icons)}")

    # 检查连续相同图标 (从左到右，至少2个连续才有奖)
    case find_consecutive_match(line_icons) do
      {icon_type, count} when count >= 2 ->
        multiplier = get_payout_multiplier(icon_type, count, config)
        Logger.debug("🎰 [SLOT777_WIN_CHECK] 找到匹配: 图标=#{icon_type}, 数量=#{count}, 倍率=#{multiplier}")

        if multiplier > 0 do
          {true, icon_type, count, multiplier}
        else
          {false, nil, 0, 0}
        end

      {icon_type, count} ->
        Logger.debug("🎰 [SLOT777_WIN_CHECK] 匹配不足: 图标=#{icon_type}, 数量=#{count}")
        {false, nil, 0, 0}

      _ ->
        Logger.debug("🎰 [SLOT777_WIN_CHECK] 无匹配")
        {false, nil, 0, 0}
    end
  end

  @doc """
  查找连续匹配的图标 (支持万能牌替代，但万能牌不能替代苹果和7)
  """
  defp find_consecutive_match([first | rest]) do
    # 如果第一个是万能牌，找到第一个非万能牌作为基准
    base_icon = if first == 0, do: find_first_non_wild(rest), else: first

    Logger.debug("🎰 [SLOT777_MATCH] 连续匹配检查: 第一个=#{first}, 基准图标=#{base_icon}, 剩余=#{inspect(rest)}")

    if base_icon do
      # 检查万能牌是否可以替代找到的基准图标
      if first == 0 and not can_wild_substitute?(base_icon) do
        # 万能牌不能替代苹果或7，只计算非万能牌部分
        Logger.debug("🎰 [SLOT777_MATCH] 万能牌不能替代图标#{base_icon}，跳过万能牌")
        find_consecutive_match(rest)
      else
        count = 1 + count_consecutive_with_wild(base_icon, rest)
        Logger.debug("🎰 [SLOT777_MATCH] 匹配结果: 图标=#{base_icon}, 数量=#{count}")
        {base_icon, count}
      end
    else
      # 全是万能牌的情况，按万能牌可替代的最高价值图标计算 (BAR=8，因为万能牌不能替代苹果9和7=10)
      count = length([first | rest])
      Logger.debug("🎰 [SLOT777_MATCH] 全万能牌: 数量=#{count}，按BAR(8)计算")
      {8, count}
    end
  end

  # 处理空列表的情况
  defp find_consecutive_match([]), do: {nil, 0}

  # 找到第一个非万能牌图标
  defp find_first_non_wild([]), do: nil
  # WILD = 0
  defp find_first_non_wild([0 | rest]), do: find_first_non_wild(rest)
  defp find_first_non_wild([icon | _]), do: icon

  # 检查万能牌是否可以替代指定图标
  # 万能牌不能替代苹果(9)和7(10)
  defp can_wild_substitute?(9), do: false  # 苹果(FREE)
  defp can_wild_substitute?(10), do: false # 7(JACKPOT)
  defp can_wild_substitute?(_), do: true   # 其他图标可以替代

  # 支持万能牌的连续计数 (但万能牌不能替代苹果和7)
  defp count_consecutive_with_wild(_target, []), do: 0

  # WILD = 0，但需要检查是否可以替代目标图标
  defp count_consecutive_with_wild(target, [0 | rest]) do
    if can_wild_substitute?(target) do
      1 + count_consecutive_with_wild(target, rest)
    else
      # 万能牌不能替代苹果或7，停止计数
      0
    end
  end

  defp count_consecutive_with_wild(target, [target | rest]),
    do: 1 + count_consecutive_with_wild(target, rest)

  defp count_consecutive_with_wild(_target, _), do: 0

  @doc """
  获取赔率倍数
  """
  defp get_payout_multiplier(icon_type, count, config) do
    payout_table = config.payout.payout_table

    payout_table
    |> Map.get(icon_type, %{})
    |> Map.get(count, 0)
  end

  @doc """
  计算Jackpot - 根据游戏规则图片
  单线下注达到₹10，相同符号(7)就是jackpot，获得Jackpot池的一定比例

  计算公式：
  - 总下注 = difen × odds × bet_rate_num × score_rate (系统单位)
  - 单线下注 = 总下注 ÷ 9条线 ÷ 100 (转换为实际金额)
  - 例如：1800 ÷ 9 ÷ 100 = 2 (₹2单线下注)
  """
  defp calculate_jackpot(matrix, bet_amount, config) do
    flat_matrix = List.flatten(matrix)
    # 7现在是值10
    seven_count = Enum.count(flat_matrix, &(&1 == 10))

    # 计算单线下注金额 (总下注除以线数，再除以100转换为实际金额)
    single_line_bet = bet_amount / @max_lines / 100

    # 获取当前Jackpot池金额（系统单位）
    current_jackpot_pool_raw = get_current_jackpot()
    # 转换为显示单位（除以100）
    current_jackpot_pool = current_jackpot_pool_raw / 100

    # 根据7的数量和单线下注金额计算Jackpot比例
    jackpot_percentage = get_jackpot_percentage(seven_count, single_line_bet, config)

    # 添加调试日志
    if seven_count >= 3 do
      Logger.info("🎰 [SLOT777_JACKPOT] 检测到Jackpot条件: 7的数量=#{seven_count}, 单线下注=#{single_line_bet}, 奖池(显示)=#{current_jackpot_pool}, 比例=#{jackpot_percentage}%")
    end

    jackpot_cash = if jackpot_percentage > 0 do
      # 使用显示单位的奖池金额计算，然后转换回系统单位
      display_amount = trunc(current_jackpot_pool * jackpot_percentage / 100)
      system_amount = trunc(display_amount * 100)
      Logger.info("🎰 [SLOT777_JACKPOT] 计算Jackpot奖金: 显示金额=#{display_amount}, 系统金额=#{system_amount}")
      system_amount
    else
      0
    end

    {jackpot_cash, seven_count}
  end

  @doc """
  根据7的数量和单线下注金额获取Jackpot比例
  """
  defp get_jackpot_percentage(seven_count, single_line_bet, config) when seven_count >= 3 do
    percentage_table = config.jackpot.percentage_table

    # 找到匹配的下注级别
    bet_level = cond do
      single_line_bet >= 200 -> 200
      single_line_bet >= 100 -> 100
      single_line_bet >= 20 -> 20
      single_line_bet >= 10 -> 10
      true -> nil
    end

    Logger.info("🎰 [SLOT777_JACKPOT] Jackpot条件检查: 7数量=#{seven_count}, 单线下注=#{single_line_bet}, 匹配级别=#{inspect(bet_level)}")

    if bet_level do
      level_config = Map.get(percentage_table, bet_level, %{})
      # 如果JACKPOT数量超过5个，使用5个的比例
      lookup_count = if seven_count > 5, do: 5, else: seven_count
      percentage = Map.get(level_config, lookup_count, 0)
      Logger.info("🎰 [SLOT777_JACKPOT] 级别配置=#{inspect(level_config)}, 查找数量=#{lookup_count}, 最终比例=#{percentage}%")
      percentage
    else
      Logger.info("🎰 [SLOT777_JACKPOT] 单线下注不足₹10，无法触发Jackpot")
      0
    end
  end

  defp get_jackpot_percentage(_, _, _), do: 0

  @doc """
  计算免费游戏次数 - 根据游戏规则图片
  苹果(FREE)触发免费游戏：3个=4次，4个=8次，5个及以上=12次
  """
  defp calculate_free_times(matrix, config) do
    flat_matrix = List.flatten(matrix)
    # 苹果(FREE)现在是值9
    apple_count = Enum.count(flat_matrix, &(&1 == 9))

    trigger_table = config.free_game.trigger_table

    # 如果苹果数量超过配置表中的最大值，使用最大值对应的奖励
    cond do
      apple_count >= 5 -> Map.get(trigger_table, 5, 12)  # 5个及以上都给最高奖励
      apple_count >= 3 -> Map.get(trigger_table, apple_count, 0)  # 3-4个按配置
      true -> 0  # 少于3个不触发
    end
  end

  @doc """
  获取当前Jackpot池金额
  """
  def get_current_jackpot(game_id \\ 40) do
    # 使用标准化ID确保与后台管理一致
    normalized_game_id = Teen.GameSystem.JackpotManager.normalize_game_id(game_id)
    Teen.GameSystem.JackpotManager.get_jackpot_balance(normalized_game_id, :main)
  end

  @doc """
  测试用函数：直接计算中奖线（不生成随机结果）
  """
  def calculate_win_lines_for_test(matrix, config) do
    calculate_win_lines(matrix, config)
  end

  @doc """
  更新Jackpot池
  """
  def update_jackpot_pool(bet_amount, config) do
    # 使用传入的动态配置
    jackpot_config = get_config_section(config, [:jackpot, "jackpot"])
    contribution_rate = jackpot_config.contribution_rate

    # 每次下注的一定比例进入Jackpot池
    contribution = trunc(bet_amount * contribution_rate)

    # 这里应该更新数据库中的Jackpot金额
    Logger.info("🎰 [SLOT777] Jackpot池增加: #{contribution} (贡献率: #{contribution_rate * 100}%)")

    contribution
  end
end
