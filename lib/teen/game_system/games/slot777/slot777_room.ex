defmodule <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.Games.Slot777.Slot777Room do
  @moduledoc """
  Slot777游戏房间实现
  """

  use <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomBase, game_type: :slot777
  require Logger

  alias <PERSON><PERSON><PERSON><PERSON>.Teen.GameSystem.RoomManager
  alias <PERSON><PERSON><PERSON>ina.Teen.GameSystem.Games.Slot777.Slot777GameLogic
  alias Cy<PERSON><PERSON>ina.Teen.GameSystem.Games.Slot777.Slot777FreeGameTest
  alias Cypridina.Teen.GameSystem.Games.Slot777.Slot777Config
  alias Teen.GameSystem.JackpotManager

  # 初始化游戏逻辑
  def init_game_logic(state) do
    Logger.info("🎰 [SLOT777] 初始化游戏房间: #{state.id} gameid: #{state.game_id}")

    # 通过 GameFactory 确保全局管理器已启动，然后注册房间
    # case GameFactory.get_global_manager(:slot777) do
    #   {:ok, _pid} ->
    #     # 全局管理器已存在，直接注册
    #     register_to_global_manager(state)

    #   {:error, :not_found} ->
    #     # 全局管理器不存在，尝试启动
    #     case GameFactory.start_global_manager(:slot777) do
    #       {:ok, _pid} ->
    #         Logger.info("🎰 [SLOT777] 全局管理器启动成功，注册房间")
    #         register_to_global_manager(state)

    #       {:error, reason} ->
    #         Logger.error("❌ [SLOT777] 全局管理器启动失败: #{inspect(reason)}")
    #         init_with_default_config(state)
    #     end
    # end
    # 使用本地配置初始化
    init_with_local_config(state)
  end

  # 使用本地配置初始化
  defp init_with_local_config(state) do
    Logger.info("✅ [SLOT777] 使用智能合并配置初始化房间")
    Logger.info("🎰 [SLOT777_CONFIG] 房间配置内容: #{inspect(state.config, pretty: true)}")

    # 使用智能合并：传入配置优先，缺失项用默认值补充
    final_config = Slot777Config.get_merged_config(state.config)

    # 初始化奖池
    init_jackpots(state, final_config)
    # 获取配置项（支持原子键和字符串键）
    betting_config = get_config_section(final_config, [:betting, "betting"])
    testing_config = get_config_section(final_config, [:testing, "testing"])

    game_config = %{
      # 最小下注 - 统一获取配置值
      min_bet: get_config_value(betting_config, [:min_bet, "min_bet"], 1),
      # 最大下注
      max_bet: get_config_value(betting_config, [:max_bet, "max_bet"], 1000),
      # 赔率配置 - 统一获取配置值
      odds_config:
        get_config_value(betting_config, [:odds_config, "odds_config"], %{
          "1" => 0.2,
          "2" => 1,
          "3" => 2,
          "4" => 10,
          "5" => 20,
          "6" => 100,
          "7" => 200
        }),
      # 底分
      difen: get_config_value(betting_config, [:difen, "difen"], 1),
      # 固定倍率 (对应前端的 BET_RATE_NUM)
      bet_rate_num: get_config_value(betting_config, [:bet_rate_num, "bet_rate_num"], 9),
      # 金币比例 (对应前端的 SCORE_RATE)
      score_rate: get_config_value(betting_config, [:score_rate, "score_rate"], 1)
    }

    # TODO: 奖池初始化将由新的统一奖池系统实现

    # 🎰 根据配置初始化免费游戏测试状态
    enable_free_game_test =
      get_config_value(testing_config, [:enable_free_game_test, "enable_free_game_test"], false)

    free_game_test_spins =
      get_config_value(testing_config, [:free_game_test_spins, "free_game_test_spins"], 0)

    free_game_test_state =
      if enable_free_game_test do
        Logger.info("🎰 [FREE_GAME_TEST] 启用免费游戏测试功能 - 触发次数: #{free_game_test_spins}")

        Slot777FreeGameTest.init_state(
          enabled: true,
          trigger_count: free_game_test_spins,
          free_game_type: :normal
        )
      else
        Logger.info("🎰 [FREE_GAME_TEST] 免费游戏测试功能已禁用")
        Slot777FreeGameTest.init_state(enabled: false)
      end

    # 构建测试配置对象
    test_config = %{
      test_mode: get_config_value(testing_config, [:test_mode, "test_mode"], false),
      fixed_result: get_config_value(testing_config, [:fixed_result, "fixed_result"], nil),
      enable_free_game_test: enable_free_game_test,
      free_game_test_spins: free_game_test_spins
    }

    Logger.info("🎰 [SLOT777] 配置加载完成:")
    Logger.info("  - 游戏配置: #{inspect(game_config)}")
    Logger.info("  - 测试配置: #{inspect(test_config)}")

    game_data = %{
      # 游戏配置
      config: game_config,
      # 当前状态
      status: :waiting,
      # 玩家下注记录
      bets: %{},
      # 当前玩家下注倍率
      current_odds: %{},
      # 游戏结果
      results: [],
      # 当前回合
      current_round: 0,
      # 免费游戏状态
      free_games: %{},
      # 待发送的免费游戏
      pending_free_games: %{},
      # 🎰 免费游戏测试状态 (根据配置启用/禁用)
      free_game_test_state: free_game_test_state,
      # 🏆 Jackpot中奖记录 (本地存储，最多保留10条)
      jackpot_records: []
    }

    # 将最终配置和游戏数据都存储到状态中
    %{state | game_data: game_data, config: final_config}
  end

  # 玩家加入房间后的处理
  def on_player_joined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOT777] 玩家加入: #{user_id}, numeric_id: #{numeric_id}")
    Logger.info("🎰 [SLOT777] 玩家信息详情: #{inspect(player, pretty: true, limit: :infinity)}")

    # 获取玩家真实积分
    initial_points = get_player_points(state, numeric_id)
    Logger.info("🎰 [SLOT777] 玩家初始积分: #{initial_points} (机器人: #{player.is_robot})")

    # 更新用户信息
    updated_user_info =
      player.user
      |> Map.put(:money, initial_points)

    # 更新state中的玩家信息
    updated_player = %{player | user: updated_user_info}
    new_state = %{state | players: Map.put(state.players, numeric_id, updated_player)}

    send_player_info(new_state, player)
    # 发送游戏配置给新玩家
    send_game_config(new_state, player)

    # 发送当前游戏状态给新玩家
    send_game_state_to_user(new_state, player)
    # 发送奖池信息给新玩家
    send_jackpot_info(new_state, player)

    # 发送玩家列表给新加入的玩家
    send_player_list_to_user(new_state, player)

    # 广播玩家数量变化通知
    broadcast_player_count_change(new_state)

    new_state
  end

  # 玩家重连加入房间
  def on_player_rejoined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOT777] 玩家重连加入: #{user_id}, numeric_id: #{numeric_id}")

    # 获取玩家真实积分
    current_points = get_player_points(state, numeric_id)
    Logger.info("🎰 [SLOT777] 重连玩家积分: #{current_points}")

    send_player_info(state, player)
    # 发送游戏配置给重连玩家
    send_game_config(state, player)

    # 发送当前游戏状态给重连玩家
    send_game_state_to_user(state, player)
    # 发送奖池信息给重连玩家
    send_jackpot_info(state, player)

    # 发送玩家列表给重连玩家
    send_player_list_to_user(state, player)

    state
  end

  # 玩家离开房间
  def on_player_left(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOT777] 玩家离开: #{user_id}, numeric_id: #{numeric_id}")

    # 积分已由新系统自动同步，无需手动处理

    # 广播玩家数量变化通知
    broadcast_player_count_change(state)

    state
  end

  # 游戏开始
  def on_game_start(state) do
    Logger.info("🎰 [SLOT777] 游戏开始: #{state.id}")
    state
  end

  # 处理游戏消息
  def handle_game_message(state, player, message) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎰 [SLOT777] 收到游戏消息 - 房间: #{state.id}, 用户: #{user_id}, numeric_id: #{numeric_id}, 消息: #{inspect(message)}"
    )

    case message do
      # 处理MainID=4的退出房间协议
      %{"mainId" => 4, "subId" => 40} ->
        data = message["data"] || %{}
        handle_exit_room_protocol(state, player, data)

      # 处理MainID=5的Slot777协议消息
      %{"mainId" => 5, "subId" => sub_id} ->
        data = message["data"] || %{}
        handle_slot777_protocol(state, player, sub_id, data)

      # 兼容性处理
      %{"cmd" => "get_room_info"} ->
        Logger.info("🏠 [ROOM_INFO_REQUEST] 房间信息请求 - 用户: #{user_id}")
        # 发送房间信息给用户
        send_game_config(state, player)
        send_game_state_to_user(state, player)
        send_jackpot_info(state, player)
        state

      %{"cmd" => "request_jackpot"} ->
        Logger.info("🎰 [JACKPOT] 奖池功能将由新系统实现 - 用户: #{user_id}")
        state

      _ ->
        Logger.info(
          "ℹ️ [GAME_MESSAGE] 消息已通过客户端协议处理或为未知消息 - 用户: #{user_id}, 消息: #{inspect(message)}"
        )

        state
    end
  end

  # 处理Slot777协议消息
  defp handle_slot777_protocol(state, player, sub_id, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎰 [SLOT777_PROTOCOL] 处理协议 - SubID: #{sub_id}, 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}"
    )

    case sub_id do
      # 离开房间 (CS_SLOT777_LEAVE_ROOM_P)
      2 ->
        handle_leave_room_request(state, player, data)

      # 游戏开始 (CS_SLOT777_GAMESTART_P)
      1000 ->
        handle_game_start_request(state, player, data)

      # 切换下注倍率 (CS_SLOTS_SWITCH_BET_P)
      1009 ->
        handle_switch_bet_request(state, player, data)

      # 获取房间信息
      1006 ->
        handle_room_info_request(state, player, data)

      # Jackpot中奖列表请求 (CS_SLOT777_JPLIST_P)
      1003 ->
        handle_jackpot_list_request(state, player, data)

      # 其他未实现的协议
      _ ->
        Logger.warning("🎰 [SLOT777] 未实现的子协议: #{sub_id}")
        send_error_response(state, player, sub_id, "未实现的协议")
        state
    end
  end

  # 发送游戏配置给玩家
  defp send_game_config(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "⚙️ [SEND_CONFIG] 发送游戏配置 - 用户: #{user_id}, numeric_id: #{numeric_id}, 房间: #{state.id}"
    )

    # 🔧 使用新的标准积分获取方式
    player_money = get_player_points(state, numeric_id)

    player_name = "玩家#{numeric_id}"

    # 构建当前玩家信息
    player_info = %{
      # 使用numeric_id作为playerid
      "playerid" => numeric_id,
      "money" => player_money,
      "name" => player_name,
      "seat" => 1
    }

    # 构建玩家列表（slot777是单机游戏，只包含当前玩家）
    playerlist = %{
      # 使用字符串键，与客户端期望的格式一致
      "1" => player_info
    }

    # 使用房间的合并后配置
    current_config = get_room_config(state)
    betting_config = get_config_section(current_config, [:betting, "betting"])
    # 获取玩家上次使用的下注倍率
    odds_config = get_config_value(betting_config, [:odds_config, "odds_config"], %{"1" => 0.2})
    # 默认为1倍
    last_odds = Map.get(state.game_data.current_odds, user_id, odds_config["1"])

    message = %{
      # MainProto.Slot777
      "mainId" => 5,
      # 游戏配置协议
      "subId" => 0,
      "data" => %{
        # 底分配置 - 使用最新配置
        "difen" => betting_config.difen,
        # 下注倍率配置 - 使用最新配置
        "odds" => betting_config.odds_config,
        # 玩家上次使用的下注倍率
        "lastodds" => last_odds,
        # 下注限制 - 使用最新配置
        "BetMax" => betting_config.max_bet,
        "BetNeed" => betting_config.min_bet,
        # 当前Jackpot金额 - 将由新系统提供
        "jackpot" => 0,
        # 玩家金币
        "money" => player_money,
        # 当前回合ID
        "room_id" => state.id,
        "roundid" => state.game_data.current_round,
        # 玩家列表（替换playerInfo）
        "playerlist" => playerlist
      }
    }

    Logger.info(
      "📤 [SEND_CONFIG] 配置消息内容 - 协议: 5/0, 底分: #{betting_config.difen}, 上次倍率: #{last_odds}, 玩家: #{player_name}"
    )

    send_to_player(state, player, message)
  end

  # 发送游戏状态给指定用户
  defp send_game_state_to_user(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 🔧 使用新的标准积分获取方式
    player_money = get_player_points(state, numeric_id)

    message = %{
      # MainProto.Game
      "mainId" => 4,
      # 游戏状态协议
      "subId" => 3,
      "data" => %{
        "status" => state.game_data.status,
        "round" => state.game_data.current_round,
        "money" => player_money
      }
    }

    Logger.info(
      "📤 [SEND_GAME_STATE] 发送游戏状态给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}, 状态: #{state.game_data.status}"
    )

    send_to_player(state, player, message)
  end

  # 发送奖池信息给指定用户 - 使用1005协议同步Jackpot分数
  defp send_jackpot_info(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 🎰 获取当前动态奖池金额
    jackpot_game_id = get_jackpot_game_id(state.game_id)
    current_jackpot = JackpotManager.get_jackpot_balance(jackpot_game_id, :main)

    message = %{
      # MainProto.Slot777
      "mainId" => 5,
      # SC_SLOT777_JACKPOT_P - 同步Jackpot分数
      "subId" => 1005,
      "data" => %{
        # 动态奖池金额
        "jackpot" => current_jackpot
      }
    }

    Logger.info(
      "📤 [SEND_JACKPOT_INFO] 发送奖池信息给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}, 奖池: #{current_jackpot}"
    )

    send_to_player(state, player, message)
  end

  # 发送奖池信息给指定用户 - 使用1005协议同步Jackpot分数
  defp send_jackpot_info(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 🎰 获取当前动态奖池金额
    jackpot_game_id = get_jackpot_game_id(state.game_id)
    current_jackpot = JackpotManager.get_jackpot_balance(jackpot_game_id, :main)

    message = %{
      # MainProto.Slot777
      "mainId" => 5,
      # SC_SLOT777_JACKPOT_P - 同步Jackpot分数
      "subId" => 1005,
      "data" => %{
        # 动态奖池金额
        "jackpot" => current_jackpot
      }
    }

    Logger.info(
      "📤 [SEND_JACKPOT_INFO] 发送奖池信息给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}, 奖池: #{current_jackpot}"
    )

    send_to_player(state, player, message)
  end

  # 发送玩家列表给指定用户（slot777单机游戏简化版）
  defp send_player_list_to_user(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # slot777是单机游戏，只发送当前玩家信息
    player_money = get_player_points(state, numeric_id)

    player_info = %{
      # 使用numeric_id作为playerid
      "playerid" => numeric_id,
      "money" => player_money,
      "name" => "玩家#{numeric_id}",
      "seat" => 1
    }

    message = %{
      # MainProto.Slot777
      "mainId" => 5,
      # SC_SLOT777_PLAYERLIST_P
      "subId" => 1007,
      "data" => %{
        "playerlist" => [player_info],
        "totalplayernum" => 1
      }
    }

    Logger.info("📤 [SEND_PLAYER_LIST] 发送玩家信息给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}")
    send_to_player(state, player, message)
  end

  # 发送玩家信息给指定用户（slot777单机游戏简化版）
  defp send_player_info(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # slot777是单机游戏，只发送当前玩家信息
    player_money = get_player_points(state, numeric_id)

    player_info = %{
      # 使用numeric_id作为playerid
      "playerid" => numeric_id,
      "money" => player_money,
      "name" => "玩家#{numeric_id}",
      "seat" => 1
    }

    message = %{
      # MainProto.Slot777
      "mainId" => 4,
      # SC_SLOT777_PLAYERLIST_P
      "subId" => 2,
      "data" => %{
        "playerlist" => [player_info],
        "totalplayernum" => 1
      }
    }

    Logger.info("📤 [SEND_PLAYER_INFO] 发送玩家信息给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}")
    send_to_player(state, player, message)
  end

  # 广播玩家数量变化通知
  defp broadcast_player_count_change(state) do
    message = %{
      # MainProto.Slot777
      "mainId" => 5,
      # SC_SLOT777_PLAYERLIST_P
      "subId" => 1007,
      "data" => %{
        "totalplayernum" => map_size(state.players)
      }
    }

    Logger.info("📤 [BROADCAST_PLAYER_COUNT] 广播玩家数量变化 - 房间总人数: #{map_size(state.players)}")
    broadcast_to_room(state, message)
  end

  @doc """
  开始游戏
  """
  def start_game(room_id, user_id, odds) do
    case GenServer.call(
           {:via, Registry, {:game_room_registry, room_id}},
           {:start_game, user_id, odds}
         ) do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
    end
  end

  @doc """
  切换下注倍率
  """
  def switch_bet(room_id, user_id, odds) do
    case GenServer.call(
           {:via, Registry, {:game_room_registry, room_id}},
           {:switch_bet, user_id, odds}
         ) do
      {:ok, result} -> {:ok, result}
      {:error, reason} -> {:error, reason}
    end
  end

  # TODO: 奖池记录获取将由新系统实现

  # 处理游戏开始请求
  @impl true
  def handle_call({:start_game, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOT777] 玩家 #{user_id} 开始游戏，倍率: #{odds}")

    # 通过 user_id 找到对应的玩家
    player_entry =
      Enum.find(state.players, fn {_numeric_id, player} ->
        player.user_id == user_id
      end)

    case player_entry do
      nil ->
        {:reply, {:error, "玩家不在房间中"}, state}

      {numeric_id, _player} ->
        # 使用房间的合并后配置
        current_config = get_room_config(state)
        betting_config = get_config_section(current_config, [:betting, "betting"])
        current_testing_config = get_config_section(current_config, [:testing, "testing"])

        # 验证倍率是否有效 - 检查倍率是否在最新配置的值中
        valid_odds = Map.values(betting_config.odds_config)

        if odds not in valid_odds do
          {:reply, {:error, "无效的下注倍率"}, state}
        else
          # 🔧 使用新的标准积分获取方式
          current_points = get_player_points(state, numeric_id)

          # 计算下注金额 - 使用最新配置计算
          # 前端计算: odds × BET_RATE_NUM × difen × SCORE_RATE
          # 🔧 确保bet_amount是整数，避免Money库的浮点数错误
          bet_amount =
            round(
              betting_config.difen * odds * betting_config.bet_rate_num *
                betting_config.score_rate
            )

          Logger.info(
            "🎰 [SLOT777] 玩家积分 #{current_points} 下注金额: #{bet_amount} (倍率: #{odds}, 底分: #{betting_config.difen}, 固定倍率: #{betting_config.bet_rate_num}, 金币比例: #{betting_config.score_rate})"
          )

          # 💰 立即检查玩家余额，余额不足直接返回错误
          if current_points < bet_amount do
            Logger.warning(
              "❌ [INSUFFICIENT_FUNDS] 玩家余额不足，返回特殊错误标识 - 用户: #{user_id}, 当前积分: #{current_points}, 需要积分: #{bet_amount}"
            )

            # 返回特殊的余额不足错误，不包含游戏结果数据
            {:reply, {:error, :insufficient_funds}, state}
          else
            # 余额充足，继续游戏逻辑
            Logger.info("🎰 [FREE_GAME_TEST] 开始检查免费游戏测试 - 用户: #{user_id}")

            Logger.info(
              "🎰 [FREE_GAME_TEST] 当前配置状态: enable_free_game_test=#{current_testing_config.enable_free_game_test}, free_game_test_spins=#{current_testing_config.free_game_test_spins}"
            )

            # 🔄 根据最新配置动态更新免费游戏测试状态
            updated_free_game_test_state =
              if current_testing_config.enable_free_game_test do
                # 如果当前状态未启用，或者触发次数发生变化，则更新状态
                current_state = state.game_data.free_game_test_state

                if not current_state.enabled or
                     current_state.trigger_count != current_testing_config.free_game_test_spins do
                  Logger.info("🔄 [FREE_GAME_TEST] 配置已更改，更新免费游戏测试状态")

                  Logger.info(
                    "🔄 [FREE_GAME_TEST] 旧状态: enabled=#{current_state.enabled}, trigger_count=#{current_state.trigger_count}"
                  )

                  Logger.info(
                    "🔄 [FREE_GAME_TEST] 新状态: enabled=true, trigger_count=#{current_testing_config.free_game_test_spins}"
                  )

                  # 保持现有的玩家计数，只更新配置
                  %{
                    current_state
                    | enabled: true,
                      trigger_count: current_testing_config.free_game_test_spins
                  }
                else
                  current_state
                end
              else
                # 如果配置禁用了免费游戏测试，则禁用状态
                current_state = state.game_data.free_game_test_state

                if current_state.enabled do
                  Logger.info("🔄 [FREE_GAME_TEST] 免费游戏测试已被禁用")
                  %{current_state | enabled: false}
                else
                  current_state
                end
              end

            Logger.info(
              "🎰 [FREE_GAME_TEST] 更新后的免费游戏测试状态: #{inspect(updated_free_game_test_state)}"
            )

            # 记录玩家旋转次数
            updated_free_game_test_state =
              Slot777FreeGameTest.record_player_spin(updated_free_game_test_state, user_id)

            current_count = Map.get(updated_free_game_test_state.player_spin_counts, user_id, 0)

            should_trigger_test_free_game =
              Slot777FreeGameTest.should_trigger_free_game(updated_free_game_test_state, user_id)

            Logger.info(
              "🎰 [FREE_GAME_TEST] 玩家 #{user_id} 旋转计数: #{current_count}/#{updated_free_game_test_state.trigger_count}"
            )

            Logger.info("🎰 [FREE_GAME_TEST] 应该触发免费游戏: #{should_trigger_test_free_game}")

            # 更新状态中的 free_game_test_state
            temp_game_data = %{
              state.game_data
              | free_game_test_state: updated_free_game_test_state
            }

            temp_state = %{state | game_data: temp_game_data}

            # 🌐 生成游戏结果
            game_result =
              cond do
                should_trigger_test_free_game ->
                  Logger.info("🎰 [FREE_GAME_TEST] 玩家 #{user_id} 触发测试免费游戏，强制生成星星结果")
                  # 强制生成免费游戏结果 - 使用最新配置
                  generate_test_free_game_result(
                    odds,
                    current_points,
                    betting_config.difen,
                    betting_config,
                    temp_state
                  )

                true ->
                  # 使用本地游戏逻辑生成游戏结果 - 使用最新配置
                  Slot777GameLogic.generate_game_result(
                    odds,
                    current_points,
                    current_config,
                    betting_config.difen,
                    betting_config
                  )
              end

            # 🔧 使用新的标准积分操作方式
            # 确保所有金额都是整数，避免Money库的浮点数错误
            # 基础奖金
            base_win_money = round(game_result["winmoney"] || 0)
            # Jackpot奖金
            jackpot_amount = round(game_result["jackpotcash"] || 0)

            # 注意：Jackpot奖金将从奖池转账，不计入游戏奖励，避免重复支付
            # 总奖金 = 基础奖金（Jackpot单独处理）
            total_win = base_win_money

            # 先扣除下注金额
            temp_updated_state = subtract_player_points(temp_state, numeric_id, bet_amount)

            # 处理抽水和奖池贡献
            player = elem(player_entry, 1)
            rake_amount = cacl_rake_amount(temp_updated_state, bet_amount)

            if rake_amount > 0 do
              game_rake(temp_updated_state, player, rake_amount)
            end

            # 处理奖池贡献
            contribution_state =
              handle_jackpot_contribution(temp_updated_state, player, bet_amount)

            # 检查是否有Jackpot中奖 - 执行奖池扣除并记录
            jackpot_state =
              if jackpot_amount > 0 do
                # 先执行奖池扣除转账
                jackpot_game_id = get_jackpot_game_id(contribution_state.game_id)

                case Cypridina.Ledger.jackpot_win(
                       jackpot_game_id,
                       :main,
                       player.user_id,
                       jackpot_amount,
                       description: "Slot777 Jackpot中奖",
                       metadata: %{
                         game_type: :slot777,
                         player_id: player.user_id,
                         jackpot_amount: jackpot_amount,
                         win_time: DateTime.utc_now(),
                         seven_count: game_result["sevennum"] || 3,
                         bet_amount: bet_amount
                       }
                     ) do
                  {:ok, _transfer} ->
                    Logger.info(
                      "🎉 [SLOT777] Jackpot中奖支付成功: 玩家#{player.user_id}, 金额#{jackpot_amount}"
                    )

                    # 记录Jackpot中奖到历史记录，传入游戏信息
                    game_info = %{
                      "sevennum" => game_result["sevennum"] || 3,
                      "bet_amount" => bet_amount
                    }

                    updated_state =
                      record_jackpot_win(contribution_state, player, jackpot_amount, game_info)

                    # 触发奖池更新回调
                    new_jackpot_balance =
                      Teen.GameSystem.JackpotManager.get_jackpot_balance(jackpot_game_id, :main)

                    on_jackpot_updated(updated_state, :main, new_jackpot_balance)
                    updated_state

                  {:error, reason} ->
                    Logger.error("❌ [SLOT777] Jackpot中奖支付失败: #{inspect(reason)}")
                    contribution_state
                end
              else
                contribution_state
              end

            # 再增加奖金
            final_updated_state =
              if total_win > 0 do
                add_player_points(jackpot_state, numeric_id, total_win)
              else
                jackpot_state
              end

            # 计算最终积分和变化金额
            final_points = get_player_points(final_updated_state, numeric_id)
            change_amount = final_points - current_points

            Logger.info(
              "🎰 [SLOT777] 积分变化 - 用户: #{user_id}, 原积分: #{current_points}, 下注: #{bet_amount}, 基础奖金: #{base_win_money}, Jackpot奖金: #{jackpot_amount}(已从奖池扣除), 游戏奖励: #{total_win}, 新积分: #{final_points}, 变化: #{change_amount}"
            )

            # 更新房间状态
            new_game_data = %{
              final_updated_state.game_data
              | current_round: final_updated_state.game_data.current_round + 1,
                current_odds: Map.put(final_updated_state.game_data.current_odds, user_id, odds)
            }

            new_state = %{final_updated_state | game_data: new_game_data}

            # 发送游戏结果
            enhanced_result =
              game_result
              # 使用最新积分
              |> Map.put("current_money", final_points)
              # 使用numeric_id作为playerid
              |> Map.put("playerid", numeric_id)
              # 使用变化金额
              |> Map.put("changemoney", change_amount)
              # winmoney = 基础奖金 + Jackpot奖金(用于前端显示)
              |> Map.put("winmoney", base_win_money + jackpot_amount)
              # 保留jackpotcash字段显示Jackpot部分
              |> Map.put("jackpotcash", jackpot_amount)

            # 存储免费游戏信息到状态中，稍后在handle_game_start_request中发送
            # 同时处理免费游戏测试模式的计数器重置
            updated_state =
              if game_result["freetimes"] > 0 do
                free_game_info = %{
                  user_id: user_id,
                  free_times: game_result["freetimes"],
                  odds: odds
                }

                # 如果是测试模式触发的免费游戏，重置计数器
                updated_free_game_test_state =
                  if should_trigger_test_free_game do
                    Logger.info("🎰 [FREE_GAME_TEST] 玩家 #{user_id} 触发免费游戏后重置计数器")

                    Slot777FreeGameTest.reset_player_count(
                      new_state.game_data.free_game_test_state,
                      user_id
                    )
                  else
                    new_state.game_data.free_game_test_state
                  end

                # 更新游戏数据
                updated_game_data = %{
                  new_state.game_data
                  | free_game_test_state: updated_free_game_test_state
                }

                updated_final_state = %{new_state | game_data: updated_game_data}

                put_in(
                  updated_final_state,
                  [:game_data, :pending_free_games, user_id],
                  free_game_info
                )
              else
                new_state
              end

            {:reply, {:ok, enhanced_result}, updated_state}
          end
        end
    end
  end

  # 处理广播消息
  @impl true
  def handle_cast({:broadcast_message, message}, state) do
    Logger.info("📢 [BROADCAST_MESSAGE] 收到广播消息 - 房间: #{state.id}, 消息: #{inspect(message)}")

    # 广播消息给房间内所有玩家
    broadcast_to_room(state, message)

    {:noreply, state}
  end

  # 处理房间终止
  @impl true
  def terminate(reason, state) do
    Logger.info("🏠 [ROOM_TERMINATE] 房间终止 - 房间: #{state.id}, 原因: #{inspect(reason)}")
    # 本地管理，无需注销全局管理器
    :ok
  end

  # 处理切换下注倍率请求
  @impl true
  def handle_call({:switch_bet, user_id, odds}, _from, state) do
    Logger.info("🎰 [SLOT777] 玩家 #{user_id} 切换下注倍率: #{odds}")

    # 通过 user_id 找到对应的玩家
    player_entry =
      Enum.find(state.players, fn {_numeric_id, player} ->
        player.user_id == user_id
      end)

    case player_entry do
      nil ->
        {:reply, {:error, "玩家不在房间中"}, state}

      {_numeric_id, _player} ->
        # 使用房间的合并后配置
        current_config = get_room_config(state)
        betting_config = get_config_section(current_config, [:betting, "betting"])

        # 验证倍率是否有效 - 检查倍率是否在最新配置的值中
        valid_odds = Map.values(betting_config.odds_config)

        if odds not in valid_odds do
          {:reply, {:error, "无效的下注倍率"}, state}
        else
          # 更新玩家当前倍率
          new_current_odds = Map.put(state.game_data.current_odds, user_id, odds)
          new_game_data = %{state.game_data | current_odds: new_current_odds}
          new_state = %{state | game_data: new_game_data}

          result = %{
            "code" => 0,
            "msg" => "切换下注倍率成功",
            "odds" => odds,
            "bet_amount" =>
              round(
                betting_config.difen * odds * betting_config.bet_rate_num *
                  betting_config.score_rate
              )
          }

          {:reply, {:ok, result}, new_state}
        end
    end
  end

  # 处理MainID=4, SubID=40的退出房间协议
  defp handle_exit_room_protocol(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🚪 [EXIT_ROOM] 处理退出房间协议 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}"
    )

    # 清理玩家房间信息
    cleaned_state = cleanup_player_room_data(state, player)

    # 发送退出房间成功响应
    response = %{
      "mainId" => 4,
      # 退出房间响应协议
      "subId" => 14,
      "data" => %{
        "code" => 0,
        "msg" => "退出房间成功",
        "playerid" => numeric_id,
        "room_id" => state.id
      }
    }

    Logger.info("📤 [EXIT_ROOM_RESPONSE] 发送退出房间响应 - 用户: #{user_id}, numeric_id: #{numeric_id}")
    send_to_player(cleaned_state, player, response)

    # 广播玩家离开通知给其他玩家（如果有的话）
    # broadcast_player_left_notification(cleaned_state, player)

    cleaned_state
  end

  # 处理离开房间请求
  defp handle_leave_room_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎰 [LEAVE_ROOM] 处理离开房间请求 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}"
    )

    # 发送离开房间成功响应
    response = %{
      "mainId" => 5,
      # SC_SLOT777_LEAVE_ROOM_P
      "subId" => 12,
      "data" => %{
        "code" => 0,
        "msg" => "离开房间成功"
      }
    }

    send_to_player(state, player, response)
    state
  end

  # 清理玩家房间数据
  defp cleanup_player_room_data(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🧹 [CLEANUP] 清理玩家房间数据 - 用户: #{user_id}, numeric_id: #{numeric_id}")

    # 清理游戏数据中的玩家相关信息
    cleaned_game_data = %{
      state.game_data
      | # 清理当前倍率记录
        current_odds: Map.delete(state.game_data.current_odds, user_id),
        # 清理待处理的免费游戏
        pending_free_games: Map.delete(state.game_data.pending_free_games, user_id)
    }

    # 清理免费游戏测试状态中的玩家数据
    cleaned_free_game_test_state =
      Slot777FreeGameTest.cleanup_player_data(cleaned_game_data.free_game_test_state, user_id)

    # 更新游戏数据
    final_game_data = %{cleaned_game_data | free_game_test_state: cleaned_free_game_test_state}

    # 从玩家列表中移除玩家
    cleaned_players = Map.delete(state.players, numeric_id)

    # 返回清理后的状态
    cleaned_state = %{state | players: cleaned_players, game_data: final_game_data}

    Logger.info(
      "✅ [CLEANUP_COMPLETE] 玩家房间数据清理完成 - 用户: #{user_id}, 剩余玩家数: #{map_size(cleaned_players)}"
    )

    cleaned_state
  end

  # 广播玩家离开通知
  defp broadcast_player_left_notification(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 如果房间还有其他玩家，广播玩家离开通知
    if map_size(state.players) > 0 do
      Logger.info("📢 [PLAYER_LEFT] 广播玩家离开通知 - 用户: #{user_id}, numeric_id: #{numeric_id}")

      notification = %{
        "mainId" => 4,
        # 玩家离开通知协议
        "subId" => 41,
        "data" => %{
          "playerid" => numeric_id,
          "player_name" => "玩家#{numeric_id}",
          "room_id" => state.id,
          "remaining_players" => map_size(state.players)
        }
      }

      broadcast_to_room(state, notification)
    else
      Logger.info("🏠 [EMPTY_ROOM] 房间已无其他玩家，无需广播离开通知 - 房间: #{state.id}")
    end
  end

  # 处理游戏开始请求
  defp handle_game_start_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎰 [GAME_START] 处理游戏开始请求 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}"
    )

    adjustment = get_probability_adjustment(state, 1)
    Logger.info("🎰 [GAME_START] 处理游戏开始请求 - adjustment: #{adjustment}")

    odds = Map.get(data, "odds", 1)

    # 调用内部游戏开始处理
    case handle_call({:start_game, user_id, odds}, nil, state) do
      {:reply, {:ok, result}, new_state} ->
        # 正常游戏结果，发送1001协议
        response_1001 = %{
          "mainId" => 5,
          # SC_SLOT777_GAMESTART_P
          "subId" => 1001,
          "data" => result
        }

        send_to_player(new_state, player, response_1001)

        # 发送金币更新协议
        current_points = get_player_points(new_state, numeric_id)

        money_update_data = %{
          "playerid" => numeric_id,
          "coin" => current_points
        }

        response_money_update = %{
          # MainProto.Game
          "mainId" => 4,
          # Game.SC_ROOM_RESET_COIN_P
          "subId" => 8,
          "data" => money_update_data
        }

        Logger.info(
          "💰 [MONEY_UPDATE] 发送金币更新协议 - 用户: #{user_id}, numeric_id: #{numeric_id}, 积分: #{current_points}"
        )

        send_to_player(new_state, player, response_money_update)

        # 检查是否有待发送的免费游戏，如果有则发送1002协议
        final_state =
          case get_in(new_state, [:game_data, :pending_free_games, user_id]) do
            %{free_times: free_times, odds: free_odds} ->
              Logger.info("🎰 [FREE_GAME] 发送免费游戏 - 用户: #{user_id}, 免费次数: #{free_times}")
              # 生成并发送免费游戏结果，获取更新后的状态
              state_after_free_games =
                generate_and_send_free_games(new_state, user_id, free_times, free_odds)

              # 清除待发送的免费游戏信息
              put_in(
                state_after_free_games,
                [:game_data, :pending_free_games],
                Map.delete(state_after_free_games.game_data.pending_free_games || %{}, user_id)
              )

            _ ->
              new_state
          end

        # 广播游戏结果给其他玩家
        broadcast_game_result(final_state, user_id, result)

        final_state

      {:reply, {:error, :insufficient_funds}, state} ->
        # 💰 余额不足，发送[4][1999]协议，不发送游戏结果
        Logger.warning("❌ [INSUFFICIENT_FUNDS] 玩家余额不足，发送[4][1999]协议 - 用户: #{user_id}")

        insufficient_funds_response = %{
          "mainId" => 4,
          # 余额不足协议
          "subId" => 1999,
          "data" => %{
            "code" => 1,
            "msg" => "余额不足",
            "playerid" => numeric_id
          }
        }

        send_to_player(state, player, insufficient_funds_response)
        state

      {:reply, {:error, reason}, state} ->
        # 其他错误使用原有的错误响应
        send_error_response(state, player, 1001, reason)
        state
    end
  end

  # TODO: 奖池记录请求处理将由新系统实现

  # 处理切换下注倍率请求
  defp handle_switch_bet_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎰 [SWITCH_BET] 处理切换下注倍率请求 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}"
    )

    odds = Map.get(data, "odds", 1)

    # 调用内部切换倍率处理
    case handle_call({:switch_bet, user_id, odds}, nil, state) do
      {:reply, {:ok, result}, new_state} ->
        # 发送切换结果给玩家
        response = %{
          "mainId" => 5,
          # CS_SLOTS_SWITCH_BET_P
          "subId" => 1009,
          "data" => result
        }

        send_to_player(new_state, player, response)
        new_state

      {:reply, {:error, reason}, state} ->
        send_error_response(state, player, 1009, reason)
        state
    end
  end

  # 处理房间信息请求
  defp handle_room_info_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎰 [ROOM_INFO] 处理房间信息请求 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}"
    )

    send_player_list_to_user(state, player)
    # 发送游戏配置和状态
    send_game_config(state, player)
    send_game_state_to_user(state, player)
    # 发送奖池信息
    send_jackpot_info(state, player)

    state
  end

  # 发送错误响应
  defp send_error_response(state, player, sub_id, reason) do
    response = %{
      "mainId" => 5,
      "subId" => sub_id,
      "data" => %{
        "code" => 1,
        "msg" => reason
      }
    }

    send_to_player(state, player, response)
  end

  # 生成并发送免费游戏结果
  defp generate_and_send_free_games(state, user_id, free_times, odds) do
    Logger.info("🎰 [FREE_GAME] 开始生成免费游戏 - 用户: #{user_id}, 免费次数: #{free_times}")

    # 使用房间的合并后配置
    current_config = get_room_config(state)
    betting_config = get_config_section(current_config, [:betting, "betting"])

    # 生成免费游戏结果数组 - 传入配置
    difen = get_config_value(betting_config, [:difen, "difen"], 1)
    free_game_results = generate_free_game_results(free_times, odds, difen, current_config)

    # 计算免费游戏总奖金
    total_free_game_winnings = calculate_total_free_game_winnings(free_game_results)
    Logger.info("🎰 [FREE_GAME] 免费游戏总奖金: #{total_free_game_winnings}")

    # 🔧 使用新的标准积分操作方式（加上免费游戏奖金）
    updated_state =
      if total_free_game_winnings > 0 do
        player = find_player_by_user_id(state, user_id)

        if player do
          numeric_id = player.numeric_id
          current_points = get_player_points(state, numeric_id)
          new_state = add_player_points(state, numeric_id, total_free_game_winnings)
          new_points = get_player_points(new_state, numeric_id)

          Logger.info(
            "🎰 [FREE_GAME] 更新玩家积分 - 用户: #{user_id}, 原积分: #{current_points}, 免费游戏奖金: #{total_free_game_winnings}, 新积分: #{new_points}"
          )

          new_state
        else
          state
        end
      else
        state
      end

    # 发送免费游戏结果给玩家
    response = %{
      "mainId" => 5,
      # SC_SLOT777_FREEGAME_P
      "subId" => 1002,
      "data" => free_game_results
    }

    Logger.info("📤 [FREE_GAME] 发送免费游戏结果 - 用户: #{user_id}, 结果数量: #{map_size(free_game_results)}")
    player = find_player_by_user_id(updated_state, user_id)

    if player do
      send_to_player(updated_state, player, response)

      # 发送金币更新协议 (免费游戏后，使用更新后的积分)
      numeric_id = player.numeric_id
      current_points = get_player_points(updated_state, numeric_id)

      money_update_data = %{
        "playerid" => numeric_id,
        "coin" => current_points
      }

      response_money_update = %{
        # MainProto.Game
        "mainId" => 4,
        # Game.SC_ROOM_RESET_COIN_P
        "subId" => 8,
        "data" => money_update_data
      }

      Logger.info(
        "💰 [FREE_GAME_MONEY_UPDATE] 发送免费游戏后积分更新协议 - 用户: #{user_id}, numeric_id: #{numeric_id}, 积分: #{current_points}"
      )

      send_to_player(updated_state, player, response_money_update)
    else
      Logger.warning("⚠️ [FREE_GAME] 找不到玩家 - user_id: #{user_id}")
    end

    updated_state
  end

  # 计算免费游戏总奖金
  defp calculate_total_free_game_winnings(free_game_results) do
    free_game_results
    |> Enum.reduce(0, fn {_round, result}, acc ->
      win_money = Map.get(result, "winmoney", 0)
      acc + win_money
    end)
  end

  # 生成免费游戏结果数组（下标从1开始）
  defp generate_free_game_results(free_times, odds, difen, config) do
    Logger.info("🎰 [FREE_GAME] 生成 #{free_times} 次免费游戏结果")

    # 使用传入的配置
    betting_config = get_config_section(config, [:betting, "betting"])

    # 生成指定次数的免费游戏结果，转换为以1为起始下标的Map
    1..free_times
    |> Enum.map(fn round ->
      # 每次免费游戏都生成新的结果 - 使用最新配置
      # 免费游戏不扣金币，传入0，使用最新配置
      game_result = Slot777GameLogic.generate_game_result(odds, 0, config, difen, betting_config)

      # 免费游戏结果包含所有必要字段
      result = %{
        "round" => round,
        # 免费游戏中不再触发免费游戏
        "freetimes" => 0,
        "sevennum" => game_result["sevennum"],
        "iconresult" => game_result["iconresult"],
        "linecount" => game_result["linecount"],
        "lineresult" => game_result["lineresult"],
        "totalmult" => game_result["totalmult"],
        "winmoney" => game_result["winmoney"],
        "changemoney" => game_result["changemoney"],
        "jackpotcash" => game_result["jackpotcash"],
        "luckyjackpot" => Map.get(game_result, "luckyjackpot", 0)
      }

      # 返回{下标, 结果}的元组
      {round, result}
    end)
    # 转换为Map，下标从1开始
    |> Enum.into(%{})
  end

  # TODO: 奖池中奖通知将由新系统实现

  # 广播游戏结果给其他玩家 (1008协议)
  defp broadcast_game_result(state, user_id, result) do
    # 通过 user_id 找到对应的玩家获取 numeric_id
    player_entry =
      Enum.find(state.players, fn {_numeric_id, player} ->
        player.user_id == user_id
      end)

    case player_entry do
      {numeric_id, player} ->
        # 获取玩家当前积分
        current_points = get_player_points(state, numeric_id)

        # 计算赢的分数（不包括下注金额）
        win_score = Map.get(result, "winmoney", 0)

        # 计算倍数（总倍数除以10，与前端逻辑一致）
        mult = Map.get(result, "totalmult", 0)

        # 构建1008协议数据，符合前端要求的格式
        broadcast_data = %{
          # 玩家ID
          "playerid" => numeric_id,
          # 赢的分数
          "winscore" => win_score,
          # 赢的倍数
          "mult" => mult,
          # 玩家当前积分
          "pmoney" => current_points
        }

        message = %{
          "mainId" => 5,
          # SC_SLOT777_GAMERESULT_P
          "subId" => 1008,
          "data" => broadcast_data
        }

        Logger.info(
          "📤 [BROADCAST_GAME_RESULT] 广播游戏结果 - 玩家: #{user_id}, numeric_id: #{numeric_id}, 赢分: #{win_score}, 倍数: #{mult}, 余额: #{current_points}"
        )

        # 找到要排除的玩家对象
        exclude_player = find_player_by_user_id(state, user_id)

        if exclude_player do
          # 排除玩家自己
          broadcast_to_room(state, message, [exclude_player])
        else
          broadcast_to_room(state, message)
        end

      nil ->
        Logger.warning("⚠️ [BROADCAST_GAME_RESULT] 找不到玩家 - user_id: #{user_id}")
    end
  end

  # 🧪 临时测试功能 - 可随时删除
  # 生成测试用的免费游戏结果（星星）
  defp generate_test_free_game_result(
         odds,
         current_money,
         difen,
         game_config \\ nil,
         state \\ nil
       ) do
    # 使用配置中的值或默认值
    bet_rate_num = if game_config, do: game_config.bet_rate_num, else: 9
    score_rate = if game_config, do: game_config.score_rate, else: 100
    bet_amount = round(difen * odds * bet_rate_num * score_rate)

    # 得奖不需要乘以bet_rate_num，所以使用不含bet_rate_num的金额计算奖金
    win_bet_amount = round(difen * odds * score_rate)

    # 获取免费游戏测试状态
    free_game_test_state =
      if state, do: state.game_data.free_game_test_state, else: Slot777FreeGameTest.init_state()

    # 生成测试免费游戏矩阵
    test_icon_result =
      Slot777FreeGameTest.generate_test_free_game_matrix(free_game_test_state)
      |> matrix_to_icon_result_map()

    # 获取免费游戏次数
    free_times = Slot777FreeGameTest.get_free_times(free_game_test_state)

    # 计算星星数量
    star_count = count_stars_in_matrix(test_icon_result)

    # 计算总变化金额
    # 免费游戏触发时只扣除下注金额
    total_change = -bet_amount

    Logger.info("🎰 [FREE_GAME_TEST] 生成测试免费游戏结果 - 星星数量: #{star_count}, 免费次数: #{free_times}")

    %{
      "freetimes" => free_times,
      # 免费游戏触发时没有7
      "sevennum" => 0,
      "iconresult" => test_icon_result,
      # 免费游戏触发时没有中奖线
      "linecount" => 0,
      "lineresult" => [],
      "totalmult" => 0,
      # 免费游戏触发时没有直接奖金
      "winmoney" => 0,
      # 总变化金额
      "changemoney" => total_change,
      # 奖池功能将由新系统实现
      "jackpotcash" => 0,
      # 奖池功能将由新系统实现
      "luckyjackpot" => 0
    }
  end

  # TODO: 测试奖池功能将由新系统实现

  # 🧪 临时测试功能控制函数 - 可随时删除
  @doc """
  启用测试模式的便捷函数
  注意：现在需要在每个房间内部单独管理测试模式
  """
  def enable_test_mode() do
    Logger.info("🧪 [TEST_CONTROL] 注意：测试模式现在由每个房间内部管理，请在房间内调用相关函数")
  end

  @doc """
  禁用测试模式的便捷函数
  注意：现在需要在每个房间内部单独管理测试模式
  """
  def disable_test_mode() do
    Logger.info("🧪 [TEST_CONTROL] 注意：测试模式现在由每个房间内部管理，请在房间内调用相关函数")
  end

  @doc """
  重置所有玩家的旋转计数器
  注意：现在需要在每个房间内部单独管理测试模式
  """
  def reset_test_counters() do
    Logger.info("🧪 [TEST_CONTROL] 注意：测试模式现在由每个房间内部管理，请在房间内调用相关函数")
  end

  # 辅助函数：将3x5矩阵转换为icon_result格式的Map
  defp matrix_to_icon_result_map(matrix) do
    matrix
    |> Enum.with_index()
    |> Enum.flat_map(fn {row, row_index} ->
      row
      |> Enum.with_index()
      |> Enum.map(fn {value, col_index} ->
        # 计算位置索引：row * 5 + col + 1 (1-based indexing)
        position = row_index * 5 + col_index + 1
        {to_string(position), value}
      end)
    end)
    |> Enum.into(%{})
  end

  # 辅助函数：计算矩阵中星星的数量
  defp count_stars_in_matrix(icon_result) do
    icon_result
    # 8 = 星星
    |> Enum.count(fn {_pos, value} -> value == 8 end)
  end

  # 辅助函数：通过user_id查找玩家对象
  defp find_player_by_user_id(state, user_id) do
    state.players
    |> Enum.find_value(fn {_id, player} ->
      if player.user_id == user_id, do: player, else: nil
    end)
  end

  # ==================== 配置访问辅助函数 ====================

  # 获取当前房间的合并后配置
  defp get_room_config(state) do
    # 优先使用存储的合并配置，如果没有则使用原始配置
    # 添加安全检查，确保 state.config 存在
    cond do
      Map.has_key?(state, :config) ->
        state.config

      true ->
        Logger.warning("🎰 [SLOT777] 房间状态中没有找到配置，使用空配置")
        %{}
    end
  end

  # 统一访问配置项，支持原子键和字符串键
  defp get_config_section(config, keys) when is_list(keys) do
    Enum.find_value(keys, fn key ->
      case Map.get(config, key) do
        nil -> nil
        value -> value
      end
    end) || %{}
  end

  # 统一获取配置值，支持原子键和字符串键
  defp get_config_value(config, keys, default \\ nil) when is_list(keys) do
    Enum.find_value(keys, fn key ->
      case Map.get(config, key) do
        nil -> nil
        value -> value
      end
    end) || default
  end

  # ==================== 奖池管理函数 ====================

  # 获取奖池使用的游戏ID（保持数字ID与后台管理一致）
  defp get_jackpot_game_id(40), do: "40"
  # 转换为字符串
  defp get_jackpot_game_id(game_id) when is_integer(game_id), do: to_string(game_id)
  # 已经是字符串则直接使用
  defp get_jackpot_game_id(game_id), do: game_id

  # 初始化奖池
  defp init_jackpots(state, config) do
    jackpot_config = get_config_section(config, [:jackpot, "jackpot"])
    jackpot_pools = Map.get(jackpot_config, :pools, [])

    # 将数字 game_id 映射为正确的奖池标识符
    jackpot_game_id = get_jackpot_game_id(state.game_id)
    Logger.info("🎰 [SLOT777] 奖池初始化 - 房间game_id: #{state.game_id}, 奖池game_id: #{jackpot_game_id}")

    case JackpotManager.init_game_jackpots(jackpot_game_id, jackpot_pools) do
      {:ok, jackpot_ids} ->
        Logger.info("✅ [SLOT777] 奖池初始化成功: #{inspect(jackpot_ids)}")

      {:error, errors} ->
        Logger.error("❌ [SLOT777] 奖池初始化失败: #{inspect(errors)}")
    end

    state
  end

  # 实现奖池更新回调
  def on_jackpot_updated(state, jackpot_id, new_balance) do
    Logger.info("💰 [SLOT777] 奖池更新回调被调用: #{jackpot_id} -> #{new_balance}")
    Logger.info("🎮 [SLOT777] 房间玩家数量: #{map_size(state.players)}")

    # 广播奖池更新给所有玩家
    Logger.info("📡 [SLOT777] 开始广播奖池更新...")
    broadcast_jackpot_update(state, jackpot_id, new_balance)
    Logger.info("📡 [SLOT777] 奖池更新广播完成")

    state
  end

  # 广播奖池更新 - 使用1005协议同步Jackpot分数
  defp broadcast_jackpot_update(state, jackpot_id, new_balance) do
    # 使用1005协议格式，与send_jackpot_info保持一致
    update_data = %{
      # 前端期望的jackpot字段
      "jackpot" => new_balance
    }

    broadcast_to_room(state, %{
      # MainProto.Slot777
      "mainId" => 5,
      # SC_SLOT777_JACKPOT_P - 同步Jackpot分数
      "subId" => 1005,
      "data" => update_data
    })

    Logger.info("📤 [BROADCAST_JACKPOT] 广播奖池更新 - 奖池ID: #{jackpot_id}, 新余额: #{new_balance}")
  end

  # 处理奖池贡献
  defp handle_jackpot_contribution(state, player, bet_amount) do
    # 从 JackpotManager 获取贡献率配置
    contribution_rate = JackpotManager.get_total_contribution_rate(state.game_id)
    Logger.info("🎰 [SLOT777] 处理奖池贡献 - 下注金额: #{bet_amount}, 贡献率: #{contribution_rate} (来源: 后台配置)")

    if contribution_rate > 0 do
      contribution_amount = JackpotManager.calculate_contribution(bet_amount, contribution_rate)

      if contribution_amount > 0 do
        # 贡献到主奖池
        case contribute_to_jackpot(state, :main, contribution_amount) do
          {:ok, _transfer} ->
            Logger.info("💰 [SLOT777] 奖池贡献成功: #{contribution_amount}")

            # 🔧 获取更新后的奖池余额并触发回调
            jackpot_game_id = get_jackpot_game_id(state.game_id)
            old_balance = JackpotManager.get_jackpot_balance(jackpot_game_id, :main)
            Logger.info("🔍 [SLOT777] 贡献前余额: #{old_balance}")

            # 等待一小段时间确保转账完成
            :timer.sleep(100)

            new_balance = JackpotManager.get_jackpot_balance(jackpot_game_id, :main)
            Logger.info("🔍 [SLOT777] 贡献后余额: #{new_balance}")
            Logger.info("🔍 [SLOT777] 余额变化: #{new_balance - old_balance}")

            # 触发奖池更新回调
            Logger.info("🔄 [SLOT777] 触发奖池更新回调...")
            on_jackpot_updated(state, :main, new_balance)

          {:error, reason} ->
            Logger.error("❌ [SLOT777] 奖池贡献失败: #{inspect(reason)}")
        end
      end
    end

    state
  end

  # 处理奖池中奖
  defp handle_jackpot_win(state, player, jackpot_amount) do
    if jackpot_amount > 0 do
      jackpot_game_id = get_jackpot_game_id(state.game_id)

      case Cypridina.Ledger.jackpot_win(jackpot_game_id, :main, player.user_id, jackpot_amount,
             description: "Slot777 Jackpot中奖",
             metadata: %{
               game_type: :slot777,
               player_id: player.user_id,
               jackpot_amount: jackpot_amount,
               win_time: DateTime.utc_now()
             }
           ) do
        {:ok, _transfer} ->
          Logger.info("🎉 [SLOT777] Jackpot中奖支付成功: 玩家#{player.user_id}, 金额#{jackpot_amount}")
          # 更新玩家积分显示
          # updated_state = add_player_points(state, player.numeric_id, jackpot_amount)
          updated_state = add_player_points(state, player.numeric_id, 0)
          # 记录Jackpot中奖到本地存储并返回更新后的状态
          record_jackpot_win(updated_state, player, jackpot_amount)

        {:error, reason} ->
          Logger.error("❌ [SLOT777] Jackpot中奖支付失败: #{inspect(reason)}")
          state
      end
    else
      state
    end
  end

  # 处理Jackpot中奖列表请求 (CS_SLOT777_JPLIST_P: 1003)
  defp handle_jackpot_list_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🏆 [JACKPOT_LIST] 处理Jackpot中奖列表请求 - 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}"
    )

    # 获取本地存储的Jackpot中奖记录
    jackpot_records = state.game_data.jackpot_records || []

    # 转换为前端期望的格式：以索引为key的对象
    response_data =
      jackpot_records
      |> Enum.with_index(1)
      |> Enum.into(%{}, fn {record, index} ->
        {to_string(index), record}
      end)

    # 发送SC_SLOT777_JPLIST_P (1004) 响应
    response = %{
      "mainId" => 5,
      # SC_SLOT777_JPLIST_P
      "subId" => 1004,
      "data" => response_data
    }

    Logger.info(
      "📤 [JACKPOT_LIST_RESPONSE] 发送Jackpot中奖列表响应 - 用户: #{user_id}, 记录数: #{length(jackpot_records)}"
    )

    send_to_player(state, player, response)
    state
  end

  # 记录Jackpot中奖到本地存储
  defp record_jackpot_win(state, player, jackpot_amount) do
    record_jackpot_win(state, player, jackpot_amount, %{})
  end

  # 记录Jackpot中奖到本地存储（带游戏信息）
  defp record_jackpot_win(state, player, jackpot_amount, game_info) do
    win_time = DateTime.utc_now()

    # 从游戏信息中获取额外数据
    # 7的个数，默认3
    seven_count = Map.get(game_info, "sevennum", 3)
    # 下注金额
    bet_amount = Map.get(game_info, "bet_amount", 0)

    # 构造中奖记录，按照前端期望的格式
    jackpot_record = %{
      # 前端期望的字段名
      "playerid" => player.user_id,
      # 前端期望的字段名
      "name" => player.user.nickname || "玩家#{player.numeric_id}",
      # 系统头像ID
      "headid" => player.user.avatar_id || 1,
      # 自定义头像URL
      "wxheadurl" => player.user.avatar_url || "",
      # 7的个数
      "sevennum" => seven_count,
      # 下注金额（系统单位，前端会处理显示格式）
      "bet" => bet_amount,
      # 奖金（系统单位，前端会处理显示格式）
      "winscore" => jackpot_amount,
      # 格式化时间
      "time" => format_time_for_display(win_time)
    }

    # 获取当前记录列表
    current_records = state.game_data.jackpot_records || []

    # 添加新记录到列表开头，保持最新的在前面
    new_records = [jackpot_record | current_records]

    # 只保留最近10条记录
    trimmed_records = Enum.take(new_records, 10)

    # 更新state中的记录
    updated_game_data = %{state.game_data | jackpot_records: trimmed_records}
    updated_state = %{state | game_data: updated_game_data}

    # 修复：通过user访问nickname
    Logger.info(
      "🏆 [JACKPOT_RECORD] 记录Jackpot中奖 - 玩家: #{player.user.nickname}, 金额: #{jackpot_amount}, 当前记录数: #{length(trimmed_records)}"
    )

    updated_state
  end

  # 格式化时间为显示格式
  defp format_time_for_display(%DateTime{} = datetime) do
    Calendar.strftime(datetime, "%m-%d %H:%M")
  end

  defp format_time_for_display(_), do: "未知时间"
end
