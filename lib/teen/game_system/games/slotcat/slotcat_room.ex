defmodule Cypridina.Teen.GameSystem.Games.SlotCat.SlotCatRoom do
  @moduledoc """
  SlotCat 老虎机游戏房间实现

  基于 RoomBase 模板实现的单人老虎机游戏房间。
  支持：
  - 5x3 转轮布局，9 条支付线
  - 免费游戏功能
  - Jackpot 奖池系统
  - 游戏控制机制
  - 任务系统
  - 统一的积分管理
  """

  # 先导入必要的模块
  alias Cypridina.Teen.GameSystem.Games.SlotCat.{
    SlotCatGameLogic,
    SlotCatConstants,
    SlotCatConfig
  }

  alias Teen.GameSystem.JackpotManager
  alias Cypridina.Ledger.AccountIdentifier

  # 然后使用 RoomBase
  use Cypridina.Teen.GameSystem.RoomBase, game_type: :slotcat
  require Logger

  # 游戏状态
  @game_state_waiting :waiting
  @game_state_playing :playing
  @game_state_free_game :free_game

  # 房间初始化
  @impl true
  def init_game_logic(state) do
    Logger.info("🎰 [SLOTCAT] 初始化 SlotCat 房间: #{state.id}")

    # 获取默认配置作为基础
    default_config = SlotCatConfig.get_default_config()

    # 深度合并配置，确保所有配置项都有默认值兜底
    merged_config = deep_merge_config(default_config, state.config || %{})
    Logger.info("🎰 [slotcat_CONFIG] 房间配置内容: #{inspect(state.config, pretty: true)}")

    # 提取合并后的配置
    game_config = merged_config.game
    jackpot_config = merged_config.jackpot

    # 初始化奖池
    init_jackpots(state, merged_config)

    # 初始化游戏数据
    game_data = %{
      # 游戏配置 (包含完整的合并配置)
      config: merged_config,
      # 当前状态
      status: @game_state_waiting,
      # 玩家下注记录
      bets: %{},
      # 当前玩家下注倍率
      current_odds: %{},
      # 游戏结果历史
      results: [],
      # 当前回合
      current_round: 0,

      # Jackpot 奖池数据 (从配置初始化)
      jackpot_pools: initialize_jackpot_pools(jackpot_config.pools || []),

      # 免费游戏数据
      free_game: %{
        # 剩余免费次数
        remaining_times: 0,
        # 总免费次数
        total_times: 0,
        # 免费游戏总赢取
        total_win: 0
      },

      # Jackpot 中奖记录
      jackpot_winners: [],

      # 任务系统数据
      task_data: %{},

      # 玩家任务进度
      player_tasks: %{},

      # 游戏统计
      game_stats: %{
        total_games: 0,
        total_bet: 0,
        total_win: 0
      }
    }

    # 启动定时器
    schedule_jackpot_broadcast()

    %{state | game_data: game_data}
  end

  # 玩家加入房间
  @impl true
  def on_player_joined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 玩家加入房间: #{user_id}, numeric_id: #{numeric_id}")

    # 获取玩家真实积分
    initial_points = get_player_points(state, numeric_id)
    Logger.info("🎰 [SLOTCAT] 玩家初始积分: #{initial_points} (机器人: #{player.is_robot})")

    # 更新用户信息
    updated_user_info =
      player.user
      |> Map.put(:money, initial_points)

    # 更新state中的玩家信息
    updated_player = %{player | user: updated_user_info}
    new_state = %{state | players: Map.put(state.players, numeric_id, updated_player)}

    # 初始化玩家游戏数据
    new_game_data = %{
      new_state.game_data
      | current_odds: Map.put(new_state.game_data.current_odds, user_id, 1)
    }

    new_state = %{new_state | game_data: new_game_data}

    # 发送初始化数据给玩家
    send_initial_data(new_state, player)

    new_state
  end

  # 玩家重连加入房间
  @impl true
  def on_player_rejoined(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 玩家重连加入: #{user_id}, numeric_id: #{numeric_id}")

    # 获取玩家真实积分
    current_points = get_player_points(state, numeric_id)
    Logger.info("🎰 [SLOTCAT] 重连玩家积分: #{current_points}")

    # 发送初始化数据给重连玩家
    send_initial_data(state, player)

    state
  end

  # 玩家离开房间
  @impl true
  def on_player_left(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 玩家离开房间: #{user_id}, numeric_id: #{numeric_id}")

    # 清理玩家游戏数据
    new_game_data = %{
      state.game_data
      | current_odds: Map.delete(state.game_data.current_odds, user_id),
        bets: Map.delete(state.game_data.bets, user_id)
    }

    %{state | game_data: new_game_data}
  end

  # 处理游戏消息
  @impl true
  def handle_game_message(state, player, message) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎰 [SLOTCAT] 收到游戏消息 - 房间: #{state.id}, 用户: #{user_id}, numeric_id: #{numeric_id}, 消息: #{inspect(message)}"
    )

    case message do
      # 处理MainID=4的退出房间协议
      %{"mainId" => 4, "subId" => 40} ->
        data = message["data"] || %{}
        handle_exit_room_protocol(state, player, data)

      # 处理MainID=5的SlotCat协议消息
      %{"mainId" => 5, "subId" => sub_id} ->
        data = message["data"] || %{}
        handle_slotcat_protocol(state, player, sub_id, data)

      # 兼容性处理
      %{"cmd" => "get_room_info"} ->
        Logger.info("🏠 [ROOM_INFO_REQUEST] 房间信息请求 - 用户: #{user_id}")
        send_game_config(state, player)
        send_game_state_to_user(state, player)
        state

      %{"cmd" => "request_jackpot"} ->
        Logger.info("🎰 [JACKPOT] Jackpot信息已自动广播，无需手动请求 - 用户: #{user_id}")
        state

      _ ->
        Logger.info(
          "ℹ️ [GAME_MESSAGE] 消息已通过客户端协议处理或为未知消息 - 用户: #{user_id}, 消息: #{inspect(message)}"
        )

        state
    end
  end

  # 处理SlotCat协议消息
  defp handle_slotcat_protocol(state, player, sub_id, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎰 [SLOTCAT_PROTOCOL] 处理协议 - SubID: #{sub_id}, 用户: #{user_id}, numeric_id: #{numeric_id}, 数据: #{inspect(data)}"
    )

    case sub_id do
      # 游戏配置协议
      0 ->
        handle_game_config_request(state, player, data)

      # 离开房间
      2 ->
        handle_leave_room_request(state, player, data)

      # 游戏开始 (CS_SLOTCAT_GAMESTART_P)
      1000 ->
        handle_game_start_request(state, player, data)

      # Jackpot记录请求 (CS_SLOTCAT_JPLIST_P)
      1003 ->
        handle_jackpot_list_request(state, player, data)

      # 领取任务奖励 (CS_SLOTCAT_GETTASK_P)
      1010 ->
        handle_get_task_request(state, player, data)

      # 切换下注倍率 (CS_SLOTS_SWITCH_BET_P)
      1012 ->
        handle_switch_bet_request(state, player, data)

      # 获取房间信息
      1006 ->
        handle_room_info_request(state, player, data)

      # 其他未实现的协议
      _ ->
        Logger.warning("🎰 [SLOTCAT] 未实现的子协议: #{sub_id}")
        send_error_response(state, player, sub_id, "未实现的协议")
        state
    end
  end

  # 发送游戏配置给玩家
  defp send_game_config(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "⚙️ [SEND_CONFIG] 发送游戏配置 - 用户: #{user_id}, numeric_id: #{numeric_id}, 房间: #{state.id}"
    )

    # 使用统一的积分获取方式
    player_money = get_player_points(state, numeric_id)

    # 获取下注倍率配置 (从 state.game_data.config 中动态获取)
    bet_multipliers = state.game_data.config.game.bet_multipliers
    odds_config = build_odds_config(bet_multipliers)

    # 获取玩家上次使用的下注倍率
    last_odds = Map.get(state.game_data.current_odds, user_id, 1)

    player_name = "玩家#{numeric_id}"
    # 构建当前玩家信息
    player_info = %{
      "playerid" => numeric_id,
      "money" => player_money,
      "name" => player_name,
      "seat" => 1
    }

    # 构建玩家列表（slotcat是单机游戏，只包含当前玩家）
    playerlist = %{
      "1" => player_info
    }

    message = %{
      "mainId" => 5,
      "subId" => 0,
      "data" => %{
        # 底分配置
        "difen" => state.game_data.config.game.difen,
        # 下注倍率配置 (动态生成，与 bet_multipliers 保持一致)
        "odds" => odds_config,
        # 玩家上次使用的下注倍率
        "lastodds" => last_odds,
        # 下注限制
        "BetMax" => state.game_data.config.game.max_bet,
        "BetNeed" => state.game_data.config.game.min_bet,
        # 当前Jackpot金额 (使用实时奖池余额)
        # 中间Jackpot的值
        "jackpot" => get_safe_jackpot_balance(state, :center),
        # 左边Jackpot的值
        "jackpotleft" => get_safe_jackpot_balance(state, :left),
        # 右边Jackpot的值
        "jackpotright" => get_safe_jackpot_balance(state, :right),
        # 玩家金币
        "money" => player_money,
        # 当前回合ID
        "room_id" => state.id,
        "roundid" => state.game_data.current_round,
        # 玩家列表
        "playerlist" => playerlist
      }
    }

    Logger.info(
      "📤 [SEND_CONFIG] 配置消息内容 - 协议: 5/0, 底分: #{state.game_data.config.game.difen}, 上次倍率: #{last_odds}, 玩家: #{player_name}"
    )

    send_to_player(state, player, message)
  end

  # 处理游戏开始请求 (增强版本，包含更好的验证和错误处理)
  defp handle_game_start_request(state, player, data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 处理游戏开始请求: #{user_id}, 数据: #{inspect(data)}")

    # 验证输入数据
    case validate_game_start_data(data, state) do
      {:ok, validated_data} ->
        # 检查游戏状态
        case validate_game_state(state) do
          :ok ->
            # 检查玩家状态
            case validate_player_in_room(state, numeric_id) do
              :ok ->
                # 处理有效的游戏开始请求
                execute_game_logic(state, player, validated_data.odds)

              {:error, reason} ->
                Logger.warning("🎰 [SLOTCAT] 玩家验证失败: #{reason}")

                send_error_response(
                  state,
                  player,
                  SlotCatConstants.sc_slotcat_gamestart_p(),
                  reason
                )

                state
            end

          {:error, reason} ->
            Logger.warning("🎰 [SLOTCAT] 游戏状态验证失败: #{reason}")
            send_error_response(state, player, SlotCatConstants.sc_slotcat_gamestart_p(), reason)
            state
        end

      {:error, reason} ->
        Logger.error("🎰 [SLOTCAT] 输入数据验证失败: #{reason}")
        send_error_response(state, player, SlotCatConstants.sc_slotcat_gamestart_p(), reason)
        state
    end
  end

  # 验证游戏开始数据
  defp validate_game_start_data(data, state) do
    with {:ok, odds} <- validate_odds(data, state) do
      {:ok, %{odds: odds}}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # 验证下注倍率
  defp validate_odds(data, state) do
    odds = Map.get(data, "odds", 1)
    valid_multipliers = state.game_data.config.game.bet_multipliers

    cond do
      not is_number(odds) ->
        {:error, "倍率必须是数字"}

      odds not in valid_multipliers ->
        {:error, "无效的下注倍率: #{odds}，有效倍率: #{inspect(valid_multipliers)}"}

      true ->
        {:ok, odds}
    end
  end

  # 验证游戏状态
  defp validate_game_state(state) do
    case state.game_data.status do
      @game_state_waiting -> :ok
      # 免费游戏期间也可以继续
      @game_state_free_game -> :ok
      status -> {:error, "游戏状态不允许开始新回合: #{status}"}
    end
  end

  # 验证玩家是否在房间中
  defp validate_player_in_room(state, numeric_id) do
    if Map.has_key?(state.players, numeric_id) do
      :ok
    else
      {:error, "玩家不在房间中"}
    end
  end

  # 执行游戏逻辑
  defp execute_game_logic(state, player, odds) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 执行游戏逻辑: #{user_id}, 倍率: #{odds}")

    # 计算下注金额 (使用配置中的 bet_rate_num 和 score_rate，参考 Slot777 实现)
    {single_line_bet, total_bet_amount} = calculate_bet_amount(odds, state)

    Logger.info(
      "🎰 [SLOTCAT] 下注计算 - 倍率: #{odds}, 单线下注: #{single_line_bet}, 总下注: #{total_bet_amount}"
    )

    # 检查玩家余额 (提前检查积分是否够下注，不够下注，1000协议，后面的都不用走了)
    current_points = get_player_points(state, numeric_id)

    if current_points < total_bet_amount do
      Logger.warning(
        "🎰 [SLOTCAT] 用户余额不足: #{user_id}, 余额: #{current_points}, 需要: #{total_bet_amount}"
      )

      send_insufficient_balance_message(state, player, total_bet_amount)
      state
    else
      # 执行游戏
      process_game_round(state, player, total_bet_amount, single_line_bet, odds)
    end
  end

  # 计算下注金额 (参考 Slot777 的实现，使用配置中的 bet_rate_num 和 score_rate)
  defp calculate_bet_amount(odds, state) do
    # 获取游戏配置
    game_config = state.game_data.config.game

    # 使用配置中的值，参考 Slot777 的计算方式
    # 计算公式: difen × odds × bet_rate_num × score_rate
    difen = Map.get(game_config, :difen, 100)
    bet_rate_num = Map.get(game_config, :bet_rate_num, 9)
    score_rate = Map.get(game_config, :score_rate, 1)

    # 确保倍率在有效范围内
    bet_multipliers = state.game_data.config.game.bet_multipliers
    valid_odds = if odds in bet_multipliers, do: odds, else: 1

    # 计算总下注金额 (参考 Slot777 的公式) - 支持浮点数倍率
    total_bet_amount_float = difen * valid_odds * bet_rate_num * score_rate

    # 转换为整数，确保 Money 库兼容性
    total_bet_amount = round(total_bet_amount_float)

    # 计算单线下注金额 (总下注 / 线数) - 支持浮点数计算
    paylines = state.game_data.config.game.paylines
    single_line_bet = total_bet_amount_float / paylines

    Logger.info("🎰 [SLOTCAT] 下注计算详情:")
    Logger.info("🎰 [SLOTCAT] - 倍率: #{valid_odds}")
    Logger.info("🎰 [SLOTCAT] - 底分(difen): #{difen}")
    Logger.info("🎰 [SLOTCAT] - 固定倍率(bet_rate_num): #{bet_rate_num}")
    Logger.info("🎰 [SLOTCAT] - 金币比例(score_rate): #{score_rate}")

    Logger.info(
      "🎰 [SLOTCAT] - 计算公式: #{difen} × #{valid_odds} × #{bet_rate_num} × #{score_rate} = #{total_bet_amount_float}"
    )

    Logger.info("🎰 [SLOTCAT] - 总下注(整数): #{total_bet_amount}")
    Logger.info("🎰 [SLOTCAT] - 单线下注: #{single_line_bet}")

    {single_line_bet, total_bet_amount}
  end

  # 处理游戏回合
  defp process_game_round(state, player, total_bet_amount, single_line_bet, odds) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎰 [SLOTCAT] 处理游戏回合: #{user_id}, 总下注: #{total_bet_amount}, 单线下注: #{single_line_bet}"
    )

    # 扣除下注金额
    new_state = subtract_player_points(state, numeric_id, total_bet_amount)

    # 处理抽水和奖池贡献
    rake_amount = cacl_rake_amount(new_state, total_bet_amount)

    if rake_amount > 0 do
      game_rake(new_state, player, rake_amount)
    end

    # 处理奖池贡献
    contribution_state = handle_jackpot_contribution(new_state, player, total_bet_amount)

    # 检查扣除后的余额
    remaining_points = get_player_points(contribution_state, numeric_id)

    if remaining_points < 0 do
      Logger.error("🎰 [SLOTCAT] 扣除下注金额后余额不足: #{remaining_points}")
      send_insufficient_balance_message(state, player, total_bet_amount)
      state
    else
      # 生成游戏结果 (传入单线下注金额和完整配置)
      full_config = contribution_state.config

      case SlotCatGameLogic.generate_game_result(user_id, single_line_bet, 0, false, full_config) do
        {:ok, game_result} ->
          # 处理游戏结果
          handle_game_result(
            contribution_state,
            player,
            total_bet_amount,
            single_line_bet,
            odds,
            game_result
          )

        {:error, reason} ->
          Logger.error("🎰 [SLOTCAT] 生成游戏结果失败: #{reason}")
          # 退还下注金额
          refund_state = add_player_points(contribution_state, numeric_id, total_bet_amount)
          refund_state
      end
    end
  end

  # 定时广播 Jackpot 信息
  defp schedule_jackpot_broadcast do
    # 30秒
    Process.send_after(self(), :jackpot_broadcast, 30_000)
  end

  # 发送初始化数据
  defp send_initial_data(state, player) do
    # 发送玩家列表
    send_player_list_to_user(state, player)

    # 发送游戏配置
    send_game_config(state, player)

    # 发送游戏状态
    send_game_state_to_user(state, player)

    # 发送 Jackpot 信息
    send_jackpot_info(state, player)

    # 发送任务信息
    send_task_info(state, player)
  end

  # 发送游戏状态给指定用户
  defp send_game_state_to_user(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 使用统一的积分获取方式
    player_money = get_player_points(state, numeric_id)

    message = %{
      "mainId" => 4,
      "subId" => 3,
      "data" => %{
        "status" => state.game_data.status,
        "round" => state.game_data.current_round,
        "money" => player_money,
        "jackpot" => get_safe_jackpot_balance(state, :center)
      }
    }

    Logger.info(
      "📤 [SEND_GAME_STATE] 发送游戏状态给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}, 状态: #{state.game_data.status}"
    )

    send_to_player(state, player, message)
  end

  # 发送玩家列表给指定用户（slotcat单机游戏简化版）
  defp send_player_list_to_user(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # slotcat是单机游戏，只发送当前玩家信息
    player_money = get_player_points(state, numeric_id)

    player_info = %{
      "playerid" => numeric_id,
      "money" => player_money,
      "name" => "玩家#{numeric_id}",
      "seat" => 1
    }

    message = %{
      "mainId" => 4,
      "subId" => 2,
      "data" => %{
        "playerlist" => [player_info],
        "totalplayernum" => 1
      }
    }

    Logger.info("📤 [SEND_PLAYER_LIST] 发送玩家信息给用户 - 用户: #{user_id}, numeric_id: #{numeric_id}")
    send_to_player(state, player, message)
  end

  # 发送 Jackpot 信息 (参照前端 onUpdateJackpot 方法的数据格式)
  defp send_jackpot_info(state, player) do
    # 使用实时奖池余额
    center_balance = get_safe_jackpot_balance(state, :center)
    left_balance = get_safe_jackpot_balance(state, :left)
    right_balance = get_safe_jackpot_balance(state, :right)

    # 前端期望的字段: jackpot (中间), jackpotleft (左边), jackpotright (右边)
    jackpot_info = %{
      # 中间Jackpot的值
      "jackpot" => center_balance,
      # 左边Jackpot的值
      "jackpotleft" => left_balance,
      # 右边Jackpot的值
      "jackpotright" => right_balance
    }

    send_to_player(state, player, %{
      "mainId" => 5,
      # 1005协议
      "subId" => SlotCatConstants.sc_slotcat_jackpot_p(),
      "data" => jackpot_info
    })
  end

  # 发送任务信息
  defp send_task_info(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 获取玩家任务进度
    task_config = state.game_data.config.task_system.default_task

    player_task =
      Map.get(state.game_data.player_tasks, user_id, %{
        icon_id: task_config.icon_id,
        current_count: 0,
        total_count: task_config.total_count,
        reward_amount: task_config.reward_amount,
        min_bet_required: task_config.min_bet_required,
        completed: false
      })

    task_info = %{
      "taskicon" => player_task.icon_id,
      "taskcurnum" => player_task.current_count,
      "tasktotalnum" => player_task.total_count,
      "taskmoney" => player_task.reward_amount,
      "taskminbet" => player_task.min_bet_required
    }

    send_to_player(state, player, %{
      "mainId" => 5,
      "subId" => SlotCatConstants.sc_slotcat_taskinfo_p(),
      "data" => task_info
    })
  end

  # 更新玩家任务进度
  defp update_player_task_progress(state, user_id, bet_amount, game_result) do
    # 获取任务配置
    task_config = state.game_data.config.task_system.default_task

    current_task =
      Map.get(state.game_data.player_tasks, user_id, %{
        icon_id: task_config.icon_id,
        current_count: 0,
        total_count: task_config.total_count,
        reward_amount: task_config.reward_amount,
        min_bet_required: task_config.min_bet_required,
        completed: false
      })

    # 检查是否满足最小下注要求
    if bet_amount >= current_task.min_bet_required and not current_task.completed do
      # 检查是否获得了任务图标
      task_icon_count = count_icon_in_result(game_result, current_task.icon_id)

      if task_icon_count > 0 do
        new_count = min(current_task.current_count + task_icon_count, current_task.total_count)
        completed = new_count >= current_task.total_count

        updated_task = %{
          current_task
          | current_count: new_count,
            completed: completed
        }

        new_player_tasks = Map.put(state.game_data.player_tasks, user_id, updated_task)
        new_game_data = %{state.game_data | player_tasks: new_player_tasks}
        new_state = %{state | game_data: new_game_data}

        # 如果任务有更新，发送任务信息
        if new_count > current_task.current_count do
          player = %{user_id: user_id, numeric_id: get_numeric_id_from_user_id(state, user_id)}
          send_task_info(new_state, player)
        end

        new_state
      else
        state
      end
    else
      state
    end
  end

  # 计算游戏结果中指定图标的数量
  defp count_icon_in_result(game_result, target_icon) do
    game_result.icons
    |> List.flatten()
    |> Enum.count(fn icon -> icon == target_icon end)
  end

  # 从用户ID获取数字ID
  defp get_numeric_id_from_user_id(state, user_id) do
    case Enum.find(state.players, fn {_numeric_id, player_data} ->
           player_data.user.id == user_id
         end) do
      {numeric_id, _} -> numeric_id
      nil -> nil
    end
  end

  # 添加缺失的处理函数
  defp handle_game_config_request(state, player, _data) do
    send_game_config(state, player)
    state
  end

  defp handle_leave_room_request(state, player, _data) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTCAT] 处理离开房间请求: #{user_id}")

    # 移除玩家
    {:ok, new_state} = handle_player_leave(state, user_id)
    new_state
  end

  defp handle_room_info_request(state, player, _data) do
    send_game_config(state, player)
    send_game_state_to_user(state, player)
    state
  end

  defp send_error_response(state, player, sub_id, message) do
    error_response = %{
      "mainId" => 5,
      "subId" => sub_id,
      "data" => %{
        "error" => true,
        "message" => message
      }
    }

    send_to_player(state, player, error_response)
  end

  # 处理Jackpot记录请求 (参照前端 onUpdateRecordInfo 方法的数据格式)
  defp handle_jackpot_list_request(state, player, _data) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTCAT] 处理Jackpot记录请求: #{user_id}")

    # 获取真实的 Jackpot 中奖记录
    jackpot_records = get_jackpot_records_for_display(state)

    # 直接发送记录数据，不包装在 "records" 字段中
    # 因为前端直接遍历 info: for (let key in info)
    send_to_player(state, player, %{
      "mainId" => 5,
      # 1004协议
      "subId" => SlotCatConstants.sc_slotcat_jplist_p(),
      "data" => jackpot_records
    })

    state
  end

  # 格式化时间为显示格式
  defp format_time_for_display(timestamp) when is_integer(timestamp) do
    datetime = DateTime.from_unix!(timestamp)
    Calendar.strftime(datetime, "%m-%d %H:%M")
  end

  defp format_time_for_display(%DateTime{} = datetime) do
    Calendar.strftime(datetime, "%m-%d %H:%M")
  end

  defp format_time_for_display(_), do: "未知时间"

  # 处理任务奖励领取请求
  defp handle_get_task_request(state, player, _data) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 处理任务奖励领取请求: #{user_id}")

    # 获取任务奖励配置
    task_reward = state.game_data.config.task_system.default_task.reward_amount

    # 增加玩家积分
    new_state = add_player_points(state, numeric_id, task_reward)

    # 发送任务奖励结果
    send_to_player(new_state, player, %{
      "mainId" => 5,
      "subId" => SlotCatConstants.sc_slotcat_gettask_p(),
      "data" => %{
        "taskmoney" => task_reward,
        "success" => true
      }
    })

    new_state
  end

  # 处理切换下注倍率请求
  defp handle_switch_bet_request(state, player, data) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTCAT] 处理切换下注倍率请求: #{user_id}, 数据: #{inspect(data)}")

    # 获取新的下注倍率
    new_odds = Map.get(data, "odds", 1)

    # 更新玩家的下注倍率
    new_current_odds = Map.put(state.game_data.current_odds, user_id, new_odds)
    new_game_data = %{state.game_data | current_odds: new_current_odds}
    new_state = %{state | game_data: new_game_data}

    # 发送确认消息
    send_to_player(new_state, player, %{
      "mainId" => 5,
      "subId" => SlotCatConstants.cs_slots_switch_bet_p(),
      "data" => %{
        "odds" => new_odds,
        "success" => true
      }
    })

    new_state
  end

  # 处理游戏结果
  defp handle_game_result(state, player, total_bet_amount, single_line_bet, odds, game_result) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    Logger.info(
      "🎰 [SLOTCAT] 处理游戏结果: #{user_id}, 中奖金额: #{game_result.win_amount}, 总下注: #{total_bet_amount}"
    )

    # 计算实际赢取金额 (扣除抽水)
    net_win = game_result.win_amount - total_bet_amount

    actual_win_float =
      if net_win > 0 do
        # 抽水 5%
        draw_amount = trunc(net_win * 5 / 100)
        game_result.win_amount - draw_amount
      else
        game_result.win_amount
      end

    # 转换为整数，确保 Money 库兼容性
    actual_win = round(actual_win_float)

    # 更新用户余额
    new_state =
      if actual_win > 0 do
        add_player_points(state, numeric_id, actual_win)
      else
        state
      end

    # 更新玩家下注倍率
    new_game_data = %{
      new_state.game_data
      | current_odds: Map.put(new_state.game_data.current_odds, user_id, odds),
        current_round: new_state.game_data.current_round + 1
    }

    new_state = %{new_state | game_data: new_game_data}

    # 更新任务进度
    task_updated_state =
      update_player_task_progress(new_state, user_id, total_bet_amount, game_result)

    # 发送游戏结果给玩家（包括处理 Jackpot）
    final_state =
      send_game_result(
        task_updated_state,
        player,
        game_result,
        total_bet_amount,
        single_line_bet,
        actual_win,
        odds
      )

    # 发送积分更新通知（参考 Slot777）
    send_money_update_notification(final_state, player)

    # 广播桌面玩家信息
    broadcast_desk_players_info(final_state)
    # 注意：游戏结果广播移到 send_game_result 内部，确保包含 Jackpot 奖金

    # 处理免费游戏
    if game_result.free_times > 0 do
      handle_free_game(
        final_state,
        player,
        game_result.free_times,
        total_bet_amount,
        single_line_bet,
        odds
      )
    else
      final_state
    end
  end

  # 发送游戏结果
  defp send_game_result(
         state,
         player,
         game_result,
         total_bet_amount,
         single_line_bet,
         actual_win,
         odds
       ) do
    # 使用游戏逻辑模块的格式化函数，确保数据格式正确
    case SlotCatGameLogic.format_result_for_client(game_result) do
      {:ok, formatted_result} ->
        # 计算 Jackpot 奖金 (如果中了 Jackpot，根据不同奖池计算)
        jackpot_cash =
          if game_result.jackpot_count >= 3 do
            # 根据 Jackpot 数量确定奖池类型
            pool_id =
              case game_result.jackpot_count do
                # 左边奖池
                3 -> :left
                # 右边奖池
                4 -> :right
                # 中间奖池
                5 -> :center
                # 默认左边奖池
                _ -> :left
              end

            # 获取实时奖池余额
            pool_balance = get_safe_jackpot_balance(state, pool_id)

            jackpot_percentage =
              SlotCatConstants.get_jackpot_cash_percentage(
                single_line_bet,
                game_result.jackpot_count,
                total_bet_amount
              )

            if jackpot_percentage > 0 do
              # 正确计算 Jackpot 奖金：基于显示金额计算，然后转换为系统金额
              # 1. 将奖池余额转换为显示金额（元）
              pool_display_amount = pool_balance / 100
              # 2. 基于显示金额计算奖金（元）
              jackpot_display_amount = pool_display_amount * jackpot_percentage / 100
              # 3. 转换为系统金额（分）
              jackpot_amount = trunc(jackpot_display_amount * 100)

              # 检查奖池余额是否足够支付奖金
              if jackpot_amount > pool_balance do
                Logger.warning("🎰 [SLOTCAT] 前端显示：奖金超过奖池余额，限制为奖池余额")
                # 限制奖金不超过奖池余额
                pool_balance
              else
                jackpot_amount
              end
            else
              0
            end
          else
            0
          end

        # 计算总奖金 = 连线奖金 + Jackpot奖金，确保前端动画显示正确的金额
        total_win_money = formatted_result["winmoney"] + jackpot_cash

        # 发送游戏开始结果 (确保包含前端期望的所有字段)
        result_data = %{
          "freetimes" => formatted_result["freetimes"],
          "jackpotnum" => formatted_result["jackpotnum"],
          "iconresult" => formatted_result["iconresult"],
          "linecount" => formatted_result["linecount"],
          "lineresult" => formatted_result["lineresult"],
          "totalmult" => formatted_result["totalmult"],
          # 总奖金 = 连线奖金 + Jackpot奖金
          "winmoney" => total_win_money,
          "changemoney" => actual_win - total_bet_amount,
          # 中Jackpot获得的金额
          "jackpotcash" => jackpot_cash,
          # 转盘彩金
          "luckyjackpot" => formatted_result["luckyjackpot"]
        }

        send_to_player(state, player, %{
          "mainId" => 5,
          # 1001协议
          "subId" => SlotCatConstants.sc_slotcat_gamestart_p(),
          "data" => result_data
        })

        # 计算总奖金（普通奖金 + Jackpot 奖金）
        total_win_amount = actual_win + jackpot_cash

        # 广播游戏结果（包含完整的奖金信息）
        broadcast_game_result(
          state,
          player,
          game_result,
          total_bet_amount,
          total_win_amount,
          odds
        )

      {:error, reason} ->
        Logger.error("🎰 [SLOTCAT] 格式化游戏结果失败: #{reason}")
        # 发送错误响应
        send_error_response(state, player, SlotCatConstants.sc_slotcat_gamestart_p(), "游戏结果格式化失败")
    end

    # 处理 Jackpot 中奖 (传入单线下注金额用于计算奖励比例)
    final_state =
      if game_result.jackpot_count >= 3 do
        handle_jackpot_win(
          state,
          player,
          game_result.jackpot_count,
          single_line_bet,
          total_bet_amount
        )
      else
        state
      end

    # 返回最终状态
    final_state
  end

  # 发送积分更新通知（参考 Slot777 实现）
  defp send_money_update_notification(state, player) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    current_points = get_player_points(state, numeric_id)

    money_update_data = %{
      "playerid" => numeric_id,
      "coin" => current_points
    }

    response_money_update = %{
      # MainProto.Game
      "mainId" => 4,
      # Game.SC_ROOM_RESET_COIN_P
      "subId" => 8,
      "data" => money_update_data
    }

    Logger.info(
      "💰 [SLOTCAT_MONEY_UPDATE] 发送积分更新协议 - 用户: #{user_id}, numeric_id: #{numeric_id}, 积分: #{current_points}"
    )

    send_to_player(state, player, response_money_update)
  end

  # 处理免费游戏 (参考 Slot777 实现，一次性生成所有免费游戏结果)
  defp handle_free_game(state, player, free_times, total_bet_amount, single_line_bet, odds) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTCAT] 触发免费游戏: #{user_id}, 次数: #{free_times}")

    # 生成所有免费游戏结果 (参考 Slot777 的实现方式)
    generate_and_send_free_games(state, player, free_times, single_line_bet)
  end

  # 生成并发送免费游戏结果 (参考 Slot777 实现)
  defp generate_and_send_free_games(state, player, free_times, single_line_bet) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    Logger.info("🎰 [SLOTCAT] 开始生成免费游戏 - 用户: #{user_id}, 免费次数: #{free_times}")

    # 生成免费游戏结果数组 (参考 Slot777 的实现)
    free_game_results = generate_free_game_results(free_times, single_line_bet, state)

    # 计算免费游戏总奖金
    total_free_game_winnings = calculate_total_free_game_winnings(free_game_results)
    Logger.info("🎰 [SLOTCAT] 免费游戏总奖金: #{total_free_game_winnings}")

    # 发送1002协议 - 免费游戏结果 (参考 Slot777 的协议格式)
    response_1002 = %{
      "mainId" => 5,
      # 1002
      "subId" => SlotCatConstants.sc_slotcat_freegame_p(),
      "data" => free_game_results
    }

    Logger.info("🎰 [SLOTCAT] 发送1002协议 - 免费游戏结果数据")
    send_to_player(state, player, response_1002)

    # 更新用户积分 (如果有奖金) - 转换为整数确保 Money 库兼容性
    final_state =
      if total_free_game_winnings > 0 do
        total_winnings_int = round(total_free_game_winnings)
        updated_state = add_player_points(state, numeric_id, total_winnings_int)
        # 发送积分更新通知
        send_money_update_notification(updated_state, player)
        updated_state
      else
        state
      end

    # 重置游戏状态
    reset_free_game_state(final_state)
  end

  # 生成免费游戏结果数组 (参考 Slot777 实现)
  defp generate_free_game_results(free_times, single_line_bet, state) do
    full_config = state.config

    # 生成指定次数的免费游戏结果，转换为以1为起始下标的Map (参考 Slot777)
    1..free_times
    |> Enum.map(fn round ->
      # 每次免费游戏都生成新的结果
      case SlotCatGameLogic.generate_game_result(0, single_line_bet, 0, true, full_config) do
        {:ok, game_result} ->
          # 格式化结果为前端期望的格式
          case SlotCatGameLogic.format_result_for_client(game_result) do
            {:ok, formatted_result} ->
              # 免费游戏结果包含所有必要字段 (参考 Slot777 格式)
              result = %{
                "round" => round,
                # 免费游戏中不再触发免费游戏
                "freetimes" => 0,
                "jackpotnum" => formatted_result["jackpotnum"],
                "iconresult" => formatted_result["iconresult"],
                "linecount" => formatted_result["linecount"],
                "lineresult" => formatted_result["lineresult"],
                "totalmult" => formatted_result["totalmult"],
                "winmoney" => formatted_result["winmoney"],
                "changemoney" => formatted_result["changemoney"],
                "jackpotcash" => formatted_result["jackpotcash"],
                "luckyjackpot" => Map.get(formatted_result, "luckyjackpot", 0)
              }

              # 返回{下标, 结果}的元组
              {round, result}

            {:error, _reason} ->
              # 格式化失败，返回空结果
              {round,
               %{
                 "round" => round,
                 "freetimes" => 0,
                 "jackpotnum" => 0,
                 "iconresult" => %{},
                 "linecount" => 0,
                 "lineresult" => %{},
                 "totalmult" => 0,
                 "winmoney" => 0,
                 "changemoney" => 0,
                 "jackpotcash" => 0,
                 "luckyjackpot" => 0
               }}
          end

        {:error, _reason} ->
          # 如果生成失败，返回空结果
          {round,
           %{
             "round" => round,
             "freetimes" => 0,
             "jackpotnum" => 0,
             "iconresult" => %{},
             "linecount" => 0,
             "lineresult" => %{},
             "totalmult" => 0,
             "winmoney" => 0,
             "changemoney" => 0,
             "jackpotcash" => 0,
             "luckyjackpot" => 0
           }}
      end
    end)
    # 转换为Map，下标从1开始
    |> Enum.into(%{})
  end

  # 计算免费游戏总奖金 (参考 Slot777 实现)
  defp calculate_total_free_game_winnings(free_game_results) do
    free_game_results
    |> Enum.reduce(0, fn {_round, result}, acc ->
      acc + Map.get(result, "winmoney", 0)
    end)
  end

  # 重置免费游戏状态
  defp reset_free_game_state(state) do
    new_free_game = %{
      remaining_times: 0,
      total_times: 0,
      total_win: 0
    }

    new_game_data = %{
      state.game_data
      | free_game: new_free_game,
        status: @game_state_waiting
    }

    %{state | game_data: new_game_data}
  end

  # 处理 Jackpot 中奖 (支持三个不同的奖池，使用真实奖池系统)
  defp handle_jackpot_win(state, player, jackpot_count, single_line_bet, total_bet_amount) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 验证 Jackpot 数量是否有效 (3个及以上)
    if not SlotCatConstants.valid_jackpot_count?(jackpot_count) do
      Logger.warning("🎰 [SLOTCAT] 无效的 Jackpot 数量: #{jackpot_count}")
      state
    else
      # 先计算奖金比例，如果为0则不处理Jackpot
      jackpot_percentage =
        SlotCatConstants.get_jackpot_cash_percentage(
          single_line_bet,
          jackpot_count,
          total_bet_amount
        )

      if jackpot_percentage <= 0 do
        Logger.info(
          "🎰 [SLOTCAT] 下注不满足Jackpot奖金要求: 用户#{user_id}, #{jackpot_count}个JACKPOT, 单线下注#{single_line_bet}, 总下注#{total_bet_amount}, 奖金比例#{jackpot_percentage}%"
        )

        state
      else
        # 获取奖池类型和名称
        pool_type = SlotCatConstants.get_jackpot_pool_type(jackpot_count)
        pool_name = SlotCatConstants.get_jackpot_pool_name(jackpot_count)

        Logger.info(
          "🎰 [SLOTCAT] Jackpot 中奖: #{user_id}, #{pool_name}(#{jackpot_count}个), 单线下注: #{single_line_bet}, 奖金比例: #{jackpot_percentage}%"
        )

        # 获取实时奖池余额
        pool_balance = get_safe_jackpot_balance(state, pool_type)

        if pool_balance <= 0 do
          Logger.error("🎰 [SLOTCAT] 奖池余额不足: #{pool_type}, 余额: #{pool_balance}")
          state
        else
          # 正确计算 Jackpot 奖金：基于显示金额计算，然后转换为系统金额
          # 1. 将奖池余额转换为显示金额（元）
          pool_display_amount = pool_balance / 100
          # 2. 基于显示金额计算奖金（元）
          jackpot_display_amount = pool_display_amount * jackpot_percentage / 100
          # 3. 转换为系统金额（分）
          jackpot_amount = trunc(jackpot_display_amount * 100)

          # 检查奖池余额是否足够支付奖金
          if jackpot_amount > pool_balance do
            Logger.error(
              "🎰 [SLOTCAT] 奖金超过奖池余额: 奖金#{jackpot_amount}(₹#{jackpot_amount / 100}), 奖池#{pool_balance}(₹#{pool_balance / 100}), 限制为奖池余额"
            )

            # 限制奖金不超过奖池余额
            jackpot_amount = pool_balance
            jackpot_display_amount = jackpot_amount / 100
          end

          Logger.info("🎉 [SLOTCAT_JACKPOT] #{pool_name} 中奖详情 - 用户: #{user_id}")
          Logger.info("🎉 [SLOTCAT_JACKPOT] - 奖池: #{pool_balance}(分) = ₹#{pool_display_amount}")
          Logger.info("🎉 [SLOTCAT_JACKPOT] - 奖励比例: #{jackpot_percentage}%")

          Logger.info(
            "🎉 [SLOTCAT_JACKPOT] - 奖金: #{jackpot_amount}(分) = ₹#{jackpot_display_amount}"
          )

          # 使用 Ledger 系统处理 Jackpot 中奖 (自动扣除奖池并给玩家加钱)
          jackpot_game_id = get_jackpot_game_id(state.game_id)

          case Cypridina.Ledger.jackpot_win(jackpot_game_id, pool_type, user_id, jackpot_amount,
                 description: "SlotCat Jackpot中奖 - #{pool_name}",
                 metadata: %{
                   game_type: :slotcat,
                   player_id: user_id,
                   jackpot_amount: jackpot_amount,
                   jackpot_count: jackpot_count,
                   pool_type: pool_type,
                   pool_name: pool_name,
                   win_time: DateTime.utc_now()
                 }
               ) do
            {:ok, _transfer} ->
              Logger.info("💰 [SLOTCAT] Jackpot中奖支付成功: 玩家#{user_id}, 金额#{jackpot_amount}")

              # 注意：不需要更新玩家余额显示，因为 Ledger.jackpot_win 已经处理了转账
              # 玩家的实际余额会在下次查询时自动更新
              new_state = state

              # 记录中奖信息
              winner_info = %{
                user_id: user_id,
                player_id: numeric_id,
                user_name: "玩家#{numeric_id}",
                win_time: DateTime.utc_now(),
                bet_amount: total_bet_amount,
                single_line_bet: single_line_bet,
                win_amount: jackpot_amount,
                jackpot_count: jackpot_count,
                pool_type: pool_type,
                pool_name: pool_name,
                jackpot_percentage: jackpot_percentage,
                pool_amount_before: pool_balance,
                pool_amount_after: get_safe_jackpot_balance(new_state, pool_type)
              }

              new_jackpot_winners = [winner_info | Enum.take(state.game_data.jackpot_winners, 9)]

              # 更新房间状态
              new_game_data = %{new_state.game_data | jackpot_winners: new_jackpot_winners}
              final_state = %{new_state | game_data: new_game_data}

              # 发送 Jackpot 中奖通知
              send_jackpot_award(
                final_state,
                player,
                jackpot_amount,
                jackpot_count,
                total_bet_amount
              )

              # 发送积分更新通知
              send_money_update_notification(final_state, player)

              # 广播更新后的 Jackpot 奖池信息给所有玩家
              broadcast_jackpot_update(final_state)

              final_state

            {:error, reason} ->
              Logger.error("❌ [SLOTCAT] Jackpot中奖支付失败: #{inspect(reason)}")
              state
          end
        end
      end
    end
  end

  # 发送 Jackpot 中奖通知 (参照前端 onOhterPalyerJackpot 方法的数据格式)
  defp send_jackpot_award(state, player, jackpot_amount, jackpot_count, total_bet_amount) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    # 构建符合前端期望的 Jackpot 中奖通知数据格式
    # 前端期望字段: playerid, name, headid, wxheadurl, winscore, jackpotnum, bet
    award_data = %{
      # 玩家ID
      "playerid" => numeric_id,
      # 玩家昵称
      "name" => "玩家#{numeric_id}",
      # 玩家系统头像id
      "headid" => 1,
      # 自定义头像url
      "wxheadurl" => "",
      # 玩家赢的金币
      "winscore" => jackpot_amount,
      # JACKPOT的个数 (3=左边, 4=右边, 5=中间)
      "jackpotnum" => jackpot_count,
      # 玩家下注额
      "bet" => total_bet_amount
    }

    # 广播给房间内所有玩家 (包括自己)
    Enum.each(state.players, fn {_player_numeric_id, player_data} ->
      target_player = %{user_id: player_data.user.id, numeric_id: player_data.user.numeric_id}

      send_to_player(state, target_player, %{
        "mainId" => 5,
        # 1006协议
        "subId" => SlotCatConstants.sc_slotcat_jpaward_p(),
        "data" => award_data
      })
    end)

    Logger.info(
      "🎉 [SLOTCAT_JACKPOT_AWARD] 广播Jackpot中奖通知 - 用户: #{user_id}, 奖金: #{jackpot_amount}, Jackpot数量: #{jackpot_count}"
    )
  end

  # 广播 Jackpot 奖池更新 (参照前端 onUpdateJackpot 方法)
  defp broadcast_jackpot_update(state) do
    # 使用实时奖池余额
    center_balance = get_safe_jackpot_balance(state, :center)
    left_balance = get_safe_jackpot_balance(state, :left)
    right_balance = get_safe_jackpot_balance(state, :right)

    # 构建 Jackpot 奖池更新数据
    jackpot_info = %{
      # 中间Jackpot的值
      "jackpot" => center_balance,
      # 左边Jackpot的值
      "jackpotleft" => left_balance,
      # 右边Jackpot的值
      "jackpotright" => right_balance
    }

    # 广播给房间内所有玩家
    Enum.each(state.players, fn {_player_numeric_id, player_data} ->
      target_player = %{user_id: player_data.user.id, numeric_id: player_data.user.numeric_id}

      send_to_player(state, target_player, %{
        "mainId" => 5,
        # 1005协议
        "subId" => SlotCatConstants.sc_slotcat_jackpot_p(),
        "data" => jackpot_info
      })
    end)

    Logger.info(
      "📢 [SLOTCAT_JACKPOT_UPDATE] 广播Jackpot奖池更新 - 中间: #{jackpot_info["jackpot"]}, 左边: #{jackpot_info["jackpotleft"]}, 右边: #{jackpot_info["jackpotright"]}"
    )

    state
  end

  # 广播桌面玩家信息
  defp broadcast_desk_players_info(state) do
    # 构建桌面玩家列表
    desk_players =
      Enum.map(state.players, fn {numeric_id, player_data} ->
        %{
          "playerid" => numeric_id,
          "name" => Map.get(player_data.user, :nickname, "玩家#{numeric_id}"),
          "headid" => Map.get(player_data.user, :head_id, 1),
          "wxheadurl" => Map.get(player_data.user, :avatar_url, ""),
          "pmoney" => get_player_points(state, numeric_id)
        }
      end)

    # 广播给所有玩家
    Enum.each(state.players, fn {_numeric_id, player_data} ->
      player = %{user_id: player_data.user.id, numeric_id: player_data.user.numeric_id}

      send_to_player(state, player, %{
        "mainId" => 5,
        "subId" => SlotCatConstants.sc_slotcat_playerlist_p(),
        "data" => desk_players
      })
    end)
  end

  # 广播游戏结果
  defp broadcast_game_result(state, player, game_result, bet_amount, win_amount, odds) do
    user_id = player.user_id
    numeric_id = player.numeric_id

    result_data = %{
      "playerid" => numeric_id,
      "winscore" => win_amount,
      "mult" => game_result.total_multiplier,
      "pmoney" => get_player_points(state, numeric_id)
    }

    # 广播给所有玩家
    Enum.each(state.players, fn {_id, player_data} ->
      target_player = %{user_id: player_data.user.id, numeric_id: player_data.user.numeric_id}

      send_to_player(state, target_player, %{
        "mainId" => 5,
        "subId" => SlotCatConstants.sc_slotcat_gameresult_p(),
        "data" => result_data
      })
    end)
  end

  # 处理退出房间协议
  defp handle_exit_room_protocol(state, player, _data) do
    user_id = player.user_id
    Logger.info("🎰 [SLOTCAT] 处理退出房间协议: #{user_id}")

    # 移除玩家
    {:ok, new_state} = handle_player_leave(state, user_id)
    new_state
  end

  # 发送余额不足消息 (使用1000协议，提前告知客户端没钱了)
  defp send_insufficient_balance_message(state, player, required_amount \\ 0) do
    user_id = player.user_id
    numeric_id = player.numeric_id
    current_points = get_player_points(state, numeric_id)

    Logger.warning(
      "🎰 [SLOTCAT] 发送余额不足消息: #{user_id}, 当前余额: #{current_points}, 需要金额: #{required_amount}"
    )

    # 使用1000协议返回，告知客户端余额不足，后面的都不用走了
    send_to_player(state, player, %{
      "mainId" => 5,
      # 1000协议
      "subId" => SlotCatConstants.cs_slotcat_gamestart_p(),
      "data" => %{
        # 错误码1000表示余额不足
        "error" => 1000,
        "message" => "余额不足，无法下注",
        "current_balance" => current_points,
        "required_amount" => required_amount
      }
    })
  end

  # 处理定时器消息
  @impl true
  def handle_info(:jackpot_broadcast, state) do
    Logger.debug("🎰 [SLOTCAT] 广播 Jackpot 信息")

    # 向所有玩家广播 Jackpot 信息
    Enum.each(state.players, fn {_numeric_id, player} ->
      send_jackpot_info(state, player)
    end)

    # 重新调度下次广播
    schedule_jackpot_broadcast()

    {:noreply, state}
  end

  @impl true
  def handle_info(msg, state) do
    Logger.debug("🎰 [SLOTCAT_ROOM] 收到未处理的消息: #{inspect(msg)}")
    {:noreply, state}
  end

  # ==================== 私有辅助函数 ====================

  # 构建下注倍率配置 (参照 slot777 实现，动态生成 odds 配置)
  defp build_odds_config(bet_multipliers) do
    bet_multipliers
    |> Enum.with_index(1)
    |> Enum.into(%{}, fn {multiplier, index} ->
      {to_string(index), multiplier}
    end)
  end

  # 从配置初始化奖池
  defp initialize_jackpot_pools(pool_configs) when is_list(pool_configs) do
    Enum.reduce(pool_configs, %{}, fn pool_config, acc ->
      # 安全地处理 identifier，支持原子和字符串两种格式
      pool_name =
        case pool_config.identifier do
          identifier when is_atom(identifier) -> identifier
          identifier when is_binary(identifier) -> String.to_atom(identifier)
          _ -> :unknown_pool
        end

      pool_data = %{
        current_amount: pool_config.base_amount,
        min_amount: Map.get(pool_config, :min_amount, pool_config.base_amount),
        max_amount: pool_config.max_amount,
        # 保存权重信息
        weight: Map.get(pool_config, :weight, 1),
        # 保存优先级信息
        priority: Map.get(pool_config, :priority, 1)
      }

      Map.put(acc, pool_name, pool_data)
    end)
  end

  # 如果没有配置，使用默认奖池
  defp initialize_jackpot_pools(_) do
    %{
      center: %{
        current_amount: 2_500_000,
        min_amount: 125_000,
        max_amount: 50_000_000,
        weight: 5,
        priority: 1
      },
      right: %{
        current_amount: 1_250_000,
        min_amount: 62500,
        max_amount: 25_000_000,
        weight: 3,
        priority: 2
      },
      left: %{
        current_amount: 625_000,
        min_amount: 31250,
        max_amount: 12_500_000,
        weight: 2,
        priority: 3
      }
    }
  end

  # 深度合并配置，确保所有配置项都有默认值兜底
  defp deep_merge_config(default_config, room_config)
       when is_map(default_config) and is_map(room_config) do
    Map.merge(default_config, room_config, fn
      _key, default_value, room_value when is_map(default_value) and is_map(room_value) ->
        deep_merge_config(default_value, room_value)

      _key, _default_value, room_value ->
        room_value
    end)
  end

  # 如果房间配置不是 map，直接返回默认配置
  defp deep_merge_config(default_config, _room_config), do: default_config

  # ==================== 奖池管理函数 ====================

  # 获取奖池使用的游戏ID（参照 Slot777 的实现）
  defp get_jackpot_game_id(42), do: "42"
  defp get_jackpot_game_id(game_id) when is_integer(game_id), do: to_string(game_id)
  defp get_jackpot_game_id(game_id), do: game_id

  # 获取奖池余额 (参照 Slot777 的方式)
  defp get_safe_jackpot_balance(state, jackpot_id) do
    jackpot_game_id = get_jackpot_game_id(state.game_id)
    JackpotManager.get_jackpot_balance(jackpot_game_id, jackpot_id)
  end

  # 初始化奖池
  defp init_jackpots(state, config) do
    jackpot_config = config.jackpot
    jackpot_pools = Map.get(jackpot_config, :pools, [])

    # 使用映射后的游戏ID
    jackpot_game_id = get_jackpot_game_id(state.game_id)

    case JackpotManager.init_game_jackpots(jackpot_game_id, jackpot_pools) do
      {:ok, jackpot_ids} ->
        Logger.info("✅ [SLOTCAT] 奖池初始化成功: #{inspect(jackpot_ids)}")

      {:error, errors} ->
        Logger.error("❌ [SLOTCAT] 奖池初始化失败: #{inspect(errors)}")
    end

    state
  end

  # 实现奖池更新回调
  def on_jackpot_updated(state, jackpot_id, new_balance) do
    Logger.info("💰 [SLOTCAT] 奖池更新: #{jackpot_id} -> #{new_balance}")

    # 更新本地奖池数据
    updated_pools =
      update_local_jackpot_pool(state.game_data.jackpot_pools, jackpot_id, new_balance)

    new_game_data = %{state.game_data | jackpot_pools: updated_pools}
    updated_state = %{state | game_data: new_game_data}

    # 广播奖池更新给所有玩家
    broadcast_jackpot_update(updated_state, jackpot_id, new_balance)

    updated_state
  end

  # 更新本地奖池数据
  defp update_local_jackpot_pool(pools, jackpot_id, new_balance) do
    Enum.map(pools, fn pool ->
      if pool.identifier == jackpot_id do
        %{pool | current_amount: new_balance}
      else
        pool
      end
    end)
  end

  # 广播奖池更新
  defp broadcast_jackpot_update(state, jackpot_id, new_balance) do
    update_data = %{
      "jackpot_id" => jackpot_id,
      "jackpot_amount" => new_balance,
      "updated_at" => DateTime.utc_now() |> DateTime.to_unix()
    }

    broadcast_to_room(state, %{
      # Game protocol
      "mainId" => 5,
      # Jackpot update protocol
      "subId" => SlotCatConstants.cs_slotcat_gettask_p(),
      "data" => update_data
    })
  end

  # 处理奖池贡献（支持两种模式：独立贡献率 或 总贡献率+权重分配）
  defp handle_jackpot_contribution(state, player, bet_amount) do
    # 从 JackpotManager 获取奖池贡献率配置
    jackpot_rates = JackpotManager.get_jackpot_contribution_rates(state.game_id)

    if map_size(jackpot_rates) > 0 do
      # 模式1: 使用后台配置的各奖池独立贡献率
      Logger.info("🎰 [SLOTCAT] 使用独立奖池贡献率: #{inspect(jackpot_rates)}")

      Enum.each(jackpot_rates, fn {pool_id, contribution_rate} ->
        if contribution_rate > 0 do
          contribution_amount = JackpotManager.calculate_contribution(bet_amount, contribution_rate)

          if contribution_amount > 0 do
            case contribute_to_jackpot(state, pool_id, contribution_amount) do
              {:ok, _transfer} ->
                Logger.info("💰 [SLOTCAT] 奖池贡献成功: #{pool_id} -> #{contribution_amount} (贡献率: #{contribution_rate})")

              {:error, reason} ->
                Logger.error("❌ [SLOTCAT] 奖池贡献失败: #{pool_id} -> #{inspect(reason)}")
            end
          end
        end
      end)
    else
      # 模式2: 使用总贡献率 + 权重分配（向后兼容）
      total_contribution_rate = JackpotManager.get_total_contribution_rate(state.game_id)
      Logger.info("🎰 [SLOTCAT] 使用总贡献率+权重分配: #{total_contribution_rate}")

      if total_contribution_rate > 0 do
        total_contribution = JackpotManager.calculate_contribution(bet_amount, total_contribution_rate)

        if total_contribution > 0 do
          # 获取奖池权重配置
          pool_weights = get_pool_weights(state.game_data.jackpot_pools)

          # 分配贡献到各个奖池
          contributions = JackpotManager.distribute_contribution(total_contribution, pool_weights)

          # 执行贡献
          Enum.each(contributions, fn {pool_id, amount} ->
            if amount > 0 do
              case contribute_to_jackpot(state, pool_id, amount) do
                {:ok, _transfer} ->
                  Logger.info("💰 [SLOTCAT] 奖池贡献成功: #{pool_id} -> #{amount} (权重分配)")

                {:error, reason} ->
                  Logger.error("❌ [SLOTCAT] 奖池贡献失败: #{pool_id} -> #{inspect(reason)}")
              end
            end
          end)
        end
      end
    end

    state
  end

  # 获取 Jackpot 中奖记录用于显示
  defp get_jackpot_records_for_display(state) do
    # 获取最近的 Jackpot 中奖记录，最多显示10条
    recent_winners = Enum.take(state.game_data.jackpot_winners, 10)

    # 如果没有真实记录，返回空对象
    if Enum.empty?(recent_winners) do
      %{}
    else
      # 转换为前端期望的格式
      recent_winners
      |> Enum.with_index(1)
      |> Enum.into(%{}, fn {winner, index} ->
        {to_string(index), %{
          "playerid" => winner.player_id,
          "name" => winner.user_name,
          "headid" => winner.player_id || 1,  # 修复：winner_info 中没有 head_id，使用默认值
          "wxheadurl" => "",  # 修复：winner_info 中没有 avatar_url，使用默认值
          "jackpotnum" => winner.jackpot_count,  # jackpot_count 已在计算阶段规范化
          "bet" => winner.bet_amount,
          "winscore" => winner.win_amount,
          "time" => format_time_for_display(winner.win_time)
        }}
      end)
    end
  end

  # 获取奖池权重配置
  defp get_pool_weights(pools) when is_map(pools) do
    # pools 是一个 map，键是奖池名称，值是奖池数据
    Enum.into(pools, %{}, fn {pool_name, pool_data} ->
      # 使用奖池名称作为标识符，权重默认为1
      weight = Map.get(pool_data, :weight, 1)
      {pool_name, weight}
    end)
  end

  # 兼容列表格式的奖池配置
  defp get_pool_weights(pools) when is_list(pools) do
    Enum.into(pools, %{}, fn pool ->
      identifier = Map.get(pool, :identifier, :unknown)
      weight = Map.get(pool, :weight, 1)
      {identifier, weight}
    end)
  end
end
