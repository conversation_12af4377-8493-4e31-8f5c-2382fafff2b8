defmodule Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuJackpot do
  @moduledoc """
  SlotNiu Jackpot管理模块 - 全局内存存储
  负责：
  - 全局Jackpot记录管理
  - 中奖记录存储和排序
  - 所有SlotNiu房间共享访问
  """

  use GenServer
  require Logger

  # Jackpot状态结构
  defstruct [
    # 中奖记录列表
    :records,
    # 总记录数
    :total_records,
    # 最后中奖者
    :last_winner,
    # 最后更新时间
    :last_updated,
    # 假记录生成器定时器引用
    :fake_generator_ref,
    # 假记录生成器是否激活
    :fake_generator_active
  ]

  # 配置常量
  # 最多保存100条记录
  @max_records 100
  # GenServer进程名
  @server_name __MODULE__

  ## Client API

  @doc """
  启动SlotNiu Jackpot管理器
  """
  def start_link(opts \\ []) do
    GenServer.start_link(__MODULE__, opts, name: @server_name)
  end

  @doc """
  添加新的Jackpot中奖记录
  参考旧项目逻辑：只有真正的大奖才会被记录到全局Jackpot列表中
  """
  def add_jackpot_record(
        player_id,
        player_name,
        niunum,
        bet_amount,
        win_amount,
        player_data \\ nil
      ) do
    # 数据验证和清理
    safe_player_id =
      if is_integer(player_id) and player_id > 0, do: player_id, else: :rand.uniform(999_999)

    safe_player_name =
      if is_binary(player_name) and String.length(player_name) > 0,
        do: player_name,
        else: "玩家#{safe_player_id}"

    safe_niunum = if is_integer(niunum) and niunum >= 0, do: niunum, else: 0
    safe_bet_amount = if is_number(bet_amount) and bet_amount > 0, do: bet_amount, else: 0
    safe_win_amount = if is_number(win_amount) and win_amount > 0, do: win_amount, else: 0

    # 根据游戏配置计算正确的倍率
    safe_mult = calculate_correct_multiplier(safe_bet_amount, safe_niunum)

    # 获取玩家头像信息
    {headid, wxheadurl} = extract_player_avatar_info(player_data, safe_player_id)

    record = %{
      player_id: safe_player_id,
      player_name: safe_player_name,
      # 牛的个数（SlotNiu特有）
      niunum: safe_niunum,
      # 下注金额
      bet_amount: safe_bet_amount,
      # 中奖金额
      win_amount: safe_win_amount,
      # Jackpot倍数（根据配置计算）
      mult: safe_mult,
      # 玩家头像ID
      headid: headid,
      # 玩家自定义头像URL
      wxheadurl: wxheadurl,
      timestamp: DateTime.utc_now(),
      type: determine_jackpot_type(safe_niunum, safe_win_amount)
    }

    Logger.info(
      "🎰 [SLOTNIU_JACKPOT] 准备添加记录: #{safe_player_name}, 牛数: #{safe_niunum}, 奖金: #{safe_win_amount}, 倍率: #{safe_mult}"
    )

    GenServer.cast(@server_name, {:add_record, record})
  end

  @doc """
  获取所有Jackpot记录（按奖金从大到小排序）
  如果没有记录，会自动生成初始记录
  """
  def get_jackpot_records() do
    try do
      case GenServer.call(@server_name, :get_records, 5000) do
        {:ok, []} ->
          # 如果没有记录，生成初始记录
          Logger.info("🎰 [SLOTNIU_JACKPOT] 没有记录，生成初始记录")
          generate_initial_records(20)
          # 等待一下让记录生成完成，然后再次获取
          :timer.sleep(100)
          GenServer.call(@server_name, :get_records, 5000)

        result ->
          result
      end
    catch
      :exit, {:noproc, _} ->
        Logger.warning("🎰 [SLOTNIU_JACKPOT] Jackpot进程未启动，返回空记录")
        {:ok, []}

      :exit, {:timeout, _} ->
        Logger.warning("🎰 [SLOTNIU_JACKPOT] 获取记录超时")
        {:error, :timeout}

      :exit, reason ->
        Logger.warning("🎰 [SLOTNIU_JACKPOT] 获取记录失败: #{inspect(reason)}")
        {:error, :jackpot_unavailable}
    end
  end

  @doc """
  获取记录总数
  """
  def get_total_records() do
    try do
      GenServer.call(@server_name, :get_total_records, 5000)
    catch
      :exit, {:noproc, _} ->
        Logger.warning("🎰 [SLOTNIU_JACKPOT] Jackpot进程未启动")
        {:ok, 0}

      :exit, reason ->
        Logger.warning("🎰 [SLOTNIU_JACKPOT] 获取总数失败: #{inspect(reason)}")
        {:error, :jackpot_unavailable}
    end
  end

  @doc """
  清空所有记录（管理员功能）
  """
  def clear_all_records() do
    GenServer.cast(@server_name, :clear_records)
  end

  @doc """
  生成初始记录（当没有记录时调用）
  """
  def generate_initial_records(count \\ 20) do
    GenServer.cast(@server_name, {:generate_initial_records, count})
  end

  @doc """
  格式化记录用于前端显示
  参考前端FMWJackpotRecordLayer.ts的onUpdateRecordInfo方法期望的数据格式
  """
  def format_records_for_frontend(records) do
    Enum.map(records, fn record ->
      # 格式化时间为前端期望的格式
      formatted_time =
        record.timestamp
        |> DateTime.to_unix()
        |> format_timestamp_for_frontend()

      # 确保所有字段都存在且格式正确
      formatted_record = %{
        # 玩家ID（前端用于判断是否为空记录）
        "playerid" => record.player_id,
        # 玩家昵称
        "name" => record.player_name,
        # 玩家系统头像id
        "headid" => Map.get(record, :headid, 1),
        # 自定义头像url
        "wxheadurl" => Map.get(record, :wxheadurl, ""),
        # 牛的个数（SlotNiu特有字段，前端显示为"x#{niunum}"）
        "niunum" => record.niunum,
        # 前端下注倍率（前端显示为"₹#{bet}"）
        "bet" => record.bet_amount,
        # 玩家赢的金币（前端显示为"₹#{winscore}"）
        "winscore" => record.win_amount,
        # 当局时间（前端直接显示）
        "time" => formatted_time,
        # Jackpot倍数（前端显示为"#{mult}x"）
        "mult" => record.mult
      }

      formatted_record
    end)
  end

  ## Server Callbacks

  @impl true
  def init(_opts) do
    # 初始化空状态
    state = %__MODULE__{
      records: [],
      total_records: 0,
      last_winner: nil,
      last_updated: DateTime.utc_now(),
      fake_generator_ref: nil,
      fake_generator_active: false
    }

    Logger.info("🎰 [SLOTNIU_JACKPOT] SlotNiu Jackpot管理器启动")
    Logger.info("🎰 [SLOTNIU_JACKPOT] 初始记录数: #{state.total_records}")

    # 自动启动假记录生成器
    send(self(), :start_fake_generator)

    {:ok, state}
  end

  @impl true
  def handle_cast({:add_record, record}, state) do
    Logger.info(
      "🎰 [SLOTNIU_JACKPOT] 添加新记录 - 玩家: #{record.player_name}, 牛数: #{record.niunum}, 奖金: #{record.win_amount}"
    )

    # 添加新记录到列表开头
    new_records = [record | state.records]

    # 按奖金从大到小排序
    sorted_records = Enum.sort_by(new_records, & &1.win_amount, :desc)

    # 保持最多100条记录
    final_records = Enum.take(sorted_records, @max_records)

    new_state = %{
      state
      | records: final_records,
        total_records: state.total_records + 1,
        last_winner: record,
        last_updated: DateTime.utc_now()
    }

    Logger.info(
      "🎰 [SLOTNIU_JACKPOT] 记录已添加，当前记录数: #{length(final_records)}, 总记录数: #{new_state.total_records}"
    )

    {:noreply, new_state}
  end

  @impl true
  def handle_cast(:clear_records, state) do
    Logger.info("🎰 [SLOTNIU_JACKPOT] 清空所有记录")

    new_state = %{
      state
      | records: [],
        total_records: 0,
        last_winner: nil,
        last_updated: DateTime.utc_now()
    }

    {:noreply, new_state}
  end

  @impl true
  def handle_cast({:generate_initial_records, count}, state) do
    Logger.info("🎰 [SLOTNIU_JACKPOT] 开始生成 #{count} 条初始记录")

    # 生成指定数量的假记录
    initial_records =
      Enum.map(1..count, fn _i ->
        generate_fake_jackpot_record()
      end)

    # 按奖金从大到小排序
    sorted_records = Enum.sort_by(initial_records, & &1.win_amount, :desc)

    # 合并到现有记录中
    all_records = sorted_records ++ state.records
    final_records = Enum.take(all_records, @max_records)

    new_state = %{
      state
      | records: final_records,
        total_records: state.total_records + count,
        last_winner: List.first(sorted_records),
        last_updated: DateTime.utc_now()
    }

    Logger.info("🎰 [SLOTNIU_JACKPOT] 成功生成 #{count} 条初始记录，当前总记录数: #{length(final_records)}")

    {:noreply, new_state}
  end

  @impl true
  def handle_call(:get_records, _from, state) do
    {:reply, {:ok, state.records}, state}
  end

  @impl true
  def handle_call(:get_total_records, _from, state) do
    {:reply, {:ok, state.total_records}, state}
  end

  @impl true
  def handle_info(:start_fake_generator, state) do
    if not state.fake_generator_active do
      # 启动假记录生成器，随机间隔 3-8 秒生成一条记录（您已修改为秒）
      # 3-8秒转换为毫秒
      interval = (3 + :rand.uniform(5)) * 1000
      timer_ref = Process.send_after(self(), :generate_fake_record, interval)

      new_state = %{state | fake_generator_ref: timer_ref, fake_generator_active: true}

      Logger.info("🎰 [SLOTNIU_JACKPOT] 假记录生成器已启动，下次生成时间: #{interval / 1000} 秒后")
      {:noreply, new_state}
    else
      {:noreply, state}
    end
  end

  @impl true
  def handle_info(:generate_fake_record, state) do
    # 生成一条假的中奖记录
    fake_record = generate_fake_jackpot_record()

    # 添加记录到列表开头
    new_records = [fake_record | state.records]

    # 按奖金从大到小排序
    sorted_records = Enum.sort_by(new_records, & &1.win_amount, :desc)

    # 保持最多100条记录
    final_records = Enum.take(sorted_records, @max_records)

    # 安排下一次生成，随机间隔 3-8 秒
    next_interval = (3 + :rand.uniform(5)) * 1000
    timer_ref = Process.send_after(self(), :generate_fake_record, next_interval)

    new_state = %{
      state
      | records: final_records,
        total_records: state.total_records + 1,
        last_winner: fake_record,
        last_updated: DateTime.utc_now(),
        fake_generator_ref: timer_ref
    }

    Logger.info(
      "🎰 [SLOTNIU_JACKPOT] 生成假记录 - 玩家: #{fake_record.player_name}, 牛数: #{fake_record.niunum}, 奖金: #{fake_record.win_amount}, 下次生成: #{next_interval / 1000} 秒后"
    )

    {:noreply, new_state}
  end

  @impl true
  def handle_info(
        {:timeout, timer_ref, :generate_fake_record},
        %{fake_generator_ref: timer_ref} = state
      ) do
    # 处理定时器超时消息（备用处理）
    send(self(), :generate_fake_record)
    {:noreply, state}
  end

  @impl true
  def handle_info(_msg, state) do
    {:noreply, state}
  end

  ## Private Functions

  # 根据前端下注金额和牛头数量计算正确的Jackpot倍率
  # bet_amount现在是前端下注金额（如1800, 900, 180等）
  defp calculate_correct_multiplier(bet_amount, niunum) do
    # 从SlotNiu配置文件获取Jackpot倍率配置
    config = Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig.get_default_config()
    jackpot_multipliers = get_in(config, [:jackpot, :multipliers])

    # 查找匹配的配置
    bet_config = Map.get(jackpot_multipliers, bet_amount)

    if bet_config do
      Map.get(bet_config, niunum, 1)
    else
      # 如果没有直接匹配，按比例计算（基于900的配置）
      base_config = Map.get(jackpot_multipliers, 900, %{})
      base_mult = Map.get(base_config, niunum, 1)

      if base_mult > 1 and bet_amount > 0 do
        # 按比例计算倍率
        calculated_mult = base_mult * bet_amount / 900
        # 保留两位小数
        Float.round(calculated_mult, 2)
      else
        1
      end
    end
  end

  # 根据牛数和奖金确定Jackpot类型
  # 参考旧项目逻辑和SlotNiu游戏特点
  defp determine_jackpot_type(niunum, win_amount) do
    cond do
      # 转盘Jackpot（niunum为0但有奖金）
      niunum == 0 and win_amount > 0 -> :turntable_jackpot
      # 5牛超级大奖
      niunum >= 5 and win_amount >= 50000 -> :grand_jackpot
      # 4牛大奖
      niunum >= 4 and win_amount >= 20000 -> :major_jackpot
      # 3牛中奖
      niunum >= 3 and win_amount >= 5000 -> :minor_jackpot
      # 其他情况（不应该被记录，但作为保险）
      true -> :normal_win
    end
  end

  # 格式化时间戳为前端期望的格式
  defp format_timestamp_for_frontend(unix_timestamp) do
    case DateTime.from_unix(unix_timestamp) do
      {:ok, datetime} ->
        # 转换为本地时间并格式化为 "MM-dd HH:mm" 格式
        datetime
        # 转换为UTC+8
        |> DateTime.add(8 * 3600, :second)
        |> DateTime.to_string()
        # 提取 "MM-dd HH:mm" 部分
        |> String.slice(5, 11)
        |> String.replace("T", " ")

      {:error, _} ->
        "00-00 00:00"
    end
  end

  # 提取玩家头像信息
  defp extract_player_avatar_info(player_data, default_player_id) do
    cond do
      # 如果有完整的玩家数据
      is_map(player_data) ->
        headid =
          cond do
            # 机器人玩家
            Map.get(player_data, :is_robot, false) ->
              Map.get(player_data, :headid, 1 + rem(abs(default_player_id), 10))

            # 真实玩家 - 从user对象获取
            Map.has_key?(player_data, :user) and is_map(player_data.user) ->
              Map.get(player_data.user, :avatar_id, 1)

            # 直接有headid字段
            Map.has_key?(player_data, :headid) ->
              Map.get(player_data, :headid, 1)

            # 默认情况
            true ->
              1
          end

        wxheadurl =
          cond do
            # 机器人没有自定义头像
            Map.get(player_data, :is_robot, false) ->
              ""

            # 真实玩家 - 从user对象获取
            Map.has_key?(player_data, :user) and is_map(player_data.user) ->
              Map.get(player_data.user, :avatar_url, "")

            # 直接有wxheadurl字段
            Map.has_key?(player_data, :wxheadurl) ->
              Map.get(player_data, :wxheadurl, "")

            # 默认情况
            true ->
              ""
          end

        {headid, wxheadurl}

      # 没有玩家数据，使用默认值
      true ->
        {1, ""}
    end
  end

  # 停止假记录生成器定时器
  defp stop_fake_generator_timer(state) do
    if state.fake_generator_ref do
      Process.cancel_timer(state.fake_generator_ref)
    end

    %{state | fake_generator_ref: nil, fake_generator_active: false}
  end

  # 生成假的Jackpot中奖记录
  defp generate_fake_jackpot_record() do
    # 生成假玩家信息
    fake_player = generate_fake_player()

    # 随机选择下注金额（基于配置的下注选项）
    # 对应不同的下注级别
    bet_amounts = [180, 900, 1800, 9000, 18000]
    bet_amount = Enum.random(bet_amounts)

    # 根据下注金额确定牛数范围（越高下注越容易中大奖）
    niunum =
      case bet_amount do
        # 5-9牛
        amount when amount >= 18000 -> 5 + :rand.uniform(4)
        # 4-8牛
        amount when amount >= 9000 -> 4 + :rand.uniform(4)
        # 3-7牛
        amount when amount >= 1800 -> 3 + :rand.uniform(5)
        # 3-5牛
        _ -> 3 + :rand.uniform(3)
      end

    # 计算倍率（使用配置的倍率表）
    mult = calculate_fake_multiplier(bet_amount, niunum)

    # 计算中奖金额
    win_amount = trunc(bet_amount * mult)

    # 添加一些随机变化让金额更真实
    win_amount = trunc(win_amount * (0.9 + :rand.uniform() * 0.2))

    %{
      player_id: fake_player.player_id,
      player_name: fake_player.player_name,
      niunum: niunum,
      bet_amount: bet_amount,
      win_amount: win_amount,
      mult: mult,
      headid: fake_player.headid,
      wxheadurl: fake_player.wxheadurl,
      timestamp: DateTime.utc_now(),
      type: determine_jackpot_type(niunum, win_amount)
    }
  end

  # 生成假玩家信息
  defp generate_fake_player() do
    # 印度风格的名字前缀和后缀
    indian_prefixes = [
      "Raj",
      "Amit",
      "Suresh",
      "Vikram",
      "Arjun",
      "Rohit",
      "Sanjay",
      "Deepak",
      "Anil",
      "Manoj",
      "Ravi",
      "Ashok",
      "Vinod",
      "Ramesh",
      "Mukesh",
      "Ajay",
      "Priya",
      "Sunita",
      "Kavita",
      "Meera",
      "Pooja",
      "Neha",
      "Rekha",
      "Sita",
      "Lucky",
      "Happy",
      "Winner",
      "King",
      "Master",
      "Pro",
      "Star",
      "Hero"
    ]

    indian_suffixes = [
      "Kumar",
      "Singh",
      "Sharma",
      "Gupta",
      "Verma",
      "Agarwal",
      "Jain",
      "Shah",
      "Patel",
      "Reddy",
      "Rao",
      "Nair",
      "Iyer",
      "Menon",
      "Pillai",
      "Das",
      "123",
      "777",
      "999",
      "786",
      "007",
      "888",
      "555",
      "111"
    ]

    # 生成玩家ID（使用正数，模拟真实玩家）
    player_id = 100_000 + :rand.uniform(900_000)

    # 生成玩家名字
    prefix = Enum.random(indian_prefixes)
    suffix = Enum.random(indian_suffixes)
    player_name = "#{prefix}#{suffix}"

    # 生成头像ID（1-24之间）
    headid = 1 + :rand.uniform(23)

    %{
      player_id: player_id,
      player_name: player_name,
      headid: headid,
      # 假玩家不使用自定义头像
      wxheadurl: ""
    }
  end

  # 计算假记录的倍率
  defp calculate_fake_multiplier(bet_amount, niunum) do
    # 使用与真实游戏相同的倍率配置
    config = Cypridina.Teen.GameSystem.Games.SlotNiu.SlotNiuConfig.get_default_config()
    jackpot_multipliers = get_in(config, [:jackpot, :multipliers])

    # 查找匹配的配置
    bet_config = Map.get(jackpot_multipliers, bet_amount)

    if bet_config do
      Map.get(bet_config, niunum, 1)
    else
      # 如果没有直接匹配，按比例计算（基于900的配置）
      base_config = Map.get(jackpot_multipliers, 900, %{})
      base_mult = Map.get(base_config, niunum, 1)

      if base_mult > 1 and bet_amount > 0 do
        # 按比例计算倍率
        calculated_mult = base_mult * bet_amount / 900
        # 保留两位小数
        Float.round(calculated_mult, 2)
      else
        1
      end
    end
  end
end
