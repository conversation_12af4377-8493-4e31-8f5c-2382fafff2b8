defmodule Cypridina.Teen.GameSystem.Games.TeenPatti.TeenPattiRobot do
  @moduledoc """
  Teen Patti优化机器人系统

  实现五种机器人类型，每种都有详细的策略和触发机制：
  1. 激进型机器人（Aggressive）- 主动加注，施加压力
  2. 保守型机器人（Conservative）- 谨慎下注，控制风险
  3. 迷惑型机器人（Tricky）- 反逻辑操作，扰乱判断
  4. 陪玩型机器人（Supportive）- 协助新手，提升体验
  5. 平衡型机器人（Balanced）- 模拟真实玩家，均衡策略
  """

  require Logger

  alias Cypridina.Teen.GameSystem.Games.TeenPatti.{
    TeenPattiConfig,
    TeenPattiPlayer,
    TeenPattiLogic,
    TeenPattiGame
  }

  # 机器人类型定义
  @robot_types %{
    # 激进型
    aggressive: :aggressive,
    # 保守型
    conservative: :conservative,
    # 迷惑型
    tricky: :tricky,
    # 陪玩型
    supportive: :supportive,
    # 平衡型
    balanced: :balanced
  }

  # 机器人行为状态
  @robot_states %{
    # 正常状态
    normal: :normal,
    # 连续好牌状态
    consecutive_good: :consecutive_good,
    # 送分状态
    send_money: :send_money,
    # 收分状态
    collect_money: :collect_money,
    # 诈唬状态
    bluff: :bluff,
    # 保守模式（低峰期）
    conservative_mode: :conservative_mode
  }

  # 机器人类型分布（本地配置，不与 TeenPattiConfig 重复）
  @type_distribution %{
    # 15%激进型
    aggressive: 0.15,
    # 25%保守型
    conservative: 0.25,
    # 20%迷惑型
    tricky: 0.20,
    # 20%陪玩型
    supportive: 0.20,
    # 20%平衡型
    balanced: 0.20
  }

  @doc """
  创建机器人
  """
  def create_robot(robot_id, opts \\ %{}) do
    robot_type = Map.get(opts, :type, choose_random_robot_type())
    nickname = generate_robot_name(robot_type)
    initial_money = generate_initial_money(robot_type)

    # 创建机器人用户数据
    user_data = %{
      numeric_id: robot_id,
      id: robot_id,
      nickname: nickname,
      avatar_id: 1 + rem(abs(robot_id), 10),
      points: initial_money,
      level: :rand.uniform(10),
      is_robot: true,
      created_at: DateTime.utc_now()
    }

    robot_data = %{
      numeric_id: robot_id,
      id: robot_id,
      nickname: nickname,
      avatar_id: 1 + rem(abs(robot_id), 10),
      points: initial_money,
      level: :rand.uniform(10),
      seat: nil,

      # 用户数据引用
      user: user_data,

      # 机器人特有属性
      robot_type: robot_type,
      robot_state: :normal,
      personality_traits: generate_personality_traits(robot_type),

      # 行为参数
      aggression_level: generate_aggression_level(robot_type),
      bluff_tendency: generate_bluff_tendency(robot_type),
      risk_tolerance: generate_risk_tolerance(robot_type),
      patience_level: generate_patience_level(robot_type),

      # 统计数据
      consecutive_wins: 0,
      consecutive_losses: 0,
      total_games: 0,
      win_rate: 0.0,

      # 时间相关
      join_time: DateTime.utc_now(),
      last_action_time: DateTime.utc_now(),

      # 特殊状态
      consecutive_good_rounds: 0,
      send_money_rounds: 0,

      # 🎯 简化：不能弃牌标记（每局重置）
      # true表示这个机器人本局不能弃牌
      no_fold: false,
      created_at: DateTime.utc_now(),
      is_robot: true
    }

    # 打印机器人性格信息
    Logger.info("🤖 [TEEN_PATTI_ROBOT_CREATE] 创建机器人 - ID: #{robot_id}, 昵称: #{nickname}")
    Logger.info("🤖 [TEEN_PATTI_ROBOT_PERSONALITY] 性格类型: #{robot_type}, 初始金币: #{initial_money}")

    Logger.info(
      "🤖 [TEEN_PATTI_ROBOT_PERSONALITY] 激进度: #{robot_data.aggression_level}, 风险承受: #{robot_data.risk_tolerance}"
    )

    Logger.info(
      "🤖 [TEEN_PATTI_ROBOT_PERSONALITY] 诈唬倾向: #{robot_data.bluff_tendency}, 耐心度: #{robot_data.patience_level}"
    )

    robot_data
  end

  @doc """
  机器人决策主函数 - 合并优化版
  """
  def make_decision(robot, cards, game_context) do
    robot_type = Map.get(robot, :robot_type, :balanced)
    robot_state = Map.get(robot, :robot_state, :normal)

    Logger.info("🤖 [TEEN_PATTI_ROBOT_DECISION] 机器人 #{robot.numeric_id} 开始决策")
    Logger.info("🤖 [TEEN_PATTI_ROBOT_PERSONALITY] 性格类型: #{robot_type}, 当前状态: #{robot_state}")

    # 🎯 使用增强决策系统（已合并）
    use_enhanced_system = Map.get(game_context, :use_enhanced_robot, true)

    base_decision =
      if use_enhanced_system do
        Logger.info("🚀 [TEEN_PATTI_ROBOT_ENHANCED] 使用增强机器人决策系统")
        make_enhanced_decision(robot, cards, game_context)
      else
        Logger.info("🔄 [TEEN_PATTI_ROBOT_LEGACY] 使用传统机器人决策系统")
        make_legacy_decision(robot, cards, game_context)
      end

    # 🎯 简化：直接检查机器人的no_fold字段
    final_decision =
      if Map.get(robot, :no_fold, false) and elem(base_decision, 0) == :fold do
        Logger.info("🚫 [NO_FOLD] 机器人#{robot.numeric_id}被标记不能弃牌，强制跟注")
        # 强制跟注
        {:bet, 1}
      else
        base_decision
      end

    Logger.info(
      "🤖 [TEEN_PATTI_ROBOT_DECISION] 机器人 #{robot.numeric_id} 最终决策: #{inspect(final_decision)}"
    )

    final_decision
  end

  @doc """
  增强机器人决策系统（合并版）
  """
  def make_enhanced_decision(robot, cards, game_context) do
    # 计算手牌强度和潜力
    hand_analysis = analyze_hand_comprehensive(cards, game_context)

    # 分析游戏环境
    game_analysis = analyze_game_environment(game_context)

    # 获取机器人状态和性格
    robot_type = Map.get(robot, :robot_type, :balanced)
    robot_state = Map.get(robot, :robot_state, :normal)

    Logger.info("🤖 [ENHANCED_ROBOT] 机器人 #{robot.numeric_id} 增强决策")
    Logger.info("🤖 [ENHANCED_ROBOT] 手牌分析: #{inspect(hand_analysis)}")
    Logger.info("🤖 [ENHANCED_ROBOT] 游戏分析: #{inspect(game_analysis)}")

    # 根据机器人类型和状态做决策
    decision =
      case robot_type do
        :aggressive -> make_enhanced_aggressive_decision(robot, hand_analysis, game_analysis)
        :conservative -> make_enhanced_conservative_decision(robot, hand_analysis, game_analysis)
        :tricky -> make_enhanced_tricky_decision(robot, hand_analysis, game_analysis)
        :supportive -> make_enhanced_supportive_decision(robot, hand_analysis, game_analysis)
        :balanced -> make_enhanced_balanced_decision(robot, hand_analysis, game_analysis)
        _ -> make_enhanced_balanced_decision(robot, hand_analysis, game_analysis)
      end

    Logger.info("🤖 [ENHANCED_ROBOT] 最终决策: #{inspect(decision)}")
    decision
  end

  @doc """
  传统机器人决策系统（保持向后兼容）
  """
  def make_legacy_decision(robot, cards, game_context) do
    robot_type = Map.get(robot, :robot_type, :balanced)
    robot_state = Map.get(robot, :robot_state, :normal)

    # 分析手牌强度
    hand_strength = TeenPattiLogic.calculate_hand_strength(cards)
    Logger.info("🤖 [TEEN_PATTI_ROBOT_ANALYSIS] 手牌强度: #{hand_strength}")

    # 根据机器人类型和状态做决策
    base_decision =
      case robot_type do
        :aggressive ->
          Logger.info("🔥 [TEEN_PATTI_ROBOT_AGGRESSIVE] 激进型机器人决策中...")
          make_aggressive_decision(robot, cards, hand_strength, game_context)

        :conservative ->
          Logger.info("🛡️ [TEEN_PATTI_ROBOT_CONSERVATIVE] 保守型机器人决策中...")
          make_conservative_decision(robot, cards, hand_strength, game_context)

        :tricky ->
          Logger.info("🎭 [TEEN_PATTI_ROBOT_TRICKY] 迷惑型机器人决策中...")
          make_tricky_decision(robot, cards, hand_strength, game_context)

        :supportive ->
          Logger.info("🤝 [TEEN_PATTI_ROBOT_SUPPORTIVE] 陪玩型机器人决策中...")
          make_supportive_decision(robot, cards, hand_strength, game_context)

        :balanced ->
          Logger.info("⚖️ [TEEN_PATTI_ROBOT_BALANCED] 平衡型机器人决策中...")
          make_balanced_decision(robot, cards, hand_strength, game_context)

        _ ->
          Logger.info("⚖️ [TEEN_PATTI_ROBOT_DEFAULT] 默认平衡型机器人决策中...")
          make_balanced_decision(robot, cards, hand_strength, game_context)
      end

    # 根据特殊状态调整决策
    final_decision = adjust_decision_by_state(base_decision, robot_state, robot, game_context)

    Logger.info(
      "🤖 [TEEN_PATTI_ROBOT_RESULT] 机器人 #{robot.numeric_id} 最终决策: #{inspect(final_decision)}"
    )

    final_decision
  end

  @doc """
  激进型机器人决策 - 优化版
  """
  defp make_aggressive_decision(robot, cards, hand_strength, game_context) do
    current_bet = Map.get(game_context, :current_bet, 0)
    pot_total = Map.get(game_context, :pot_total, 0)
    turn_count = Map.get(game_context, :turn_count, 0)
    player_count = Map.get(game_context, :active_player_count, 2)
    robot_state = Map.get(robot, :robot_state, :normal)

    # 🔥 激进型特殊状态处理
    case robot_state do
      :collect_money ->
        Logger.info("🔥 [TEEN_PATTI_ROBOT_AGGRESSIVE] 激进型收分模式 - 更加激进")
        make_aggressive_collect_decision(robot, hand_strength, game_context)

      :bluff ->
        Logger.info("🔥 [TEEN_PATTI_ROBOT_AGGRESSIVE] 激进型诈唬模式")
        make_aggressive_bluff_decision(robot, hand_strength, game_context)

      _ ->
        make_aggressive_normal_decision(robot, hand_strength, game_context)
    end
  end

  # 🔥 激进型收分模式决策
  defp make_aggressive_collect_decision(_robot, hand_strength, _game_context) do
    cond do
      hand_strength >= 300 ->
        # 收分模式下更激进
        fill = if :rand.uniform() < 0.9, do: 3, else: 2
        Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型收分模式强势加注 #{fill}倍")
        {:bet, fill}

      hand_strength >= 200 ->
        Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型收分模式中等牌加注")
        {:bet, 2}

      true ->
        Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型收分模式弱牌弃牌")
        {:fold, nil}
    end
  end

  # 🔥 激进型诈唬模式决策
  defp make_aggressive_bluff_decision(_robot, _hand_strength, _game_context) do
    # 诈唬模式：不管牌好坏都激进
    fill = if :rand.uniform() < 0.7, do: 3, else: 2
    Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型诈唬模式强势加注 #{fill}倍")
    {:bet, fill}
  end

  # 🔥 激进型正常决策
  defp make_aggressive_normal_decision(_robot, hand_strength, game_context) do
    _current_bet = Map.get(game_context, :current_bet, 0)
    _pot_total = Map.get(game_context, :pot_total, 0)
    turn_count = Map.get(game_context, :turn_count, 0)
    _player_count = Map.get(game_context, :active_player_count, 2)

    cond do
      # 超强牌时必定比牌或大幅加注
      hand_strength >= 600 ->
        if turn_count >= 3 and can_compete?(game_context) and :rand.uniform() < 0.5 do
          Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型超强牌比牌")
          {:competition, choose_competition_target(game_context)}
        else
          # 80%概率大幅加注
          fill = if :rand.uniform() < 0.8, do: 3, else: 2
          Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型超强牌加注 #{fill}倍")
          {:bet, fill}
        end

      # 强牌时激进加注
      hand_strength >= 400 ->
        if turn_count >= 4 and can_compete?(game_context) and :rand.uniform() < 0.4 do
          Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型强牌比牌")
          {:competition, choose_competition_target(game_context)}
        else
          # 70%概率加注
          fill = if :rand.uniform() < 0.7, do: 2, else: 1
          Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型强牌加注 #{fill}倍")
          {:bet, fill}
        end

      # 中等牌时适度激进
      hand_strength >= 250 ->
        if :rand.uniform() < 0.6 do
          # 40%概率加注
          fill = if :rand.uniform() < 0.4, do: 2, else: 1
          Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型中等牌加注 #{fill}倍")
          {:bet, fill}
        else
          Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型中等牌看牌")
          {:look, nil}
        end

      # 弱牌时偶尔虚张声势
      hand_strength >= 150 ->
        if :rand.uniform() < 0.3 do
          Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型弱牌诈唬跟注")
          # 跟注诈唬
          {:bet, 1}
        else
          Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型弱牌弃牌")
          {:fold, nil}
        end

      # 很弱的牌偶尔大胆诈唬
      true ->
        if :rand.uniform() < 0.15 do
          Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型极弱牌大胆诈唬")
          # 大胆诈唬加注
          {:bet, 2}
        else
          Logger.info("🔥 [TEEN_PATTI_ROBOT_BEHAVIOR] 激进型极弱牌弃牌")
          {:fold, nil}
        end
    end
  end

  @doc """
  保守型机器人决策
  """
  defp make_conservative_decision(robot, cards, hand_strength, game_context) do
    current_bet = Map.get(game_context, :current_bet, 0)
    is_seen = Map.get(game_context, :is_seen, false)

    cond do
      # 很强的牌才加注
      hand_strength >= 400 ->
        # 30%概率加注
        fill = if :rand.uniform() < 0.3, do: 2, else: 1
        {:bet, fill}

      # 中等偏上的牌谨慎处理
      hand_strength >= 250 ->
        if not is_seen and :rand.uniform() < 0.6 do
          {:look, nil}
        else
          # 跟注
          {:bet, 1}
        end

      # 中等牌有选择性跟注
      hand_strength >= 150 ->
        if :rand.uniform() < 0.5 do
          # 跟注
          {:bet, 1}
        else
          {:fold, nil}
        end

      # 弱牌直接弃牌
      true ->
        {:fold, nil}
    end
  end

  @doc """
  迷惑型机器人决策
  """
  defp make_tricky_decision(robot, cards, hand_strength, game_context) do
    turn_count = Map.get(game_context, :turn_count, 0)

    # 第4轮必定诈唬
    if turn_count == 4 do
      Logger.info("🎭 [TEEN_PATTI_ROBOT_BEHAVIOR] 迷惑型机器人第4轮诈唬策略触发")
      # 加注诈唬
      {:bet, 2}
    else
      cond do
        # 强牌时50%概率弃牌（迷惑）
        hand_strength >= 500 ->
          if :rand.uniform() < 0.5 do
            Logger.info("🎭 [TEEN_PATTI_ROBOT_BEHAVIOR] 迷惑型机器人强牌弃牌 - 反逻辑操作")
            {:fold, nil}
          else
            Logger.info("🎭 [TEEN_PATTI_ROBOT_BEHAVIOR] 迷惑型机器人强牌加注")
            # 加注
            {:bet, 2}
          end

        # 中等牌正常处理
        hand_strength >= 300 ->
          Logger.info("🎭 [TEEN_PATTI_ROBOT_BEHAVIOR] 迷惑型机器人中等牌跟注")
          # 跟注
          {:bet, 1}

        # 弱牌时30%概率诈唬加注
        hand_strength >= 100 ->
          if :rand.uniform() < 0.3 do
            Logger.info("🎭 [TEEN_PATTI_ROBOT_BEHAVIOR] 迷惑型机器人弱牌诈唬加注")
            # 诈唬加注
            {:bet, 2}
          else
            Logger.info("🎭 [TEEN_PATTI_ROBOT_BEHAVIOR] 迷惑型机器人弱牌弃牌")
            {:fold, nil}
          end

        # 很弱的牌
        true ->
          Logger.info("🎭 [TEEN_PATTI_ROBOT_BEHAVIOR] 迷惑型机器人极弱牌弃牌")
          {:fold, nil}
      end
    end
  end

  @doc """
  陪玩型机器人决策
  """
  defp make_supportive_decision(robot, cards, hand_strength, game_context) do
    player_luck = get_player_luck_from_context(game_context)
    player_consecutive_losses = get_player_consecutive_losses(game_context)

    # 检查是否需要送分
    should_send_money = player_consecutive_losses >= 3 or player_luck < 200

    if should_send_money do
      Logger.info(
        "🤝 [TEEN_PATTI_ROBOT_BEHAVIOR] 陪玩型机器人进入送分模式 - 玩家连败: #{player_consecutive_losses}, 幸运值: #{player_luck}"
      )

      # 送分：即使有好牌也故意示弱
      if hand_strength >= 400 do
        # 好牌时故意小注或弃牌
        if :rand.uniform() < 0.6 do
          Logger.info("🤝 [TEEN_PATTI_ROBOT_BEHAVIOR] 陪玩型机器人好牌故意小注送分")
          # 小注
          {:bet, 1}
        else
          Logger.info("🤝 [TEEN_PATTI_ROBOT_BEHAVIOR] 陪玩型机器人好牌故意弃牌送分")
          # 弃牌
          {:fold, nil}
        end
      else
        Logger.info("🤝 [TEEN_PATTI_ROBOT_BEHAVIOR] 陪玩型机器人中等牌跟注送分")
        # 跟注
        {:bet, 1}
      end
    else
      Logger.info("🤝 [TEEN_PATTI_ROBOT_BEHAVIOR] 陪玩型机器人正常陪玩模式")
      # 正常陪玩
      cond do
        hand_strength >= 300 ->
          Logger.info("🤝 [TEEN_PATTI_ROBOT_BEHAVIOR] 陪玩型机器人好牌跟注")
          # 跟注
          {:bet, 1}

        hand_strength >= 150 ->
          if :rand.uniform() < 0.7 do
            Logger.info("🤝 [TEEN_PATTI_ROBOT_BEHAVIOR] 陪玩型机器人中等牌跟注")
            {:bet, 1}
          else
            Logger.info("🤝 [TEEN_PATTI_ROBOT_BEHAVIOR] 陪玩型机器人中等牌弃牌")
            {:fold, nil}
          end

        true ->
          Logger.info("🤝 [TEEN_PATTI_ROBOT_BEHAVIOR] 陪玩型机器人弱牌弃牌")
          {:fold, nil}
      end
    end
  end

  @doc """
  平衡型机器人决策
  """
  defp make_balanced_decision(robot, cards, hand_strength, game_context) do
    current_bet = Map.get(game_context, :current_bet, 0)
    pot_total = Map.get(game_context, :pot_total, 0)

    cond do
      # 强牌时有概率比牌或加注
      hand_strength >= 350 ->
        if :rand.uniform() < 0.2 and can_compete?(game_context) do
          {:competition, choose_competition_target(game_context)}
        else
          # 40%概率加注
          fill = if :rand.uniform() < 0.4, do: 2, else: 1
          {:bet, fill}
        end

      # 中等牌时谨慎处理
      hand_strength >= 200 ->
        if :rand.uniform() < 0.4 do
          {:look, nil}
        else
          # 20%概率加注
          fill = if :rand.uniform() < 0.2, do: 2, else: 1
          {:bet, fill}
        end

      # 弱牌时偶尔跟注
      hand_strength >= 100 ->
        if :rand.uniform() < 0.3 do
          # 跟注
          {:bet, 1}
        else
          {:fold, nil}
        end

      # 很弱的牌
      true ->
        {:fold, nil}
    end
  end

  @doc """
  根据特殊状态调整决策
  """
  defp adjust_decision_by_state(base_decision, robot_state, robot, game_context) do
    case robot_state do
      :consecutive_good ->
        # 连续好牌状态：更激进
        enhance_aggression(base_decision)

      :send_money ->
        # 送分状态：故意示弱
        weaken_decision(base_decision)

      :collect_money ->
        # 收分状态：更保守
        strengthen_decision(base_decision)

      :conservative_mode ->
        # 保守模式（低峰期）
        make_conservative_adjustment(base_decision)

      _ ->
        base_decision
    end
  end

  @doc """
  增强激进性
  """
  # 跟注变加注
  defp enhance_aggression({:bet, fill}) when fill == 1, do: {:bet, 2}
  # 弃牌变跟注
  defp enhance_aggression({:fold, _}), do: {:bet, 1}
  defp enhance_aggression(decision), do: decision

  @doc """
  削弱决策（送分用）
  """
  # 加注变跟注
  defp weaken_decision({:bet, fill}) when fill > 1, do: {:bet, 1}

  defp weaken_decision({:bet, 1}),
    do: if(:rand.uniform() < 0.5, do: {:fold, nil}, else: {:bet, 1})

  defp weaken_decision(decision), do: decision

  @doc """
  加强决策（收分用）
  """
  # 弃牌变跟注
  defp strengthen_decision({:fold, _}), do: {:bet, 1}
  # 跟注变加注
  defp strengthen_decision({:bet, 1}), do: {:bet, 2}
  defp strengthen_decision(decision), do: decision

  @doc """
  保守调整（低峰期用）
  """
  # 加注变跟注
  defp make_conservative_adjustment({:bet, fill}) when fill > 1, do: {:bet, 1}
  # 比牌变跟注
  defp make_conservative_adjustment({:competition, _}), do: {:bet, 1}
  defp make_conservative_adjustment(decision), do: decision

  @doc """
  计算操作延迟
  """
  def calculate_action_delay(robot, action_type) do
    # 根据机器人类型获取对应的延迟配置
    robot_type = Map.get(robot, :robot_type, :balanced)
    delay_config = TeenPattiConfig.get_robot_operation_delay(robot_type)

    base_min = delay_config.min_think_time
    base_max = delay_config.max_think_time

    # 根据操作类型微调延迟
    {min_delay, max_delay} =
      case action_type do
        # 看牌稍快
        :look_cards -> {base_min - 200, base_max - 500}
        # 弃牌较快
        :fold -> {base_min - 300, base_max - 800}
        # 下注正常
        :bet -> {base_min, base_max}
        # 比牌慢一些
        :competition -> {base_min + 500, base_max + 1000}
        # 其他操作正常
        _ -> {base_min, base_max}
      end

    # 根据动作类型调整
    {final_min, final_max} =
      case action_type do
        # 加注需要更多思考
        :bet when elem(action_type, 1) > 1 -> {min_delay + 500, max_delay + 1000}
        # 比牌需要更多思考
        :competition -> {min_delay + 1000, max_delay + 2000}
        # 弃牌较快
        :fold -> {min_delay - 500, max_delay - 500}
        _ -> {min_delay, max_delay}
      end

    # 确保延迟在合理范围内
    min_delay = max(1000, final_min)
    max_delay = max(min_delay + 1000, final_max)

    min_delay + :rand.uniform(max_delay - min_delay)
  end

  @doc """
  检查是否可以比牌 - 优化版
  """
  defp can_compete?(game_context) do
    turn_count = Map.get(game_context, :turn_count, 0)
    active_players = Map.get(game_context, :active_player_count, 2)
    min_turn_for_competition = Map.get(game_context, :min_turn_for_competition, 3)
    game_state = Map.get(game_context, :game_state, :betting)

    # 🎯 优化：更严格的比牌条件检查
    turn_count >= min_turn_for_competition and
      active_players >= 2 and
      game_state == :betting and
      not Map.get(game_context, :is_in_competition, false)
  end

  @doc """
  选择比牌目标 - 优化版
  """
  defp choose_competition_target(game_context) do
    active_players = Map.get(game_context, :active_players, [])
    current_player = Map.get(game_context, :current_player_id)

    # 🎯 优化：智能选择比牌目标
    # 1. 排除自己和已弃牌的玩家
    # 2. 优先选择真实玩家（增加游戏互动）
    # 3. 考虑座位顺序和玩家行为模式

    valid_targets =
      Enum.filter(active_players, fn player ->
        player.numeric_id != current_player and
          not Map.get(player, :is_folded, false)
      end)

    case valid_targets do
      [] ->
        nil

      [single_target] ->
        single_target.numeric_id

      multiple_targets ->
        # 优先选择真实玩家
        real_players = Enum.filter(multiple_targets, fn p -> not Map.get(p, :is_robot, false) end)

        target =
          if length(real_players) > 0 do
            Enum.random(real_players)
          else
            Enum.random(multiple_targets)
          end

        target.numeric_id
    end
  end

  @doc """
  机器人比牌决策 - 合并优化策略
  """
  def should_robot_compete?(robot, cards, game_context) do
    # 🎯 使用合并的比牌策略
    if can_compete?(game_context) do
      should_initiate_competition?(robot, cards, game_context)
    else
      false
    end
  end

  @doc """
  机器人被比牌应对决策 - 合并优化策略
  """
  def should_robot_accept_competition?(robot, cards, game_context, requester_id) do
    # 🎯 使用合并的比牌应对策略
    should_accept_competition?(robot, cards, game_context, requester_id)
  end

  @doc """
  机器人主动比牌决策（合并版）
  """
  def should_initiate_competition?(robot, cards, game_context) do
    robot_type = Map.get(robot, :robot_type, :balanced)
    hand_strength = TeenPattiLogic.calculate_hand_strength(cards)
    hand_analysis = analyze_hand_for_competition(cards, game_context)
    game_analysis = analyze_competition_environment(game_context)

    Logger.info("🤖 [ROBOT_COMPETITION] 机器人 #{robot.numeric_id} 主动比牌决策分析")
    Logger.info("🤖 [ROBOT_COMPETITION] 手牌强度: #{hand_strength}, 类型: #{robot_type}")
    Logger.info("🤖 [ROBOT_COMPETITION] 游戏环境: #{inspect(game_analysis)}")

    decision =
      case robot_type do
        :aggressive -> should_aggressive_compete?(hand_analysis, game_analysis)
        :conservative -> should_conservative_compete?(hand_analysis, game_analysis)
        :tricky -> should_tricky_compete?(hand_analysis, game_analysis)
        :supportive -> should_supportive_compete?(hand_analysis, game_analysis)
        :balanced -> should_balanced_compete?(hand_analysis, game_analysis)
        _ -> should_balanced_compete?(hand_analysis, game_analysis)
      end

    Logger.info("🤖 [ROBOT_COMPETITION] 主动比牌决策: #{decision}")
    decision
  end

  @doc """
  机器人被比牌应对决策（合并版）
  """
  def should_accept_competition?(robot, cards, game_context, requester_id) do
    robot_type = Map.get(robot, :robot_type, :balanced)
    hand_strength = TeenPattiLogic.calculate_hand_strength(cards)
    hand_analysis = analyze_hand_for_competition(cards, game_context)
    game_analysis = analyze_competition_environment(game_context)

    # 分析比牌发起者
    requester_analysis = analyze_requester_behavior(requester_id, game_context)

    Logger.info("🤖 [ROBOT_COMPETITION] 机器人 #{robot.numeric_id} 被比牌应对决策")
    Logger.info("🤖 [ROBOT_COMPETITION] 发起者: #{requester_id}, 分析: #{inspect(requester_analysis)}")
    Logger.info("🤖 [ROBOT_COMPETITION] 手牌强度: #{hand_strength}")

    decision =
      case robot_type do
        :aggressive ->
          should_aggressive_accept?(hand_analysis, game_analysis, requester_analysis)

        :conservative ->
          should_conservative_accept?(hand_analysis, game_analysis, requester_analysis)

        :tricky ->
          should_tricky_accept?(hand_analysis, game_analysis, requester_analysis)

        :supportive ->
          should_supportive_accept?(hand_analysis, game_analysis, requester_analysis)

        :balanced ->
          should_balanced_accept?(hand_analysis, game_analysis, requester_analysis)

        _ ->
          should_balanced_accept?(hand_analysis, game_analysis, requester_analysis)
      end

    Logger.info("🤖 [ROBOT_COMPETITION] 被比牌应对决策: #{decision}")
    decision
  end

  @doc """
  更新机器人状态
  """
  def update_robot_state(robot, game_result, game_context) do
    # 更新统计数据
    total_games = Map.get(robot, :total_games, 0) + 1

    consecutive_wins =
      if game_result == :win do
        Map.get(robot, :consecutive_wins, 0) + 1
      else
        0
      end

    consecutive_losses =
      if game_result == :lose do
        Map.get(robot, :consecutive_losses, 0) + 1
      else
        0
      end

    # 检查是否需要改变状态
    new_robot_state =
      determine_new_robot_state(robot, consecutive_wins, consecutive_losses, game_context)

    updated_robot = %{
      robot
      | total_games: total_games,
        consecutive_wins: consecutive_wins,
        consecutive_losses: consecutive_losses,
        robot_state: new_robot_state,
        last_action_time: DateTime.utc_now()
    }

    Logger.info("🤖 [TEEN_PATTI_ROBOT_STATE] 机器人 #{robot.numeric_id} 状态更新:")
    Logger.info("🤖 [TEEN_PATTI_ROBOT_STATE]   - 性格: #{Map.get(robot, :robot_type, :balanced)}")
    Logger.info("🤖 [TEEN_PATTI_ROBOT_STATE]   - 新状态: #{new_robot_state}")

    Logger.info(
      "🤖 [TEEN_PATTI_ROBOT_STATE]   - 连胜: #{consecutive_wins}, 连败: #{consecutive_losses}"
    )

    updated_robot
  end

  @doc """
  确定新的机器人状态
  """
  defp determine_new_robot_state(robot, consecutive_wins, consecutive_losses, game_context) do
    robot_type = Map.get(robot, :robot_type, :balanced)
    current_time = DateTime.utc_now()
    hour = current_time.hour

    cond do
      # 低峰期（凌晨2-5点）进入保守模式
      hour >= 2 and hour <= 5 ->
        :conservative_mode

      # 连续输局触发送分状态
      consecutive_losses >= 5 and robot_type == :supportive ->
        :send_money

      # 连续赢局触发收分状态
      consecutive_wins >= 3 and robot_type in [:aggressive, :balanced] ->
        :collect_money

      # 检查是否触发连续好牌状态
      should_trigger_consecutive_good?(game_context) ->
        :consecutive_good

      true ->
        :normal
    end
  end

  @doc """
  检查是否应该触发连续好牌
  """
  defp should_trigger_consecutive_good?(game_context) do
    player_money_low = Map.get(game_context, :player_money_low, false)
    TeenPattiConfig.should_trigger_consecutive_good_cards?() and player_money_low
  end

  @doc """
  生成机器人名称
  """
  defp generate_robot_name(robot_type) do
    base_names =
      case robot_type do
        :aggressive -> ["战神", "霸王", "雄鹰", "猛虎", "闪电"]
        :conservative -> ["智者", "稳健", "谨慎", "老手", "沉着"]
        :tricky -> ["狐狸", "魔术师", "变脸", "诡计", "幻影"]
        :supportive -> ["助手", "伙伴", "朋友", "导师", "陪练"]
        :balanced -> ["高手", "玩家", "达人", "专家", "大师"]
      end

    base_name = Enum.random(base_names)
    suffix = :rand.uniform(999)
    "#{base_name}#{suffix}"
  end

  @doc """
  生成初始金币
  """
  defp generate_initial_money(robot_type) do
    # 从 TeenPattiConfig 获取指定类型机器人的金币配置
    money_config = TeenPattiConfig.get_robot_initial_money(robot_type)
    min_money = money_config.min
    max_money = money_config.max
    min_money + :rand.uniform(max_money - min_money)
  end

  @doc """
  选择随机机器人类型
  """
  defp choose_random_robot_type do
    distribution = @type_distribution
    random_value = :rand.uniform()

    Enum.reduce_while(distribution, 0.0, fn {type, probability}, acc ->
      new_acc = acc + probability

      if random_value <= new_acc do
        {:halt, type}
      else
        {:cont, new_acc}
      end
    end)
  end

  @doc """
  生成个性特征
  """
  defp generate_personality_traits(robot_type) do
    case robot_type do
      :aggressive -> [:bold, :impatient, :competitive]
      :conservative -> [:cautious, :patient, :analytical]
      :tricky -> [:unpredictable, :clever, :deceptive]
      :supportive -> [:helpful, :friendly, :encouraging]
      :balanced -> [:adaptable, :observant, :strategic]
    end
  end

  @doc """
  生成激进程度
  """
  defp generate_aggression_level(robot_type) do
    case robot_type do
      # 0.8-1.0
      :aggressive -> 0.8 + :rand.uniform() * 0.2
      # 0.1-0.3
      :conservative -> 0.1 + :rand.uniform() * 0.2
      # 0.3-0.7
      :tricky -> 0.3 + :rand.uniform() * 0.4
      # 0.2-0.5
      :supportive -> 0.2 + :rand.uniform() * 0.3
      # 0.4-0.6
      :balanced -> 0.4 + :rand.uniform() * 0.2
    end
  end

  @doc """
  生成虚张声势倾向
  """
  defp generate_bluff_tendency(robot_type) do
    case robot_type do
      # 0.6-0.9
      :aggressive -> 0.6 + :rand.uniform() * 0.3
      # 0.1-0.3
      :conservative -> 0.1 + :rand.uniform() * 0.2
      # 0.7-1.0
      :tricky -> 0.7 + :rand.uniform() * 0.3
      # 0.2-0.4
      :supportive -> 0.2 + :rand.uniform() * 0.2
      # 0.3-0.6
      :balanced -> 0.3 + :rand.uniform() * 0.3
    end
  end

  @doc """
  生成风险承受能力
  """
  defp generate_risk_tolerance(robot_type) do
    case robot_type do
      # 0.7-1.0
      :aggressive -> 0.7 + :rand.uniform() * 0.3
      # 0.1-0.4
      :conservative -> 0.1 + :rand.uniform() * 0.3
      # 0.5-0.9
      :tricky -> 0.5 + :rand.uniform() * 0.4
      # 0.3-0.6
      :supportive -> 0.3 + :rand.uniform() * 0.3
      # 0.4-0.7
      :balanced -> 0.4 + :rand.uniform() * 0.3
    end
  end

  @doc """
  生成耐心程度
  """
  defp generate_patience_level(robot_type) do
    case robot_type do
      # 0.1-0.4
      :aggressive -> 0.1 + :rand.uniform() * 0.3
      # 0.7-1.0
      :conservative -> 0.7 + :rand.uniform() * 0.3
      # 0.4-0.8
      :tricky -> 0.4 + :rand.uniform() * 0.4
      # 0.6-0.9
      :supportive -> 0.6 + :rand.uniform() * 0.3
      # 0.5-0.8
      :balanced -> 0.5 + :rand.uniform() * 0.3
    end
  end

  # ===== 增强决策系统分析函数 =====

  @doc """
  综合手牌分析
  """
  defp analyze_hand_comprehensive(cards, game_context) do
    hand_strength = TeenPattiLogic.calculate_hand_strength(cards)
    card_type = TeenPattiLogic.calculate_card_type(cards)

    # 计算手牌潜力（考虑可能的改进）
    hand_potential = calculate_hand_potential(cards, card_type)

    # 计算相对强度（与其他可能手牌比较）
    relative_strength = calculate_relative_strength(hand_strength, game_context)

    %{
      strength: hand_strength,
      type: card_type,
      potential: hand_potential,
      relative_strength: relative_strength,
      is_premium: hand_strength >= 500,
      is_strong: hand_strength >= 350,
      is_medium: hand_strength >= 200,
      is_weak: hand_strength < 200
    }
  end

  @doc """
  分析游戏环境
  """
  defp analyze_game_environment(game_context) do
    current_bet = Map.get(game_context, :current_bet, 0)
    pot_total = Map.get(game_context, :pot_total, 0)
    turn_count = Map.get(game_context, :turn_count, 0)
    active_players = Map.get(game_context, :active_player_count, 2)
    stock_status = Map.get(game_context, :stock_status, :normal)

    # 计算底池赔率
    pot_odds = if current_bet > 0, do: pot_total / current_bet, else: 0

    # 判断游戏阶段
    game_stage =
      cond do
        turn_count <= 2 -> :early
        turn_count <= 4 -> :middle
        true -> :late
      end

    # 判断压力等级
    pressure_level =
      cond do
        current_bet >= pot_total * 0.5 -> :high
        current_bet >= pot_total * 0.2 -> :medium
        true -> :low
      end

    %{
      current_bet: current_bet,
      pot_total: pot_total,
      pot_odds: pot_odds,
      turn_count: turn_count,
      active_players: active_players,
      game_stage: game_stage,
      pressure_level: pressure_level,
      stock_status: stock_status,
      is_heads_up: active_players == 2,
      is_multi_way: active_players > 2
    }
  end

  @doc """
  计算手牌潜力
  """
  defp calculate_hand_potential(cards, card_type) do
    case card_type do
      # 三条无改进空间
      :trail -> 1.0
      # 同花顺几乎完美
      :pure_sequence -> 0.9
      # 顺子很强
      :sequence -> 0.8
      # 同花不错
      :color -> 0.7
      # 对子中等
      :pair -> 0.5
      # 高牌较弱
      :high_card -> 0.2
      _ -> 0.3
    end
  end

  @doc """
  计算相对强度
  """
  defp calculate_relative_strength(hand_strength, game_context) do
    active_players = Map.get(game_context, :active_player_count, 2)

    # 根据玩家数量调整相对强度
    adjustment =
      case active_players do
        # 单挑时手牌价值提升
        2 -> 1.2
        # 三人时正常
        3 -> 1.0
        # 四人时略降
        4 -> 0.9
        # 更多人时显著下降
        _ -> 0.8
      end

    hand_strength * adjustment
  end

  # 临时辅助函数（后续需要从游戏上下文获取真实数据）
  defp get_player_luck_from_context(_game_context), do: 500
  defp get_player_consecutive_losses(_game_context), do: 0

  @doc """
  获取机器人类型定义
  """
  def get_robot_types, do: @robot_types

  @doc """
  获取机器人状态定义
  """
  def get_robot_states, do: @robot_states

  @doc """
  获取机器人类型分布
  """
  def get_type_distribution, do: @type_distribution

  # ===== 增强决策系统各类型机器人决策 =====

  @doc """
  增强激进型决策
  """
  defp make_enhanced_aggressive_decision(robot, hand_analysis, game_analysis) do
    cond do
      # 超强牌时必定大幅加注或比牌
      hand_analysis.is_premium ->
        if game_analysis.game_stage == :late and game_analysis.is_heads_up do
          Logger.info("🔥 [ENHANCED_AGGRESSIVE] 超强牌单挑比牌")
          {:competition, :random}
        else
          fill = if game_analysis.pressure_level == :low, do: 3, else: 2
          Logger.info("🔥 [ENHANCED_AGGRESSIVE] 超强牌大幅加注 #{fill}倍")
          {:bet, fill}
        end

      # 强牌时根据环境决策
      hand_analysis.is_strong ->
        cond do
          game_analysis.pot_odds > 3 and game_analysis.game_stage != :early ->
            Logger.info("🔥 [ENHANCED_AGGRESSIVE] 强牌高赔率比牌")
            {:competition, :random}

          game_analysis.pressure_level == :low ->
            Logger.info("🔥 [ENHANCED_AGGRESSIVE] 强牌低压力加注")
            {:bet, 2}

          true ->
            Logger.info("🔥 [ENHANCED_AGGRESSIVE] 强牌跟注")
            {:bet, 1}
        end

      # 中等牌时适度激进
      hand_analysis.is_medium ->
        if game_analysis.is_heads_up and :rand.uniform() < 0.4 do
          Logger.info("🔥 [ENHANCED_AGGRESSIVE] 中等牌单挑加注")
          {:bet, 2}
        else
          Logger.info("🔥 [ENHANCED_AGGRESSIVE] 中等牌跟注")
          {:bet, 1}
        end

      # 弱牌时偶尔诈唬
      true ->
        if game_analysis.is_heads_up and game_analysis.pressure_level == :low and
             :rand.uniform() < 0.2 do
          Logger.info("🔥 [ENHANCED_AGGRESSIVE] 弱牌单挑诈唬")
          {:bet, 2}
        else
          Logger.info("🔥 [ENHANCED_AGGRESSIVE] 弱牌弃牌")
          {:fold, nil}
        end
    end
  end

  @doc """
  增强保守型决策
  """
  defp make_enhanced_conservative_decision(robot, hand_analysis, game_analysis) do
    cond do
      # 超强牌时谨慎加注
      hand_analysis.is_premium ->
        if game_analysis.pot_odds > 4 do
          Logger.info("🛡️ [ENHANCED_CONSERVATIVE] 超强牌高赔率加注")
          {:bet, 2}
        else
          Logger.info("🛡️ [ENHANCED_CONSERVATIVE] 超强牌跟注")
          {:bet, 1}
        end

      # 强牌时根据压力决策
      hand_analysis.is_strong ->
        if game_analysis.pressure_level == :high do
          Logger.info("🛡️ [ENHANCED_CONSERVATIVE] 强牌高压力看牌")
          {:look, nil}
        else
          Logger.info("🛡️ [ENHANCED_CONSERVATIVE] 强牌跟注")
          {:bet, 1}
        end

      # 中等牌时非常谨慎
      hand_analysis.is_medium ->
        if game_analysis.pressure_level == :low and game_analysis.pot_odds > 2 do
          Logger.info("🛡️ [ENHANCED_CONSERVATIVE] 中等牌低压力跟注")
          {:bet, 1}
        else
          Logger.info("🛡️ [ENHANCED_CONSERVATIVE] 中等牌弃牌")
          {:fold, nil}
        end

      # 弱牌直接弃牌
      true ->
        Logger.info("🛡️ [ENHANCED_CONSERVATIVE] 弱牌直接弃牌")
        {:fold, nil}
    end
  end

  @doc """
  增强迷惑型决策
  """
  defp make_enhanced_tricky_decision(robot, hand_analysis, game_analysis) do
    # 迷惑型的反逻辑操作
    random_factor = :rand.uniform()

    cond do
      # 强牌时50%概率反逻辑
      hand_analysis.is_premium or hand_analysis.is_strong ->
        if random_factor < 0.3 do
          Logger.info("🎭 [ENHANCED_TRICKY] 强牌反逻辑弃牌")
          {:fold, nil}
        else
          Logger.info("🎭 [ENHANCED_TRICKY] 强牌正常加注")
          {:bet, 2}
        end

      # 中等牌时随机决策
      hand_analysis.is_medium ->
        if random_factor < 0.4 do
          Logger.info("🎭 [ENHANCED_TRICKY] 中等牌诈唬加注")
          {:bet, 2}
        else
          Logger.info("🎭 [ENHANCED_TRICKY] 中等牌跟注")
          {:bet, 1}
        end

      # 弱牌时偶尔大胆诈唬
      true ->
        if random_factor < 0.25 and game_analysis.is_heads_up do
          Logger.info("🎭 [ENHANCED_TRICKY] 弱牌大胆诈唬")
          {:bet, 3}
        else
          Logger.info("🎭 [ENHANCED_TRICKY] 弱牌弃牌")
          {:fold, nil}
        end
    end
  end

  @doc """
  增强陪玩型决策
  """
  defp make_enhanced_supportive_decision(robot, hand_analysis, game_analysis) do
    # 检查是否需要送分
    should_send_money = should_supportive_send_money?(game_analysis)

    if should_send_money do
      Logger.info("🤝 [ENHANCED_SUPPORTIVE] 陪玩型送分模式")
      # 送分模式：即使好牌也保守
      if hand_analysis.is_premium do
        Logger.info("🤝 [ENHANCED_SUPPORTIVE] 好牌送分跟注")
        {:bet, 1}
      else
        Logger.info("🤝 [ENHANCED_SUPPORTIVE] 一般牌送分弃牌")
        {:fold, nil}
      end
    else
      # 正常陪玩模式
      cond do
        hand_analysis.is_strong ->
          Logger.info("🤝 [ENHANCED_SUPPORTIVE] 强牌陪玩跟注")
          {:bet, 1}

        hand_analysis.is_medium ->
          Logger.info("🤝 [ENHANCED_SUPPORTIVE] 中等牌陪玩跟注")
          {:bet, 1}

        true ->
          Logger.info("🤝 [ENHANCED_SUPPORTIVE] 弱牌陪玩弃牌")
          {:fold, nil}
      end
    end
  end

  @doc """
  增强平衡型决策
  """
  defp make_enhanced_balanced_decision(robot, hand_analysis, game_analysis) do
    cond do
      # 超强牌时积极行动
      hand_analysis.is_premium ->
        if game_analysis.pot_odds > 3 and game_analysis.game_stage == :late do
          Logger.info("⚖️ [ENHANCED_BALANCED] 超强牌比牌")
          {:competition, :random}
        else
          Logger.info("⚖️ [ENHANCED_BALANCED] 超强牌加注")
          {:bet, 2}
        end

      # 强牌时根据情况决策
      hand_analysis.is_strong ->
        if game_analysis.pressure_level == :high do
          Logger.info("⚖️ [ENHANCED_BALANCED] 强牌高压力跟注")
          {:bet, 1}
        else
          Logger.info("⚖️ [ENHANCED_BALANCED] 强牌加注")
          {:bet, 2}
        end

      # 中等牌时谨慎处理
      hand_analysis.is_medium ->
        if game_analysis.pot_odds > 2 do
          Logger.info("⚖️ [ENHANCED_BALANCED] 中等牌好赔率跟注")
          {:bet, 1}
        else
          Logger.info("⚖️ [ENHANCED_BALANCED] 中等牌弃牌")
          {:fold, nil}
        end

      # 弱牌时偶尔跟注
      true ->
        if game_analysis.pot_odds > 4 and :rand.uniform() < 0.2 do
          Logger.info("⚖️ [ENHANCED_BALANCED] 弱牌高赔率跟注")
          {:bet, 1}
        else
          Logger.info("⚖️ [ENHANCED_BALANCED] 弱牌弃牌")
          {:fold, nil}
        end
    end
  end

  @doc """
  判断陪玩型是否应该送分
  """
  defp should_supportive_send_money?(game_analysis) do
    # 根据库存状态和游戏环境判断
    case game_analysis.stock_status do
      # 库存高时送分
      :high -> true
      # 库存低时不送分
      :low -> false
      # 正常时30%概率送分
      _ -> :rand.uniform() < 0.3
    end
  end

  # ===== 比牌策略分析函数（合并版） =====

  defp analyze_hand_for_competition(cards, game_context) do
    hand_strength = TeenPattiLogic.calculate_hand_strength(cards)
    card_type = TeenPattiLogic.calculate_card_type(cards)

    # 计算胜率估算
    win_probability = estimate_win_probability(hand_strength, game_context)

    # 判断手牌等级
    hand_level =
      cond do
        # 超强牌
        hand_strength >= 700 -> :premium
        # 强牌
        hand_strength >= 500 -> :strong
        # 中等牌
        hand_strength >= 350 -> :medium
        # 弱牌
        hand_strength >= 200 -> :weak
        # 极弱牌
        true -> :very_weak
      end

    %{
      strength: hand_strength,
      type: card_type,
      level: hand_level,
      win_probability: win_probability
    }
  end

  defp analyze_competition_environment(game_context) do
    turn_count = Map.get(game_context, :turn_count, 0)
    active_players = Map.get(game_context, :active_player_count, 2)
    pot_total = Map.get(game_context, :pot_total, 0)
    current_bet = Map.get(game_context, :current_bet, 0)

    # 计算比牌成本效益
    competition_cost = calculate_competition_cost(game_context)
    pot_odds = if competition_cost > 0, do: pot_total / competition_cost, else: 0

    # 判断游戏阶段
    game_stage =
      cond do
        turn_count <= 3 -> :early
        turn_count <= 6 -> :middle
        true -> :late
      end

    # 判断竞争激烈程度
    competition_intensity =
      cond do
        current_bet >= pot_total * 0.3 -> :high
        current_bet >= pot_total * 0.1 -> :medium
        true -> :low
      end

    %{
      turn_count: turn_count,
      active_players: active_players,
      pot_total: pot_total,
      pot_odds: pot_odds,
      game_stage: game_stage,
      competition_intensity: competition_intensity,
      competition_cost: competition_cost
    }
  end

  defp analyze_requester_behavior(requester_id, game_context) do
    # 简化的发起者行为分析
    # 在实际实现中，可以基于历史数据分析玩家行为模式
    %{
      player_id: requester_id,
      is_robot: is_robot_player?(requester_id, game_context),
      # 可以基于历史数据计算
      aggression_level: :medium,
      # 可以基于历史数据计算
      bluff_tendency: :medium
    }
  end

  defp estimate_win_probability(hand_strength, game_context) do
    active_players = Map.get(game_context, :active_player_count, 2)

    # 简化的胜率估算
    base_probability =
      cond do
        hand_strength >= 700 -> 0.85
        hand_strength >= 500 -> 0.65
        hand_strength >= 350 -> 0.45
        hand_strength >= 200 -> 0.25
        true -> 0.1
      end

    # 根据玩家数量调整
    adjustment =
      case active_players do
        2 -> 1.1
        3 -> 1.0
        4 -> 0.9
        _ -> 0.8
      end

    min(base_probability * adjustment, 0.95)
  end

  defp calculate_competition_cost(game_context) do
    # 简化的比牌成本计算
    current_times = Map.get(game_context, :current_times, 1)
    base_bet = Map.get(game_context, :base_bet, 10)
    current_times * base_bet
  end

  defp is_robot_player?(player_id, game_context) do
    # 🎯 修复：使用is_robot字段判断机器人，而不是负数ID
    active_players = Map.get(game_context, :active_players, [])

    case Enum.find(active_players, fn p -> p.numeric_id == player_id end) do
      nil -> false
      player -> Map.get(player, :is_robot, false)
    end
  end

  # ===== 各类型机器人比牌策略（合并版） =====

  # 激进型机器人比牌策略
  defp should_aggressive_compete?(hand_analysis, game_analysis) do
    cond do
      # 超强牌时积极比牌
      hand_analysis.level == :premium ->
        if game_analysis.game_stage != :early do
          Logger.info("🔥 [AGGRESSIVE_COMPETE] 超强牌中后期积极比牌")
          true
        else
          # 早期也有50%概率比牌
          :rand.uniform() < 0.5
        end

      # 强牌时根据环境决策
      hand_analysis.level == :strong ->
        cond do
          game_analysis.pot_odds > 3 -> true
          game_analysis.active_players == 2 -> :rand.uniform() < 0.6
          game_analysis.game_stage == :late -> :rand.uniform() < 0.4
          true -> :rand.uniform() < 0.2
        end

      # 中等牌时偶尔诈唬比牌
      hand_analysis.level == :medium ->
        if game_analysis.active_players == 2 and :rand.uniform() < 0.3 do
          Logger.info("🔥 [AGGRESSIVE_COMPETE] 中等牌单挑诈唬比牌")
          true
        else
          false
        end

      true ->
        false
    end
  end

  defp should_aggressive_accept?(hand_analysis, game_analysis, requester_analysis) do
    cond do
      # 超强牌必接
      hand_analysis.level == :premium ->
        Logger.info("🔥 [AGGRESSIVE_ACCEPT] 超强牌必接比牌")
        true

      # 强牌时大概率接受
      hand_analysis.level == :strong ->
        accept_rate = if requester_analysis.is_robot, do: 0.8, else: 0.9
        :rand.uniform() < accept_rate

      # 中等牌时根据环境决策
      hand_analysis.level == :medium ->
        cond do
          game_analysis.pot_odds > 4 -> true
          game_analysis.active_players == 2 -> :rand.uniform() < 0.6
          true -> :rand.uniform() < 0.3
        end

      # 弱牌时偶尔接受（诈唬）
      hand_analysis.level == :weak ->
        if game_analysis.active_players == 2 and :rand.uniform() < 0.2 do
          Logger.info("🔥 [AGGRESSIVE_ACCEPT] 弱牌诈唬接受比牌")
          true
        else
          false
        end

      true ->
        false
    end
  end

  # 保守型机器人比牌策略
  defp should_conservative_compete?(hand_analysis, game_analysis) do
    cond do
      # 只有超强牌才主动比牌
      hand_analysis.level == :premium and game_analysis.pot_odds > 2 ->
        Logger.info("🛡️ [CONSERVATIVE_COMPETE] 超强牌高赔率比牌")
        true

      # 强牌时非常谨慎
      hand_analysis.level == :strong ->
        if game_analysis.active_players == 2 and game_analysis.pot_odds > 4 do
          :rand.uniform() < 0.3
        else
          false
        end

      true ->
        false
    end
  end

  defp should_conservative_accept?(hand_analysis, game_analysis, _requester_analysis) do
    cond do
      # 超强牌才接受
      hand_analysis.level == :premium ->
        true

      # 强牌时非常谨慎
      hand_analysis.level == :strong ->
        game_analysis.pot_odds > 3 and :rand.uniform() < 0.5

      true ->
        false
    end
  end

  # 迷惑型机器人比牌策略
  defp should_tricky_compete?(hand_analysis, game_analysis) do
    # 迷惑型的反逻辑操作
    random_factor = :rand.uniform()

    cond do
      # 有时候强牌不比牌
      hand_analysis.level in [:premium, :strong] ->
        if random_factor < 0.3 do
          Logger.info("🎭 [TRICKY_COMPETE] 强牌反逻辑不比牌")
          false
        else
          random_factor < 0.6
        end

      # 有时候弱牌反而比牌
      hand_analysis.level in [:weak, :very_weak] ->
        if game_analysis.active_players == 2 and random_factor < 0.25 do
          Logger.info("🎭 [TRICKY_COMPETE] 弱牌反逻辑比牌")
          true
        else
          false
        end

      # 中等牌随机决策
      true ->
        random_factor < 0.4
    end
  end

  defp should_tricky_accept?(hand_analysis, _game_analysis, _requester_analysis) do
    random_factor = :rand.uniform()

    # 迷惑型的随机接受策略
    case hand_analysis.level do
      # 80%接受
      :premium -> random_factor < 0.8
      # 60%接受
      :strong -> random_factor < 0.6
      # 40%接受
      :medium -> random_factor < 0.4
      # 30%接受
      :weak -> random_factor < 0.3
      # 20%接受
      :very_weak -> random_factor < 0.2
    end
  end

  # 陪玩型机器人比牌策略
  defp should_supportive_compete?(hand_analysis, game_analysis) do
    # 陪玩型很少主动比牌，除非超强牌
    if hand_analysis.level == :premium and game_analysis.game_stage == :late do
      Logger.info("🤝 [SUPPORTIVE_COMPETE] 超强牌后期比牌")
      :rand.uniform() < 0.4
    else
      false
    end
  end

  defp should_supportive_accept?(hand_analysis, _game_analysis, requester_analysis) do
    # 陪玩型倾向于配合玩家
    if requester_analysis.is_robot do
      # 对机器人比较谨慎
      hand_analysis.level in [:premium, :strong]
    else
      # 对真实玩家更容易接受，增加游戏乐趣
      case hand_analysis.level do
        :premium -> true
        :strong -> :rand.uniform() < 0.8
        :medium -> :rand.uniform() < 0.6
        :weak -> :rand.uniform() < 0.4
        :very_weak -> :rand.uniform() < 0.2
      end
    end
  end

  # 平衡型机器人比牌策略
  defp should_balanced_compete?(hand_analysis, game_analysis) do
    cond do
      # 超强牌时积极比牌
      hand_analysis.level == :premium ->
        if game_analysis.game_stage == :late or game_analysis.pot_odds > 2 do
          :rand.uniform() < 0.7
        else
          :rand.uniform() < 0.4
        end

      # 强牌时根据环境
      hand_analysis.level == :strong ->
        cond do
          game_analysis.pot_odds > 3 -> :rand.uniform() < 0.5
          game_analysis.active_players == 2 -> :rand.uniform() < 0.3
          true -> :rand.uniform() < 0.1
        end

      true ->
        false
    end
  end

  defp should_balanced_accept?(hand_analysis, game_analysis, _requester_analysis) do
    cond do
      # 超强牌必接
      hand_analysis.level == :premium ->
        true

      # 强牌时根据赔率
      hand_analysis.level == :strong ->
        if game_analysis.pot_odds > 2 do
          :rand.uniform() < 0.8
        else
          :rand.uniform() < 0.5
        end

      # 中等牌时谨慎
      hand_analysis.level == :medium ->
        if game_analysis.pot_odds > 4 do
          :rand.uniform() < 0.4
        else
          :rand.uniform() < 0.2
        end

      true ->
        false
    end
  end
end
