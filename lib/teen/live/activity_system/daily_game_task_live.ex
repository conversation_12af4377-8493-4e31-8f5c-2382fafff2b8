defmodule Teen.Live.ActivitySystem.DailyGameTaskLive do
  @moduledoc """
  每日游戏任务管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.GameTask
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "每日游戏任务"

  @impl Backpex.LiveResource
  def plural_name, do: "每日游戏任务"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      task_name: %{
        module: Backpex.Fields.Text,
        label: "任务名称"
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "任务类型",
        options: [
          {"游戏局数", "game_rounds"},
          {"充值金额", "recharge_amount"},
          {"游戏胜利", "game_wins"},
          {"转盘次数", "wheel_spins"},
          {"完成任务", "task_completion"}
        ]
      },
      target_value: %{
        module: Backpex.Fields.Number,
        label: "目标值"
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"道具", "items"}
        ]
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      start_date: %{
        module: Backpex.Fields.Date,
        label: "开始日期"
      },
      end_date: %{
        module: Backpex.Fields.Date,
        label: "结束日期"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
