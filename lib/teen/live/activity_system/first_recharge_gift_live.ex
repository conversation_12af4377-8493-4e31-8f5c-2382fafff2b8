defmodule Teen.Live.ActivitySystem.FirstRechargeGiftLive do
  @moduledoc """
  首充礼包管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.FirstRechargeGift
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "首充礼包"

  @impl Backpex.LiveResource
  def plural_name, do: "首充礼包"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      gift_name: %{
        module: Backpex.Fields.Text,
        label: "礼包名称"
      },
      min_recharge_amount: %{
        module: Backpex.Fields.Number,
        label: "最小充值金额"
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"道具", "items"}
        ]
      },
      bonus_multiplier: %{
        module: Backpex.Fields.Number,
        label: "奖励倍数"
      },
      time_limit_hours: %{
        module: Backpex.Fields.Number,
        label: "时间限制(小时)"
      },
      description: %{
        module: Backpex.Fields.Textarea,
        label: "描述"
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
