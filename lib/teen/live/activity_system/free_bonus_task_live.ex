defmodule Teen.Live.ActivitySystem.FreeBonusTaskLive do
  @moduledoc """
  免费任务管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.FreeBonusTask
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "免费任务"

  @impl Backpex.LiveResource
  def plural_name, do: "免费任务"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      task_name: %{
        module: Backpex.Fields.Text,
        label: "任务名称"
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "任务类型",
        options: [
          {"观看广告", "watch_ad"},
          {"分享游戏", "share_game"},
          {"评价应用", "rate_app"},
          {"关注社交", "follow_social"},
          {"完成调查", "complete_survey"}
        ]
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"道具", "items"}
        ]
      },
      daily_limit: %{
        module: Backpex.Fields.Number,
        label: "每日限制次数"
      },
      completion_time_seconds: %{
        module: Backpex.Fields.Number,
        label: "完成时间(秒)"
      },
      difficulty_level: %{
        module: Backpex.Fields.Select,
        label: "难度等级",
        options: [
          {"简单", "easy"},
          {"中等", "medium"},
          {"困难", "hard"}
        ]
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
