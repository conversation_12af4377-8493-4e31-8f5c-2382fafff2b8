defmodule Teen.Live.ActivitySystem.RechargeTaskLive do
  @moduledoc """
  充值任务管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.RechargeTask
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "充值任务"

  @impl Backpex.LiveResource
  def plural_name, do: "充值任务"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      task_name: %{
        module: Backpex.Fields.Text,
        label: "任务名称"
      },
      required_amount: %{
        module: Backpex.Fields.Number,
        label: "所需充值金额"
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励金额"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"道具", "items"}
        ]
      },
      task_type: %{
        module: Backpex.Fields.Select,
        label: "任务类型",
        options: [
          {"单次充值", "single"},
          {"累计充值", "cumulative"},
          {"连续充值", "consecutive"}
        ]
      },
      time_limit_hours: %{
        module: Backpex.Fields.Number,
        label: "时间限制(小时)"
      },
      is_active: %{
        module: Backpex.Fields.Boolean,
        label: "是否激活"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
