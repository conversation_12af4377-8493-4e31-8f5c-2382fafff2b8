defmodule Teen.Live.ActivitySystem.RewardClaimRecordLive do
  @moduledoc """
  奖励领取记录管理页面
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ActivitySystem.RewardClaimRecord
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "奖励领取记录"

  @impl Backpex.LiveResource
  def plural_name, do: "奖励领取记录"

  @impl Backpex.LiveResource
  def fields do
    %{
      id: %{
        module: Backpex.Fields.Text,
        label: "ID",
        readonly: true,
        only: [:show]
      },
      user_id: %{
        module: Backpex.Fields.Text,
        label: "用户ID"
      },
      activity_type: %{
        module: Backpex.Fields.Select,
        label: "活动类型",
        options: [
          {"每日游戏任务", "daily_game_task"},
          {"周卡活动", "weekly_card"},
          {"七日登录", "seven_day_login"},
          {"VIP礼包", "vip_gift"},
          {"充值任务", "recharge_task"},
          {"转盘抽奖", "spin_wheel"},
          {"刮刮卡", "scratch_card"},
          {"首充礼包", "first_recharge"},
          {"亏损返利", "loss_rebate"},
          {"邀请奖励", "invite_reward"},
          {"绑定奖励", "binding_reward"},
          {"免费任务", "free_task"},
          {"CDKey", "cdkey"}
        ]
      },
      activity_id: %{
        module: Backpex.Fields.Text,
        label: "活动ID"
      },
      reward_type: %{
        module: Backpex.Fields.Select,
        label: "奖励类型",
        options: [
          {"金币", "coins"},
          {"积分", "points"},
          {"道具", "items"},
          {"现金", "cash"}
        ]
      },
      reward_amount: %{
        module: Backpex.Fields.Number,
        label: "奖励数量"
      },
      claim_status: %{
        module: Backpex.Fields.Select,
        label: "领取状态",
        options: [
          {"待领取", "pending"},
          {"已领取", "claimed"},
          {"已过期", "expired"},
          {"已取消", "cancelled"}
        ]
      },
      claimed_at: %{
        module: Backpex.Fields.DateTime,
        label: "领取时间"
      },
      ip_address: %{
        module: Backpex.Fields.Text,
        label: "IP地址"
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true
      }
    }
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    true
  end
end
