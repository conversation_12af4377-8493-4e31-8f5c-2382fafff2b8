defmodule Teen.Live.Admin.IconConflictCheckerLive do
  @moduledoc """
  图标冲突检测页面 - 检查系统中图标使用的冲突和重复
  """
  use CypridinaWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(page_title: "图标冲突检测")
      |> assign(conflict_results: run_conflict_check())
      |> assign(:fluid?, true)
      |> assign(:current_url, "/admin/icon-conflict-checker")

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_event("refresh_check", _params, socket) do
    socket = assign(socket, conflict_results: run_conflict_check())
    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <div class="flex justify-between items-center">
            <div>
              <h1 class="text-2xl font-bold flex items-center gap-2">
                <.icon name="hero-shield-exclamation" class="size-6 text-warning" />
                图标冲突检测
              </h1>
              <p class="text-base-content/70 mt-1">
                检查系统中图标使用的冲突、重复和不一致问题
              </p>
            </div>
            <button
              phx-click="refresh_check"
              class="btn btn-primary"
            >
              <.icon name="hero-arrow-path" class="size-4" />
              重新检测
            </button>
          </div>
        </div>
      </div>

      <!-- 冲突检测结果 -->
      <%= for {category, results} <- @conflict_results do %>
        <div class="card bg-base-100 shadow-xl">
          <div class="card-body">
            <h2 class="card-title text-lg">
              <%= case category do %>
                <% :duplicates -> %>
                  <.icon name="hero-document-duplicate" class="size-5 text-error" />
                  重复使用检测
                <% :inconsistencies -> %>
                  <.icon name="hero-exclamation-triangle" class="size-5 text-warning" />
                  不一致使用
                <% :missing -> %>
                  <.icon name="hero-question-mark-circle" class="size-5 text-info" />
                  缺失图标
                <% :recommendations -> %>
                  <.icon name="hero-light-bulb" class="size-5 text-success" />
                  优化建议
              <% end %>
              <%= String.capitalize(to_string(category)) %>
            </h2>

            <div class="space-y-3">
              <%= if Enum.empty?(results) do %>
                <div class="alert alert-success">
                  <.icon name="hero-check-circle" class="size-5" />
                  <div>
                    <h3 class="font-bold">无问题发现</h3>
                    <div class="text-sm">此类别下没有发现任何问题</div>
                  </div>
                </div>
              <% else %>
                <%= for result <- results do %>
                  <div class={[
                    "alert",
                    case result.severity do
                      :high -> "alert-error"
                      :medium -> "alert-warning"
                      :low -> "alert-info"
                    end
                  ]}>
                    <.icon name={
                      case result.severity do
                        :high -> "hero-x-circle"
                        :medium -> "hero-exclamation-triangle"
                        :low -> "hero-information-circle"
                      end
                    } class="size-5" />
                    <div>
                      <h3 class="font-bold">{result.title}</h3>
                      <div class="text-sm">{result.message}</div>
                      <%= if result[:details] do %>
                        <details class="mt-2">
                          <summary class="cursor-pointer text-xs opacity-70">详细信息</summary>
                          <div class="text-xs mt-1 bg-base-200 p-2 rounded">
                            <%= if is_list(result.details) do %>
                              <ul class="list-disc list-inside space-y-1">
                                <%= for detail <- result.details do %>
                                  <li>{detail}</li>
                                <% end %>
                              </ul>
                            <% else %>
                              <pre class="overflow-auto">{result.details}</pre>
                            <% end %>
                          </div>
                        </details>
                      <% end %>
                      <%= if result[:suggestion] do %>
                        <div class="mt-2 p-2 bg-success/10 rounded text-xs">
                          <strong>建议:</strong> {result.suggestion}
                        </div>
                      <% end %>
                    </div>
                  </div>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

      <!-- 图标使用统计 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">
            <.icon name="hero-chart-bar" class="size-5 text-accent" />
            图标使用统计
          </h2>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <%= for {stat_name, stat_value} <- get_icon_statistics() do %>
              <div class="stat bg-base-200/50 rounded-lg">
                <div class="stat-title">{stat_name}</div>
                <div class="stat-value text-primary">{stat_value}</div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # 运行冲突检测
  defp run_conflict_check do
    [
      duplicates: check_duplicate_usage(),
      inconsistencies: check_inconsistent_usage(),
      missing: check_missing_icons(),
      recommendations: generate_recommendations()
    ]
  end

  # 检查重复使用
  defp check_duplicate_usage do
    sidebar_icons = extract_sidebar_icons()

    # 查找重复使用的图标
    duplicates =
      sidebar_icons
      |> Enum.frequencies()
      |> Enum.filter(fn {_icon, count} -> count > 1 end)
      |> Enum.map(fn {icon, count} ->
        %{
          severity: :medium,
          title: "图标重复使用: #{icon}",
          message: "此图标在侧边栏中被使用了 #{count} 次",
          details: find_icon_usage_locations(icon),
          suggestion: "考虑为不同功能使用不同的图标以提高可识别性"
        }
      end)

    duplicates
  end

  # 检查不一致使用
  defp check_inconsistent_usage do
    inconsistencies = []

    # 检查相似功能是否使用了不同图标
    similar_functions = [
      {"用户管理", ["hero-user", "hero-users", "hero-user-group"]},
      {"系统设置", ["hero-cog-6-tooth", "hero-adjustments-horizontal"]},
      {"文档相关", ["hero-document-text", "hero-clipboard-document", "hero-newspaper"]}
    ]

    Enum.flat_map(similar_functions, fn {category, icons} ->
      used_icons = Enum.filter(icons, &icon_used_in_sidebar?/1)

      if length(used_icons) > 1 do
        [%{
          severity: :low,
          title: "#{category}功能图标不一致",
          message: "相似功能使用了多个不同的图标",
          details: used_icons,
          suggestion: "考虑统一相似功能的图标风格"
        }]
      else
        []
      end
    end) ++ inconsistencies
  end

  # 检查缺失图标
  defp check_missing_icons do
    sidebar_icons = extract_sidebar_icons()
    available_icons = get_available_icons()

    missing_icons =
      sidebar_icons
      |> Enum.reject(&(&1 in available_icons))

    if Enum.empty?(missing_icons) do
      []
    else
      [%{
        severity: :high,
        title: "缺失的图标",
        message: "以下图标在系统中被引用但不存在",
        details: missing_icons,
        suggestion: "检查图标名称拼写或确保 Heroicons 依赖正确安装"
      }]
    end
  end

  # 生成优化建议
  defp generate_recommendations do
    recommendations = []

    # 检查是否有未使用的图标类别
    unused_categories = check_unused_icon_categories()

    category_recommendations =
      if Enum.empty?(unused_categories) do
        []
      else
        [%{
          severity: :low,
          title: "未充分利用的图标类别",
          message: "以下图标类别可能可以更好地利用",
          details: unused_categories,
          suggestion: "考虑在相关功能中使用这些类别的图标"
        }]
      end

    # 检查图标语义化程度
    semantic_recommendations = [
      %{
        severity: :low,
        title: "图标语义化建议",
        message: "建议使用更具语义化的图标",
        details: [
          "用户管理: 使用 hero-users 而不是 hero-user",
          "设置功能: 使用 hero-cog-6-tooth 而不是 hero-adjustments-horizontal",
          "搜索功能: 统一使用 hero-magnifying-glass"
        ],
        suggestion: "选择与功能最匹配的图标名称"
      }
    ]

    recommendations ++ category_recommendations ++ semantic_recommendations
  end

  # 提取侧边栏中使用的图标
  defp extract_sidebar_icons do
    # 通过扫描侧边栏模板文件来提取实际使用的图标
    case File.read("lib/teen/components/layouts/admin.html.heex") do
      {:ok, content} ->
        # 使用正则表达式提取所有 hero- 图标
        Regex.scan(~r/icon="(hero-[^"]+)"/, content)
        |> Enum.map(fn [_, icon] -> icon end)
        |> Enum.uniq()
        |> Enum.sort()

      {:error, _} ->
        # 如果无法读取文件，返回手动维护的列表
        get_fallback_sidebar_icons()
    end
  end

  # 备用的侧边栏图标列表
  defp get_fallback_sidebar_icons do
    [
      "hero-chart-bar", "hero-users", "hero-user", "hero-user-group", "hero-no-symbol",
      "hero-device-phone-mobile", "hero-gift", "hero-check-circle", "hero-calendar-days",
      "hero-ticket", "hero-currency-dollar", "hero-gift-top", "hero-star", "hero-arrow-path",
      "hero-squares-plus", "hero-arrow-uturn-left", "hero-megaphone", "hero-link",
      "hero-hand-raised", "hero-key", "hero-chart-bar-square", "hero-squares-2x2",
      "hero-cpu-chip", "hero-sparkles", "hero-trophy", "hero-chart-pie", "hero-cube",
      "hero-document-duplicate", "hero-receipt-percent", "hero-credit-card", "hero-server",
      "hero-building-library", "hero-cog-6-tooth", "hero-clipboard-document-list",
      "hero-shopping-cart", "hero-adjustments-horizontal", "hero-globe-alt", "hero-document-text",
      "hero-swatch", "hero-wrench-screwdriver", "hero-shield-exclamation"
    ]
  end

  # 检查图标是否在侧边栏中使用
  defp icon_used_in_sidebar?(icon) do
    icon in extract_sidebar_icons()
  end

  # 查找图标使用位置
  defp find_icon_usage_locations(icon) do
    locations = []

    # 搜索侧边栏文件
    sidebar_locations = find_in_sidebar(icon)

    # 搜索其他模板文件
    template_locations = find_in_templates(icon)

    (locations ++ sidebar_locations ++ template_locations)
    |> Enum.uniq()
  end

  # 在侧边栏中查找图标使用
  defp find_in_sidebar(icon) do
    case File.read("lib/teen/components/layouts/admin.html.heex") do
      {:ok, content} ->
        lines = String.split(content, "\n")

        lines
        |> Enum.with_index(1)
        |> Enum.filter(fn {line, _} -> String.contains?(line, icon) end)
        |> Enum.map(fn {line, line_num} ->
          # 尝试从上下文推断功能名称
          context = extract_context_from_line(line)
          "侧边栏 (第#{line_num}行): #{context}"
        end)

      {:error, _} ->
        ["侧边栏文件 (无法读取)"]
    end
  end

  # 在模板文件中查找图标使用
  defp find_in_templates(icon) do
    template_files = [
      "lib/teen/live/admin/icon_test_live.ex",
      "lib/teen/live/admin/icon_diagnostic_live.ex",
      "lib/teen/live/admin/all_icons_live.ex"
    ]

    Enum.flat_map(template_files, fn file ->
      case File.read(file) do
        {:ok, content} ->
          if String.contains?(content, icon) do
            filename = Path.basename(file, ".ex")
            ["模板文件: #{filename}"]
          else
            []
          end

        {:error, _} ->
          []
      end
    end)
  end

  # 从代码行中提取上下文信息
  defp extract_context_from_line(line) do
    cond do
      String.contains?(line, "text=") ->
        case Regex.run(~r/text="([^"]+)"/, line) do
          [_, text] -> text
          _ -> "未知功能"
        end

      String.contains?(line, "sidebar_item") ->
        "侧边栏菜单项"

      true ->
        "未知上下文"
    end
  end

  # 获取可用图标列表
  defp get_available_icons do
    # 返回 Heroicons 中实际可用的图标
    case File.ls("deps/heroicons/optimized/24/outline") do
      {:ok, files} ->
        files
        |> Enum.filter(&String.ends_with?(&1, ".svg"))
        |> Enum.map(&String.replace_suffix(&1, ".svg", ""))
        |> Enum.map(&("hero-" <> &1))
        |> Enum.sort()

      {:error, _} ->
        # 如果无法读取目录，返回常用图标列表
        get_fallback_available_icons()
    end
  end

  # 备用的可用图标列表
  defp get_fallback_available_icons do
    [
      "hero-user", "hero-users", "hero-home", "hero-cog-6-tooth", "hero-heart", "hero-star",
      "hero-check", "hero-x-mark", "hero-plus", "hero-minus", "hero-pencil", "hero-trash",
      "hero-eye", "hero-bell", "hero-magnifying-glass", "hero-arrow-right", "hero-chart-bar",
      "hero-chart-pie", "hero-gift", "hero-trophy", "hero-credit-card", "hero-shopping-cart",
      "hero-document-text", "hero-folder", "hero-server", "hero-cpu-chip", "hero-sparkles"
    ]
  end

  # 检查未使用的图标类别
  defp check_unused_icon_categories do
    used_icons = extract_sidebar_icons()

    categories = [
      {"天气", ["hero-sun", "hero-moon", "hero-cloud-rain"]},
      {"健康", ["hero-heart-pulse", "hero-plus-circle"]},
      {"交通", ["hero-truck", "hero-paper-airplane"]},
      {"安全", ["hero-finger-print", "hero-shield-check"]}
    ]

    Enum.filter(categories, fn {_name, icons} ->
      not Enum.any?(icons, &(&1 in used_icons))
    end)
    |> Enum.map(fn {name, _icons} -> name end)
  end

  # 获取图标使用统计
  defp get_icon_statistics do
    sidebar_icons = extract_sidebar_icons()
    available_icons = get_available_icons()

    [
      {"侧边栏图标总数", length(sidebar_icons)},
      {"唯一图标数", length(Enum.uniq(sidebar_icons))},
      {"可用图标总数", length(available_icons)},
      {"使用率", "#{Float.round(length(Enum.uniq(sidebar_icons)) / length(available_icons) * 100, 1)}%"}
    ]
  end
end
