defmodule Teen.Live.Admin.IconDiagnosticLive do
  @moduledoc """
  图标系统诊断页面 - 检查 Heroicons 图标系统的状态
  """
  use CypridinaWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    diagnostic_results = run_diagnostics()

    socket =
      socket
      |> assign(page_title: "图标诊断")
      |> assign(diagnostic_results: diagnostic_results)
      |> assign(overall_status: calculate_overall_status(diagnostic_results))
      |> assign(:current_url, "/admin/icon-diagnostic")
      |> assign(:fluid?, true)
    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_event("refresh_diagnostics", _params, socket) do
    diagnostic_results = run_diagnostics()

    socket =
      socket
      |> assign(diagnostic_results: diagnostic_results)
      |> assign(overall_status: calculate_overall_status(diagnostic_results))

    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <div class="flex justify-between items-center">
            <div>
              <h1 class="text-2xl font-bold flex items-center gap-2">
                <.icon name="hero-wrench-screwdriver" class="size-6 text-primary" />
                Heroicons 图标系统诊断
              </h1>
              <p class="text-base-content/70 mt-1">
                检查图标系统的配置和状态
              </p>

              <!-- 总体状态指示器 -->
              <div class="mt-3">
                <div class={[
                  "badge badge-lg",
                  case @overall_status do
                    :healthy -> "badge-success"
                    :warning -> "badge-warning"
                    :error -> "badge-error"
                  end
                ]}>
                  <.icon name={
                    case @overall_status do
                      :healthy -> "hero-check-circle"
                      :warning -> "hero-exclamation-triangle"
                      :error -> "hero-x-circle"
                    end
                  } class="size-4 mr-1" />
                  <%= case @overall_status do %>
                    <% :healthy -> %>系统正常
                    <% :warning -> %>有警告
                    <% :error -> %>有错误
                  <% end %>
                </div>
              </div>
            </div>
            <button
              phx-click="refresh_diagnostics"
              class="btn btn-primary"
            >
              <.icon name="hero-arrow-path" class="size-4" />
              刷新诊断
            </button>
          </div>
        </div>
      </div>

      <!-- 诊断结果 -->
      <div class="grid gap-6">
        <%= for {category, results} <- @diagnostic_results do %>
          <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
              <h2 class="card-title text-lg">
                <%= case category do %>
                  <% :dependencies -> %>
                    <.icon name="hero-cube" class="size-5 text-info" />
                    依赖检查
                  <% :css -> %>
                    <.icon name="hero-paint-brush" class="size-5 text-success" />
                    CSS 编译
                  <% :icons -> %>
                    <.icon name="hero-swatch" class="size-5 text-warning" />
                    图标测试
                  <% :components -> %>
                    <.icon name="hero-puzzle-piece" class="size-5 text-error" />
                    组件检查
                <% end %>
                <%= String.capitalize(to_string(category)) %>
              </h2>

              <div class="space-y-3">
                <%= for result <- results do %>
                  <div class={[
                    "alert",
                    case result.status do
                      :ok -> "alert-success"
                      :warning -> "alert-warning"
                      :error -> "alert-error"
                    end
                  ]}>
                    <.icon name={
                      case result.status do
                        :ok -> "hero-check-circle"
                        :warning -> "hero-exclamation-triangle"
                        :error -> "hero-x-circle"
                      end
                    } class="size-5" />
                    <div>
                      <h3 class="font-bold">{result.title}</h3>
                      <div class="text-sm">{result.message}</div>
                      <%= if result[:details] do %>
                        <details class="mt-2">
                          <summary class="cursor-pointer text-xs opacity-70">详细信息</summary>
                          <pre class="text-xs mt-1 bg-base-200 p-2 rounded overflow-auto">{result.details}</pre>
                        </details>
                      <% end %>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>

      <!-- 测试图标 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">
            <.icon name="hero-beaker" class="size-5 text-accent" />
            图标渲染测试
          </h2>

          <div class="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-12 gap-4">
            <%= for icon_name <- get_test_icons() do %>
              <div class="flex flex-col items-center p-2 bg-base-200/50 rounded hover:bg-base-200 transition-colors">
                <div class="flex items-center justify-center w-8 h-8 mb-1">
                  <.icon name={icon_name} class="size-6 text-base-content hover:text-primary transition-colors" />
                </div>
                <code class="text-xs text-center text-base-content/70">{String.replace_prefix(icon_name, "hero-", "")}</code>
              </div>
            <% end %>
          </div>

          <!-- 图标渲染状态 -->
          <div class="mt-4 alert alert-info">
            <.icon name="hero-information-circle" class="size-5" />
            <div>
              <h3 class="font-bold">图标渲染测试</h3>
              <div class="text-sm">
                如果上方的图标正常显示，说明 Heroicons 系统工作正常。
                如果看到空白或错误，请检查 CSS 编译和依赖配置。
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用示例 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">
            <.icon name="hero-code-bracket" class="size-5 text-secondary" />
            使用示例
          </h2>

          <div class="space-y-4">
            <div>
              <h3 class="font-semibold mb-2">基本用法</h3>
              <div class="mockup-code">
                <pre data-prefix="1"><code>&lt;.icon name="hero-user" class="size-5" /&gt;</code></pre>
                <pre data-prefix="2"><code>&lt;.icon name="hero-home" class="size-6 text-primary" /&gt;</code></pre>
              </div>
            </div>

            <div>
              <h3 class="font-semibold mb-2">在 Backpex 侧边栏中使用</h3>
              <div class="mockup-code">
                <pre data-prefix="1"><code>&lt;.sidebar_item</code></pre>
                <pre data-prefix="2"><code>  current_url={@current_url}</code></pre>
                <pre data-prefix="3"><code>  navigate="/admin/users"</code></pre>
                <pre data-prefix="4"><code>  icon="hero-users"</code></pre>
                <pre data-prefix="5"><code>  text="用户管理"</code></pre>
                <pre data-prefix="6"><code>  color="primary"</code></pre>
                <pre data-prefix="7"><code>/&gt;</code></pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  # 运行诊断检查
  defp run_diagnostics do
    [
      dependencies: check_dependencies(),
      css: check_css_compilation(),
      icons: check_icon_availability(),
      components: check_component_integration()
    ]
  end

  # 计算总体状态
  defp calculate_overall_status(diagnostic_results) do
    all_results =
      diagnostic_results
      |> Enum.flat_map(fn {_category, results} -> results end)

    cond do
      Enum.any?(all_results, &(&1.status == :error)) -> :error
      Enum.any?(all_results, &(&1.status == :warning)) -> :warning
      true -> :healthy
    end
  end

  # 检查依赖
  defp check_dependencies do
    results = []

    # 检查 Heroicons 依赖
    heroicons_result =
      case File.exists?("deps/heroicons") do
        true ->
          %{
            status: :ok,
            title: "Heroicons 依赖",
            message: "Heroicons 依赖已正确安装"
          }
        false ->
          %{
            status: :error,
            title: "Heroicons 依赖",
            message: "Heroicons 依赖未找到"
          }
      end

    # 检查图标文件
    icons_dir_result =
      case File.ls("deps/heroicons/optimized/24/outline") do
        {:ok, files} ->
          icon_count = length(files)
          %{
            status: :ok,
            title: "图标文件",
            message: "找到 #{icon_count} 个图标文件",
            details: "图标目录: deps/heroicons/optimized/24/outline"
          }
        {:error, reason} ->
          %{
            status: :error,
            title: "图标文件",
            message: "无法读取图标目录: #{reason}"
          }
      end

    [heroicons_result, icons_dir_result] ++ results
  end

  # 检查 CSS 编译
  defp check_css_compilation do
    results = []

    # 检查 CSS 文件
    css_file_result =
      case File.exists?("priv/static/assets/css/app.css") do
        true ->
          %{
            status: :ok,
            title: "CSS 文件",
            message: "CSS 文件已生成"
          }
        false ->
          %{
            status: :error,
            title: "CSS 文件",
            message: "CSS 文件未找到，请运行 mix tailwind cypridina"
          }
      end

    # 检查 CSS 中的图标
    css_icons_result =
      case File.read("priv/static/assets/css/app.css") do
        {:ok, content} ->
          if String.contains?(content, "hero-user") do
            %{
              status: :ok,
              title: "CSS 中的图标",
              message: "CSS 文件包含 Heroicons 样式"
            }
          else
            %{
              status: :warning,
              title: "CSS 中的图标",
              message: "CSS 文件可能不包含 Heroicons 样式，请重新编译"
            }
          end
        {:error, reason} ->
          %{
            status: :error,
            title: "CSS 中的图标",
            message: "无法读取 CSS 文件: #{reason}"
          }
      end

    [css_file_result, css_icons_result] ++ results
  end

  # 检查图标可用性
  defp check_icon_availability do
    test_icons = ["hero-user", "hero-users", "hero-user-group", "hero-no-symbol", "hero-device-phone-mobile", "hero-check-circle", "hero-calendar-days", "hero-ticket"]

    # 检查图标文件是否存在
    icon_files_result =
      case File.ls("deps/heroicons/optimized/24/outline") do
        {:ok, files} ->
          svg_files = Enum.filter(files, &String.ends_with?(&1, ".svg"))
          missing_icons =
            test_icons
            |> Enum.map(&String.replace_prefix(&1, "hero-", ""))
            |> Enum.map(&(&1 <> ".svg"))
            |> Enum.reject(&(&1 in svg_files))

          if Enum.empty?(missing_icons) do
            %{
              status: :ok,
              title: "测试图标文件",
              message: "所有测试图标文件都存在",
              details: "检查的图标: #{Enum.join(test_icons, ", ")}"
            }
          else
            %{
              status: :warning,
              title: "测试图标文件",
              message: "部分图标文件缺失",
              details: "缺失的图标: #{Enum.join(missing_icons, ", ")}"
            }
          end
        {:error, reason} ->
          %{
            status: :error,
            title: "图标目录",
            message: "无法访问图标目录: #{reason}"
          }
      end

    # 检查 CSS 中的图标样式
    css_icons_result =
      case File.read("priv/static/assets/css/app.css") do
        {:ok, content} ->
          missing_in_css =
            test_icons
            |> Enum.reject(&String.contains?(content, &1))

          if Enum.empty?(missing_in_css) do
            %{
              status: :ok,
              title: "CSS 中的图标样式",
              message: "所有测试图标样式都在 CSS 中",
              details: "检查的图标: #{Enum.join(test_icons, ", ")}"
            }
          else
            %{
              status: :warning,
              title: "CSS 中的图标样式",
              message: "部分图标样式可能缺失",
              details: "可能缺失的图标: #{Enum.join(missing_in_css, ", ")}"
            }
          end
        {:error, reason} ->
          %{
            status: :error,
            title: "CSS 文件读取",
            message: "无法读取 CSS 文件: #{reason}"
          }
      end

    [icon_files_result, css_icons_result]
  end

  # 检查组件集成
  defp check_component_integration do
    results = []

    # 检查图标组件模块是否存在
    icon_component_result =
      try do
        # 检查模块是否存在
        case Code.ensure_loaded(CypridinaWeb.Components.Icon) do
          {:module, _} ->
            %{
              status: :ok,
              title: "图标组件模块",
              message: "图标组件模块已加载"
            }
          {:error, reason} ->
            %{
              status: :error,
              title: "图标组件模块",
              message: "图标组件模块加载失败: #{reason}"
            }
        end
      rescue
        error ->
          %{
            status: :error,
            title: "图标组件模块",
            message: "图标组件检查错误: #{inspect(error)}"
          }
      end

    # 检查核心组件是否可用
    core_component_result =
      try do
        case Code.ensure_loaded(CypridinaWeb.CoreComponents) do
          {:module, _} ->
            %{
              status: :ok,
              title: "核心组件",
              message: "核心组件模块已加载"
            }
          {:error, reason} ->
            %{
              status: :warning,
              title: "核心组件",
              message: "核心组件模块状态: #{reason}"
            }
        end
      rescue
        error ->
          %{
            status: :warning,
            title: "核心组件",
            message: "核心组件检查: #{inspect(error)}"
          }
      end

    [icon_component_result, core_component_result] ++ results
  end

  # 获取测试图标
  defp get_test_icons do
    [
      "hero-user", "hero-users", "hero-home", "hero-cog-6-tooth",
      "hero-heart", "hero-star", "hero-check", "hero-x-mark",
      "hero-plus", "hero-minus", "hero-pencil", "hero-trash",
      "hero-eye", "hero-bell", "hero-magnifying-glass", "hero-arrow-right"
    ]
  end
end
