defmodule Teen.Live.Admin.IconForceCompileLive do
  @moduledoc """
  强制编译图标页面 - 通过在模板中使用图标来强制 Tailwind 编译
  """
  use CypridinaWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    socket = assign(socket, page_title: "强制编译图标")
    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h1 class="text-2xl font-bold">强制编译图标测试</h1>
          <p class="text-base-content/70">
            这个页面通过在模板中使用图标来强制 Tailwind 编译它们
          </p>
        </div>
      </div>

      <!-- 强制编译的图标 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">需要强制编译的图标</h2>
          
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <!-- 用户相关图标 -->
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-users" class="size-8 mb-2 text-primary" />
              <code class="text-xs">hero-users</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-user-group" class="size-8 mb-2 text-secondary" />
              <code class="text-xs">hero-user-group</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-no-symbol" class="size-8 mb-2 text-error" />
              <code class="text-xs">hero-no-symbol</code>
            </div>
            
            <!-- 设备和通信图标 -->
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-device-phone-mobile" class="size-8 mb-2 text-info" />
              <code class="text-xs">hero-device-phone-mobile</code>
            </div>
            
            <!-- 时间和日期图标 -->
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-calendar-days" class="size-8 mb-2 text-warning" />
              <code class="text-xs">hero-calendar-days</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-ticket" class="size-8 mb-2 text-success" />
              <code class="text-xs">hero-ticket</code>
            </div>
            
            <!-- 其他可能缺失的图标 -->
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-chart-bar-square" class="size-8 mb-2 text-accent" />
              <code class="text-xs">hero-chart-bar-square</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-clipboard-document-list" class="size-8 mb-2 text-neutral" />
              <code class="text-xs">hero-clipboard-document-list</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-gift-top" class="size-8 mb-2 text-primary" />
              <code class="text-xs">hero-gift-top</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-squares-plus" class="size-8 mb-2 text-secondary" />
              <code class="text-xs">hero-squares-plus</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-arrow-uturn-left" class="size-8 mb-2 text-info" />
              <code class="text-xs">hero-arrow-uturn-left</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-megaphone" class="size-8 mb-2 text-warning" />
              <code class="text-xs">hero-megaphone</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-hand-raised" class="size-8 mb-2 text-error" />
              <code class="text-xs">hero-hand-raised</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-building-library" class="size-8 mb-2 text-success" />
              <code class="text-xs">hero-building-library</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-receipt-percent" class="size-8 mb-2 text-accent" />
              <code class="text-xs">hero-receipt-percent</code>
            </div>
            
            <div class="flex flex-col items-center p-4 bg-base-200/50 rounded">
              <.icon name="hero-globe-alt" class="size-8 mb-2 text-neutral" />
              <code class="text-xs">hero-globe-alt</code>
            </div>
          </div>
        </div>
      </div>

      <!-- 编译状态检查 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">编译状态说明</h2>
          <div class="alert alert-info">
            <.icon name="hero-information-circle" class="size-5" />
            <div>
              <h3 class="font-bold">如何验证图标是否编译成功</h3>
              <div class="text-sm mt-1">
                <p>1. 如果图标正常显示，说明已成功编译到 CSS 中</p>
                <p>2. 如果图标显示为空白或方块，说明 CSS 中缺少对应样式</p>
                <p>3. 访问此页面后重新编译 CSS: <code>mix tailwind cypridina --watch=false</code></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
