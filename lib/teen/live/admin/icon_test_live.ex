defmodule Teen.Live.Admin.IconTestLive do
  @moduledoc """
  图标测试页面 - 显示所有可用的 Heroicons 图标
  """
  use CypridinaWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    socket =
      socket
      |> assign(page_title: "图标测试")
      |> assign(search_query: "")
      |> assign(:current_url, "/admin/icon-test")
      |> assign(:fluid?, true)
      |> assign(selected_category: "all")
      |> assign(all_icons: get_all_icons())
      |> assign(filtered_icons: get_all_icons())
      |> assign(categories: get_icon_categories())

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_event("search", %{"search" => %{"query" => query}}, socket) do
    filtered_icons = filter_icons(socket.assigns.all_icons, query, socket.assigns.selected_category)

    socket =
      socket
      |> assign(search_query: query)
      |> assign(filtered_icons: filtered_icons)

    {:ok, socket}
  end

  @impl true
  def handle_event("filter_category", %{"category" => category}, socket) do
    filtered_icons = filter_icons(socket.assigns.all_icons, socket.assigns.search_query, category)

    socket =
      socket
      |> assign(selected_category: category)
      |> assign(filtered_icons: filtered_icons)

    {:ok, socket}
  end

  @impl true
  def handle_event("copy_icon", %{"icon" => icon_name}, socket) do
    # 发送 JavaScript 事件来复制到剪贴板
    socket =
      socket
      |> push_event("copy-to-clipboard", %{text: icon_name})
      |> put_flash(:info, "图标名称已复制: #{icon_name}")

    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h1 class="card-title text-2xl">
            <.icon name="hero-swatch" class="size-6 text-primary" />
            Heroicons 图标库
          </h1>
          <p class="text-base-content/70">
            浏览和测试所有可用的 Heroicons 图标，点击图标可复制名称
          </p>
          <div class="stats shadow">
            <div class="stat">
              <div class="stat-title">总图标数</div>
              <div class="stat-value text-primary">{length(@all_icons)}</div>
            </div>
            <div class="stat">
              <div class="stat-title">当前显示</div>
              <div class="stat-value text-secondary">{length(@filtered_icons)}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <div class="flex flex-col lg:flex-row gap-4">
            <!-- 搜索框 -->
            <div class="flex-1">
              <form phx-submit="search" phx-change="search">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">搜索图标</span>
                  </label>
                  <div class="input-group">
                    <input
                      type="text"
                      name="search[query]"
                      value={@search_query}
                      placeholder="输入图标名称..."
                      class="input input-bordered flex-1"
                    />
                    <button type="submit" class="btn btn-square btn-primary">
                      <.icon name="hero-magnifying-glass" class="size-4" />
                    </button>
                  </div>
                </div>
              </form>
            </div>

            <!-- 分类筛选 -->
            <div class="lg:w-64">
              <div class="form-control">
                <label class="label">
                  <span class="label-text">图标分类</span>
                </label>
                <select
                  class="select select-bordered"
                  phx-change="filter_category"
                  name="category"
                >
                  <%= for {category, label} <- @categories do %>
                    <option value={category} selected={@selected_category == category}>
                      {label}
                    </option>
                  <% end %>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图标网格 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <%= if length(@filtered_icons) > 0 do %>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
              <%= for icon_name <- @filtered_icons do %>
                <.icon_card icon={icon_name} />
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-12">
              <.icon name="hero-magnifying-glass" class="size-16 text-base-content/30 mx-auto mb-4" />
              <h3 class="text-lg font-semibold text-base-content/70">未找到匹配的图标</h3>
              <p class="text-base-content/50">尝试调整搜索条件或选择其他分类</p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">
            <.icon name="hero-information-circle" class="size-5 text-info" />
            使用说明
          </h2>
          <div class="space-y-4">
            <div class="alert alert-info">
              <.icon name="hero-light-bulb" class="size-5" />
              <div>
                <h3 class="font-bold">如何使用图标</h3>
                <div class="text-sm">
                  在 Phoenix LiveView 中使用图标组件：
                  <code class="bg-base-200 px-2 py-1 rounded ml-2">&lt;.icon name="hero-图标名" class="size-5" /&gt;</code>
                </div>
              </div>
            </div>

            <div class="alert alert-success">
              <.icon name="hero-check-circle" class="size-5" />
              <div>
                <h3 class="font-bold">Backpex 侧边栏中使用</h3>
                <div class="text-sm">
                  在侧边栏组件中：
                  <code class="bg-base-200 px-2 py-1 rounded ml-2">&lt;.sidebar_item icon="hero-图标名" text="菜单文本" /&gt;</code>
                </div>
              </div>
            </div>

            <div class="alert alert-warning">
              <.icon name="hero-exclamation-triangle" class="size-5" />
              <div>
                <h3 class="font-bold">注意事项</h3>
                <div class="text-sm">
                  • 所有图标名称必须以 "hero-" 开头<br>
                  • 使用 TailwindCSS 类控制大小和颜色<br>
                  • 推荐使用 size-4, size-5, size-6 等预设大小
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // 处理复制到剪贴板的功能
      window.addEventListener("phx:copy-to-clipboard", (e) => {
        const text = e.detail.text;
        if (navigator.clipboard && window.isSecureContext) {
          navigator.clipboard.writeText(text).then(() => {
            console.log("已复制到剪贴板:", text);
          }).catch(err => {
            console.error("复制失败:", err);
            fallbackCopyTextToClipboard(text);
          });
        } else {
          fallbackCopyTextToClipboard(text);
        }
      });

      function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
          document.execCommand('copy');
          console.log("已复制到剪贴板 (fallback):", text);
        } catch (err) {
          console.error("复制失败 (fallback):", err);
        }
        document.body.removeChild(textArea);
      }
    </script>
    """
  end

  # 图标卡片组件
  attr :icon, :string, required: true

  defp icon_card(assigns) do
    ~H"""
    <div
      class="flex flex-col items-center p-4 bg-base-200/50 rounded-lg hover:bg-base-200 transition-all duration-200 cursor-pointer group hover:shadow-md"
      phx-click="copy_icon"
      phx-value-icon={@icon}
      title={"点击复制: #{@icon}"}
    >
      <.icon name={@icon} class="size-8 mb-2 text-base-content group-hover:text-primary transition-colors" />
      <code class="text-xs text-center font-mono text-base-content/70 group-hover:text-base-content transition-colors break-all">
        {@icon}
      </code>
    </div>
    """
  end

  # 获取所有可用的图标
  defp get_all_icons do
    # 直接返回扩展的常用图标列表，确保所有图标都可用
    get_common_icons()
  end

  # 常用图标列表（扩展版）
  defp get_common_icons do
    [
      # 用户和身份相关
      "hero-user", "hero-users", "hero-user-group", "hero-user-circle", "hero-user-plus",
      "hero-user-minus", "hero-identification", "hero-no-symbol", "hero-face-smile",
      "hero-face-frown", "hero-academic-cap", "hero-briefcase",

      # 导航和箭头
      "hero-home", "hero-bars-3", "hero-x-mark", "hero-chevron-down", "hero-chevron-up",
      "hero-chevron-left", "hero-chevron-right", "hero-arrow-left", "hero-arrow-right",
      "hero-arrow-up", "hero-arrow-down", "hero-arrow-path", "hero-arrow-uturn-left",
      "hero-arrow-uturn-right", "hero-arrow-top-right-on-square",

      # 基本操作
      "hero-plus", "hero-minus", "hero-pencil", "hero-trash", "hero-eye", "hero-eye-slash",
      "hero-check", "hero-check-circle", "hero-x-circle", "hero-exclamation-triangle",
      "hero-exclamation-circle", "hero-question-mark-circle", "hero-hand-raised",

      # 系统和设置
      "hero-cog-6-tooth", "hero-adjustments-horizontal", "hero-adjustments-vertical",
      "hero-bell", "hero-bell-alert", "hero-magnifying-glass", "hero-magnifying-glass-plus",
      "hero-magnifying-glass-minus", "hero-funnel", "hero-squares-2x2", "hero-squares-plus",
      "hero-list-bullet", "hero-table-cells", "hero-view-columns",

      # 文档和文件
      "hero-document-text", "hero-document-plus", "hero-document-minus", "hero-document-duplicate",
      "hero-folder", "hero-folder-open", "hero-folder-plus", "hero-archive-box",
      "hero-clipboard", "hero-clipboard-document", "hero-clipboard-document-list",
      "hero-clipboard-document-check", "hero-newspaper",

      # 技术和网络
      "hero-server", "hero-cpu-chip", "hero-command-line", "hero-code-bracket",
      "hero-globe-alt", "hero-wifi", "hero-signal", "hero-rss", "hero-link",
      "hero-cloud", "hero-cloud-arrow-up", "hero-cloud-arrow-down",

      # 商务和金融
      "hero-credit-card", "hero-banknotes", "hero-currency-dollar", "hero-currency-euro",
      "hero-currency-pound", "hero-currency-yen", "hero-shopping-cart", "hero-shopping-bag",
      "hero-receipt-percent", "hero-calculator", "hero-scale",

      # 奖励和游戏
      "hero-gift", "hero-gift-top", "hero-trophy", "hero-star", "hero-heart",
      "hero-fire", "hero-bolt", "hero-sparkles", "hero-puzzle-piece",
      "hero-dice-1", "hero-dice-2", "hero-dice-3", "hero-dice-4", "hero-dice-5", "hero-dice-6",

      # 通信和社交
      "hero-envelope", "hero-envelope-open", "hero-phone", "hero-device-phone-mobile",
      "hero-chat-bubble-left", "hero-chat-bubble-left-right", "hero-megaphone",
      "hero-speaker-wave", "hero-microphone", "hero-at-symbol",

      # 媒体和娱乐
      "hero-photo", "hero-camera", "hero-video-camera", "hero-film", "hero-musical-note",
      "hero-play", "hero-pause", "hero-stop", "hero-forward", "hero-backward",
      "hero-speaker-x-mark", "hero-volume-up", "hero-volume-off",

      # 工具和实用
      "hero-wrench", "hero-hammer", "hero-screwdriver", "hero-key", "hero-lock-closed",
      "hero-lock-open", "hero-shield-check", "hero-shield-exclamation", "hero-bug-ant",
      "hero-light-bulb", "hero-beaker", "hero-scissors", "hero-printer",

      # 图表和数据
      "hero-chart-bar", "hero-chart-pie", "hero-chart-bar-square", "hero-presentation-chart-line",
      "hero-presentation-chart-bar", "hero-table-cells", "hero-variable",

      # 时间和日期
      "hero-calendar", "hero-calendar-days", "hero-clock", "hero-stopwatch",

      # 位置和地图
      "hero-map-pin", "hero-map", "hero-building-office", "hero-building-office-2",
      "hero-building-library", "hero-building-storefront", "hero-home-modern",

      # 交通和物流
      "hero-truck", "hero-paper-airplane", "hero-rocket-launch",

      # 健康和医疗
      "hero-heart-pulse", "hero-plus-circle", "hero-minus-circle",

      # 天气和自然
      "hero-sun", "hero-moon", "hero-cloud-rain", "hero-bolt-slash",

      # 电池和电源
      "hero-battery-0", "hero-battery-50", "hero-battery-100", "hero-power",

      # 票据和标签
      "hero-ticket", "hero-tag", "hero-bookmark", "hero-flag",

      # 箭头和方向（更多）
      "hero-arrow-long-left", "hero-arrow-long-right", "hero-arrow-long-up", "hero-arrow-long-down",
      "hero-arrows-pointing-in", "hero-arrows-pointing-out", "hero-arrow-path-rounded-square",

      # 界面元素
      "hero-window", "hero-computer-desktop", "hero-device-tablet", "hero-tv",
      "hero-radio", "hero-signal-slash", "hero-qr-code", "hero-bars-arrow-up",
      "hero-bars-arrow-down", "hero-ellipsis-horizontal", "hero-ellipsis-vertical",

      # 安全和验证
      "hero-finger-print", "hero-key-solid", "hero-lock-solid", "hero-shield-solid",

      # 更多实用图标
      "hero-swatch", "hero-paint-brush", "hero-eyedropper", "hero-cube",
      "hero-cube-transparent", "hero-squares-2x2-solid", "hero-view-finder-circle"
    ]
    |> Enum.sort()
  end

  # 图标分类
  defp get_icon_categories do
    [
      {"all", "全部图标"},
      {"user", "用户相关"},
      {"navigation", "导航相关"},
      {"action", "操作相关"},
      {"system", "系统相关"},
      {"business", "商务相关"},
      {"communication", "通信相关"},
      {"media", "媒体相关"},
      {"tool", "工具相关"},
      {"chart", "图表相关"},
      {"other", "其他"}
    ]
  end

  # 筛选图标
  defp filter_icons(icons, query, category) do
    icons
    |> filter_by_query(query)
    |> filter_by_category(category)
  end

  defp filter_by_query(icons, ""), do: icons
  defp filter_by_query(icons, query) do
    query_lower = String.downcase(query)
    Enum.filter(icons, fn icon ->
      String.contains?(String.downcase(icon), query_lower)
    end)
  end

  defp filter_by_category(icons, "all"), do: icons
  defp filter_by_category(icons, category) do
    keywords = get_category_keywords(category)

    Enum.filter(icons, fn icon ->
      Enum.any?(keywords, fn keyword ->
        String.contains?(icon, keyword)
      end)
    end)
  end

  defp get_category_keywords(category) do
    case category do
      "user" -> ["user", "person", "account", "profile", "identification"]
      "navigation" -> ["arrow", "chevron", "bars", "home", "menu", "navigation"]
      "action" -> ["plus", "minus", "pencil", "trash", "edit", "delete", "check", "x-"]
      "system" -> ["cog", "settings", "server", "cpu", "system", "adjustments"]
      "business" -> ["credit", "money", "dollar", "cart", "shopping", "gift", "trophy"]
      "communication" -> ["envelope", "phone", "chat", "message", "bell", "megaphone"]
      "media" -> ["photo", "video", "music", "play", "pause", "camera"]
      "tool" -> ["wrench", "key", "lock", "shield", "bug", "light-bulb"]
      "chart" -> ["chart", "graph", "presentation", "analytics"]
      "other" -> []
    end
  end
end
