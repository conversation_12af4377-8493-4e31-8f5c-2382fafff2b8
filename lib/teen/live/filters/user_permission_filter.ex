defmodule Teen.Live.Filters.UserPermissionFilter do
  @moduledoc """
  用户权限级别过滤器

  用于在用户管理页面中按权限级别过滤用户
  """

  use Backpex.Filter
  import Phoenix.Component
  import CypridinaWeb.CoreComponents

  @impl Backpex.Filter
  def label, do: "权限级别"

  @impl Backpex.Filter
  def type, do: :select

  @impl Backpex.Filter
  def options do
    [
      {"全部", nil},
      {"普通用户", 0},
      {"管理员", 1},
      {"超级管理员", 2}
    ]
  end

  @impl Backpex.Filter
  def can?(_assigns), do: true

  @impl Backpex.Filter
  def render_form(assigns) do
    ~H"""
    <div>
      <.input field={@form[:value]} type="select" options={options()} prompt="选择权限级别" />
    </div>
    """
  end

  @impl Backpex.Filter
  def query(query, value, _live_resource) when is_nil(value) or value == "" do
    query
  end

  def query(query, value, _live_resource) do
    import Ash.Query

    query
    |> filter(permission_level == ^value)
  end
end
