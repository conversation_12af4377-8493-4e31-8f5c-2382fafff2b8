defmodule Teen.Live.Jackpot.JackpotManagementLive do
  @moduledoc """
  奖池管理LiveView界面

  提供奖池配置的可视化管理功能
  """

  use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, :live_view
  require Logger

  alias Teen.GameManagement.JackpotConfig
  alias Teen.GameSystem.JackpotManager

  @impl true
  def mount(_params, _session, socket) do
    if connected?(socket) do
      # 定时刷新奖池余额
      :timer.send_interval(30_000, self(), :refresh_balances)
    end

    socket =
      socket
      |> assign(:page_title, "奖池管理")
      |> assign(:current_url, "/admin/jackpots")
      |> assign(:selected_game, nil)
      |> assign(:show_form, false)
      |> assign(:form_mode, :create)
      |> assign(:editing_config, nil)
      |> assign(:show_contribution_form, false)
      |> assign(:editing_contribution_config, nil)
      |> assign(:fluid?, true)
      |> load_jackpot_configs()

    {:ok, socket, layout: {Teen.Layouts, :admin}}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "奖池管理")
    |> assign(:show_form, false)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "新建奖池配置")
    |> assign(:show_form, true)
    |> assign(:form_mode, :create)
    |> assign(:editing_config, nil)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    config = Ash.get!(JackpotConfig, id)

    socket
    |> assign(:page_title, "编辑奖池配置")
    |> assign(:show_form, true)
    |> assign(:form_mode, :edit)
    |> assign(:editing_config, config)
  end

  @impl true
  def handle_event("select_game", %{"game" => game_id}, socket) do
    {:noreply, assign(socket, :selected_game, game_id)}
  end

  def handle_event("show_all_games", _params, socket) do
    {:noreply, assign(socket, :selected_game, nil)}
  end

  def handle_event("new_config", _params, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/jackpots/new")}
  end

  def handle_event("edit_config", %{"id" => id}, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/jackpots/#{id}/edit")}
  end

  def handle_event("delete_config", %{"id" => id}, socket) do
    try do
      config = Ash.get!(JackpotConfig, id)

      case JackpotConfig.destroy(config) do
        :ok ->
          socket =
            socket
            |> put_flash(:info, "奖池配置删除成功")
            |> load_jackpot_configs()

          {:noreply, socket}

        {:error, _changeset} ->
          {:noreply, put_flash(socket, :error, "删除奖池配置失败")}
      end
    rescue
      error ->
        Logger.error("删除奖池配置异常: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "删除奖池配置异常")}
    end
  end

  def handle_event("initialize_jackpot", %{"id" => id, "amount" => amount_str}, socket) do
    try do
      amount = String.to_integer(amount_str)
      config = Ash.get!(JackpotConfig, id)

      case JackpotManager.init_single_jackpot(config.game_id, config.jackpot_id, amount) do
        {:ok, _} ->
          JackpotConfig.initialize_jackpot!(config, amount: amount)

          socket =
            socket
            |> put_flash(:info, "奖池初始化成功")
            |> load_jackpot_configs()

          {:noreply, socket}

        {:error, reason} ->
          {:noreply, put_flash(socket, :error, "奖池初始化失败: #{inspect(reason)}")}
      end
    rescue
      error ->
        Logger.error("奖池初始化异常: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "奖池初始化异常")}
    end
  end

  def handle_event("reset_jackpot", %{"id" => id}, socket) do
    try do
      config = Ash.get!(JackpotConfig, id)

      case JackpotManager.reset_jackpot(config.game_id, config.jackpot_id, config.base_amount) do
        {:ok, _} ->
          JackpotConfig.reset_jackpot!(config)

          socket =
            socket
            |> put_flash(:info, "奖池重置成功")
            |> load_jackpot_configs()

          {:noreply, socket}

        {:error, reason} ->
          {:noreply, put_flash(socket, :error, "奖池重置失败: #{inspect(reason)}")}
      end
    rescue
      error ->
        Logger.error("奖池重置异常: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "奖池重置异常")}
    end
  end

  def handle_event("set_contribution_rate", %{"id" => id}, socket) do
    try do
      config = Ash.get!(JackpotConfig, id)

      socket =
        socket
        |> assign(:show_contribution_form, true)
        |> assign(:editing_contribution_config, config)

      {:noreply, socket}
    rescue
      error ->
        Logger.error("获取奖池配置异常: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "获取奖池配置失败")}
    end
  end

  def handle_event("cancel_contribution_form", _params, socket) do
    Logger.info("📡 [JACKPOT] 取消贡献率表单")

    socket =
      socket
      |> assign(:show_contribution_form, false)
      |> assign(:editing_contribution_config, nil)

    {:noreply, socket}
  end

  def handle_event("update_contribution_rate", params, socket) do
    Logger.info("📡 [JACKPOT] 收到更新贡献率请求，参数: #{inspect(params)}")

    try do
      # 处理参数 - 可能没有 config_id
      case params do
        %{"config_id" => id, "contribution_rate" => rate_str} ->
          config = Ash.get!(JackpotConfig, id)
          handle_contribution_rate_update(config, rate_str, socket)

        %{"contribution_rate" => rate_str} ->
          # 如果没有 config_id，尝试从 socket assigns 获取
          if socket.assigns.editing_contribution_config do
            config = socket.assigns.editing_contribution_config
            handle_contribution_rate_update(config, rate_str, socket)
          else
            {:noreply, put_flash(socket, :error, "无法找到要更新的配置")}
          end

        _ ->
          {:noreply, put_flash(socket, :error, "参数格式错误")}
      end
    rescue
      error ->
        Logger.error("更新贡献率异常: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "更新贡献率异常")}
    end
  end

  defp handle_contribution_rate_update(config, rate_str, socket) do
    # 转换贡献率：从百分比形式转换为小数形式
    case Float.parse(rate_str) do
      {rate, _} when rate >= 0 and rate <= 100 ->
        # 转换为Decimal类型 - 使用字符串或者先转换为字符串
        decimal_rate = Decimal.div(Decimal.new(to_string(rate)), Decimal.new("100"))

        # 只更新贡献率，不更新其他字段
        case Ash.update(config, %{contribution_rate: decimal_rate}, action: :update_contribution_rate) do
          {:ok, updated_config} ->
            # 刷新贡献率缓存
            case JackpotManager.refresh_contribution_rates(updated_config.game_id) do
              :ok ->
                Logger.info("✅ [JACKPOT_CONFIG] 游戏 #{updated_config.game_id} 贡献率缓存刷新成功")
              {:error, reason} ->
                Logger.warn("⚠️ [JACKPOT_CONFIG] 游戏 #{updated_config.game_id} 贡献率缓存刷新失败: #{inspect(reason)}")
            end

            socket =
              socket
              |> put_flash(:info, "贡献率更新成功，缓存已刷新")
              |> assign(:show_contribution_form, false)
              |> assign(:editing_contribution_config, nil)
              |> load_jackpot_configs()

            {:noreply, socket}

          {:error, error} ->
            error_message = case error do
              %Ash.Error.Invalid{errors: errors} ->
                errors
                |> Enum.map(fn e -> Map.get(e, :message, "Unknown error") end)
                |> Enum.join(", ")
              _ ->
                inspect(error)
            end
            {:noreply, put_flash(socket, :error, "更新贡献率失败: #{error_message}")}
        end

      _ ->
        {:noreply, put_flash(socket, :error, "无效的贡献率值")}
    end
  end

  def handle_event("save_config", %{"jackpot_config" => params}, socket) do
    # 自动计算最小和最大金额
    params_with_amounts = calculate_min_max_amounts(params)

    case socket.assigns.form_mode do
      :create ->
        case JackpotConfig.create(params_with_amounts) do
          {:ok, config} ->
            # 如果设置了底金，自动初始化奖池
            if config.base_amount > 0 do
              JackpotManager.init_single_jackpot(
                config.game_id,
                config.jackpot_id,
                config.base_amount
              )
            end

            # 🔄 刷新贡献率缓存
            case JackpotManager.refresh_contribution_rates(config.game_id) do
              :ok ->
                Logger.info("✅ [JACKPOT_CONFIG] 游戏 #{config.game_id} 贡献率缓存刷新成功")
              {:error, reason} ->
                Logger.warn("⚠️ [JACKPOT_CONFIG] 游戏 #{config.game_id} 贡献率缓存刷新失败: #{inspect(reason)}")
            end

            socket =
              socket
              |> put_flash(:info, "奖池配置创建成功，贡献率缓存已更新")
              |> push_patch(to: ~p"/admin/jackpots")
              |> load_jackpot_configs()

            {:noreply, socket}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "创建奖池配置失败: #{format_errors(changeset)}")}
        end

      :edit ->
        config = socket.assigns.editing_config

        case JackpotConfig.update(config, params_with_amounts) do
          {:ok, updated_config} ->
            # 🔧 自动同步 BalanceCache - 只有在底金真正发生变化时才重置奖池余额
            base_amount_changed =
              Map.has_key?(params_with_amounts, "base_amount") and
              config.base_amount != updated_config.base_amount

            if base_amount_changed do
              case JackpotManager.reset_jackpot(
                     updated_config.game_id,
                     updated_config.jackpot_id,
                     updated_config.base_amount
                   ) do
                {:ok, _} ->
                  Logger.info(
                    "✅ [JACKPOT_SYNC] 底金变更，奖池余额已重置: #{updated_config.game_id}:#{updated_config.jackpot_id} -> ₹#{updated_config.base_amount}"
                  )

                {:error, reason} ->
                  Logger.warn("⚠️ [JACKPOT_SYNC] 奖池余额同步失败: #{inspect(reason)}")
              end
            end

            # 🔄 刷新贡献率缓存
            case JackpotManager.refresh_contribution_rates(updated_config.game_id) do
              :ok ->
                Logger.info("✅ [JACKPOT_CONFIG] 游戏 #{updated_config.game_id} 贡献率缓存刷新成功")
              {:error, reason} ->
                Logger.warn("⚠️ [JACKPOT_CONFIG] 游戏 #{updated_config.game_id} 贡献率缓存刷新失败: #{inspect(reason)}")
            end

            socket =
              socket
              |> put_flash(:info, "奖池配置更新成功，余额已自动同步，贡献率缓存已更新")
              |> push_patch(to: ~p"/admin/jackpots")
              |> load_jackpot_configs()

            {:noreply, socket}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "更新奖池配置失败: #{format_errors(changeset)}")}
        end
    end
  end

  def handle_event("cancel_form", _params, socket) do
    {:noreply, push_patch(socket, to: ~p"/admin/jackpots")}
  end

  def handle_event("refresh_contribution_cache", %{"game-id" => game_id}, socket) do
    try do
      case JackpotManager.refresh_contribution_rates(game_id) do
        :ok ->
          socket =
            socket
            |> put_flash(:info, "贡献率缓存刷新成功")
            |> load_jackpot_configs()

          {:noreply, socket}

        {:error, reason} ->
          {:noreply, put_flash(socket, :error, "贡献率缓存刷新失败: #{inspect(reason)}")}
      end
    rescue
      error ->
        Logger.error("刷新贡献率缓存异常: #{inspect(error)}")
        {:noreply, put_flash(socket, :error, "刷新贡献率缓存异常")}
    end
  end

  @impl true
  def handle_info(:refresh_balances, socket) do
    {:noreply, load_jackpot_configs(socket)}
  end

  # 私有函数

  defp load_jackpot_configs(socket) do
    try do
      configs = Ash.read!(JackpotConfig)

      # 获取实时余额并按游戏分组
      configs_with_balance =
        Enum.map(configs, fn config ->
          current_balance = JackpotManager.get_jackpot_balance(config.game_id, config.jackpot_id)

          config
          |> Map.put(:real_time_balance, current_balance)
          |> Map.put(
            :is_healthy,
            current_balance >= config.min_amount && current_balance <= config.max_amount
          )
          |> Map.put(
            :balance_percentage,
            calculate_balance_percentage(current_balance, config.max_amount)
          )
        end)

      grouped_configs = Enum.group_by(configs_with_balance, & &1.game_id)
      game_list = Map.keys(grouped_configs) |> Enum.sort()

      socket
      |> assign(:jackpot_configs, grouped_configs)
      |> assign(:game_list, game_list)
    rescue
      error ->
        Logger.error("加载奖池配置失败: #{inspect(error)}")

        socket
        |> put_flash(:error, "加载奖池配置失败")
        |> assign(:jackpot_configs, %{})
        |> assign(:game_list, [])
    end
  end

  defp calculate_balance_percentage(current_balance, max_amount) when max_amount > 0 do
    (current_balance / max_amount * 100) |> Float.round(1)
  end

  defp calculate_balance_percentage(_current_balance, _max_amount), do: 0.0

  defp format_errors(error) do
    case error do
      %Ecto.Changeset{} = changeset ->
        Ecto.Changeset.traverse_errors(changeset, fn {msg, opts} ->
          Enum.reduce(opts, msg, fn {key, value}, acc ->
            String.replace(acc, "%{#{key}}", to_string(value))
          end)
        end)
        |> Enum.map(fn {field, errors} -> "#{field}: #{Enum.join(errors, ", ")}" end)
        |> Enum.join("; ")

      %Ash.Error.Invalid{errors: errors} ->
        errors
        |> Enum.map(fn e -> Map.get(e, :message, "Unknown error") end)
        |> Enum.join(", ")

      _ ->
        inspect(error)
    end
  end

  defp get_status_color(:active), do: "text-green-600"
  defp get_status_color(:inactive), do: "text-gray-600"
  defp get_status_color(:maintenance), do: "text-yellow-600"

  defp get_status_text(:active), do: "启用"
  defp get_status_text(:inactive), do: "禁用"
  defp get_status_text(:maintenance), do: "维护"

  defp get_health_color(true), do: "text-green-600"
  defp get_health_color(false), do: "text-red-600"

  defp get_health_text(true), do: "健康"
  defp get_health_text(false), do: "异常"

  defp format_amount(amount) when is_integer(amount) do
    # 将分转换为货币单位（除以100），然后格式化显示
    currency_amount = amount / 100

    # 格式化为带小数点的货币格式
    :erlang.float_to_binary(currency_amount, decimals: 2)
    |> String.replace(~r/(\d)(?=(\d{3})+\.)/, "\\1,")
  end

  defp format_amount(_), do: "0.00"

  # 根据底金自动计算最小和最大金额，并转换贡献率
  defp calculate_min_max_amounts(params) do
    # 首先过滤掉不被 update action 接受的字段
    filtered_params =
      params
      # 这些字段在更新时不被接受
      |> Map.drop(["game_id", "jackpot_id"])

    params_with_amounts =
      case Map.get(filtered_params, "base_amount") do
        nil ->
          filtered_params

        base_amount_str when is_binary(base_amount_str) ->
          case Integer.parse(base_amount_str) do
            {base_amount, _} when base_amount > 0 ->
              filtered_params
              |> Map.put("min_amount", div(base_amount, 20))
              |> Map.put("max_amount", base_amount * 20)

            _ ->
              filtered_params
          end

        base_amount when is_integer(base_amount) and base_amount > 0 ->
          filtered_params
          |> Map.put("min_amount", div(base_amount, 20))
          |> Map.put("max_amount", base_amount * 20)

        _ ->
          filtered_params
      end

    # 转换贡献率：从百分比形式转换为小数形式
    case Map.get(params_with_amounts, "contribution_rate") do
      nil ->
        params_with_amounts

      rate_str when is_binary(rate_str) ->
        case Float.parse(rate_str) do
          {rate, _} when rate >= 0 and rate <= 100 ->
            # 将百分比转换为小数（例如：2.0% -> 0.02）
            # 使用 Decimal 来确保精度
            decimal_rate = Decimal.div(Decimal.new(to_string(rate)), Decimal.new("100"))
            Map.put(params_with_amounts, "contribution_rate", Decimal.to_string(decimal_rate))

          _ ->
            params_with_amounts
        end

      _ ->
        params_with_amounts
    end
  end
end
