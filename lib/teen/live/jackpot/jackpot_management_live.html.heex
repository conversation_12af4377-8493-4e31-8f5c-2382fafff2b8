<div class="p-6">
  <div class="mb-6">
    <h1 class="text-3xl font-bold text-gray-900">奖池管理</h1>
    <p class="mt-2 text-gray-600">管理游戏奖池配置、底金设置和实时监控</p>
  </div>
  
<!-- 游戏筛选和操作按钮 -->
  <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
    <div class="flex flex-wrap items-center gap-2">
      <button
        phx-click="show_all_games"
        class={[
          "px-4 py-2 rounded-lg font-medium transition-colors",
          if(@selected_game == nil,
            do: "bg-blue-600 text-white",
            else: "bg-gray-200 text-gray-700 hover:bg-gray-300"
          )
        ]}
      >
        全部游戏
      </button>

      <%= for game_id <- @game_list do %>
        <button
          phx-click="select_game"
          phx-value-game={game_id}
          class={[
            "px-4 py-2 rounded-lg font-medium transition-colors",
            if(@selected_game == game_id,
              do: "bg-blue-600 text-white",
              else: "bg-gray-200 text-gray-700 hover:bg-gray-300"
            )
          ]}
        >
          {String.upcase(game_id)}
        </button>
      <% end %>
    </div>

    <button
      phx-click="new_config"
      class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
    >
      + 新建奖池配置
    </button>
  </div>
  
<!-- 奖池配置列表 -->
  <div class="space-y-6">
    <%= for {game_id, configs} <- @jackpot_configs do %>
      <%= if @selected_game == nil or @selected_game == game_id do %>
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
          <div class="bg-gray-50 px-6 py-4 border-b">
            <h2 class="text-xl font-semibold text-gray-900">
              {String.upcase(game_id)} 奖池
              <span class="ml-2 text-sm text-gray-500">({length(configs)} 个奖池)</span>
            </h2>
          </div>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    奖池信息
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    金额配置
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    实时余额
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <%= for config <- configs do %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div class="text-sm font-medium text-gray-900">{config.name}</div>
                        <div class="text-sm text-gray-500">ID: {config.jackpot_id}</div>
                        <%= if config.description do %>
                          <div class="text-xs text-gray-400 mt-1">{config.description}</div>
                        <% end %>
                      </div>
                    </td>

                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">
                        <div>底金: ₹{format_amount(config.base_amount)}</div>
                        <div class="text-xs text-gray-500">
                          范围: ₹{format_amount(config.min_amount)} - ₹{format_amount(
                            config.max_amount
                          )}
                        </div>
                        <div class="text-xs text-gray-500">
                          贡献率: {Float.round(Decimal.to_float(config.contribution_rate) * 100, 2)}%
                        </div>
                      </div>
                    </td>

                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm">
                        <div class="font-medium text-gray-900">
                          ₹{format_amount(config.real_time_balance)}
                        </div>
                        <%= if config.real_time_balance != config.current_balance do %>
                          <div class="text-xs text-orange-500">
                            (数据库: ₹{format_amount(config.current_balance)})
                          </div>
                        <% end %>
                      </div>
                    </td>

                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="space-y-1">
                        <span class={[
                          "inline-flex px-2 py-1 text-xs font-semibold rounded-full",
                          get_status_color(config.status)
                        ]}>
                          {get_status_text(config.status)}
                        </span>
                        <div class={["text-xs", get_health_color(config.is_healthy)]}>
                          {get_health_text(config.is_healthy)}
                        </div>
                      </div>
                    </td>

                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-y-1">
                      <div class="flex flex-col space-y-1">
                        <button
                          phx-click="edit_config"
                          phx-value-id={config.id}
                          class="text-blue-600 hover:text-blue-900"
                        >
                          编辑
                        </button>

                        <button
                          phx-click="initialize_jackpot"
                          phx-value-id={config.id}
                          phx-value-amount={config.base_amount}
                          class="text-green-600 hover:text-green-900"
                          data-confirm="确定要初始化奖池吗？"
                        >
                          初始化
                        </button>

                        <button
                          phx-click="set_contribution_rate"
                          phx-value-id={config.id}
                          class="text-purple-600 hover:text-purple-900"
                          title="设置贡献率"
                        >
                          设置贡献率
                        </button>

                        <button
                          phx-click="refresh_contribution_cache"
                          phx-value-game-id={config.game_id}
                          class="text-blue-600 hover:text-blue-900"
                          title="刷新游戏贡献率缓存"
                        >
                          刷新缓存
                        </button>

                        <button
                          phx-click="delete_config"
                          phx-value-id={config.id}
                          class="text-red-600 hover:text-red-900"
                          data-confirm="确定要删除这个奖池配置吗？"
                        >
                          删除
                        </button>
                      </div>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
  
<!-- 配置表单模态框 -->
  <%= if @show_form do %>
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {if @form_mode == :create, do: "新建奖池配置", else: "编辑奖池配置"}
          </h3>

          <form phx-submit="save_config" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">游戏ID</label>
                <input
                  type="text"
                  name="jackpot_config[game_id]"
                  value={if @editing_config, do: @editing_config.game_id, else: ""}
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">奖池ID</label>
                <input
                  type="text"
                  name="jackpot_config[jackpot_id]"
                  value={if @editing_config, do: @editing_config.jackpot_id, else: ""}
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">奖池名称</label>
                <input
                  type="text"
                  name="jackpot_config[name]"
                  value={if @editing_config, do: @editing_config.name, else: ""}
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">底金金额</label>
                <input
                  type="number"
                  name="jackpot_config[base_amount]"
                  value={if @editing_config, do: @editing_config.base_amount, else: ""}
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  min="0"
                  required
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">贡献率 (%)</label>
                <input
                  type="number"
                  name="jackpot_config[contribution_rate]"
                  value={
                    if @editing_config,
                      do: Decimal.to_float(@editing_config.contribution_rate) * 100,
                      else: "2"
                  }
                  step="0.01"
                  min="0"
                  max="100"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">状态</label>
                <select
                  name="jackpot_config[status]"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option
                    value="active"
                    selected={
                      if @editing_config, do: @editing_config.status == :active, else: true
                    }
                  >
                    启用
                  </option>
                  <option
                    value="inactive"
                    selected={
                      if @editing_config, do: @editing_config.status == :inactive, else: false
                    }
                  >
                    禁用
                  </option>
                  <option
                    value="maintenance"
                    selected={
                      if @editing_config, do: @editing_config.status == :maintenance, else: false
                    }
                  >
                    维护
                  </option>
                </select>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700">描述</label>
              <textarea
                name="jackpot_config[description]"
                rows="3"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              ><%= if @editing_config, do: @editing_config.description, else: "" %></textarea>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                phx-click="cancel_form"
                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                type="submit"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                {if @form_mode == :create, do: "创建", else: "更新"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  <% end %>

  <!-- 贡献率设置模态框 -->
  <%= if @show_contribution_form do %>
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            设置贡献率
          </h3>

          <%= if @editing_contribution_config do %>
            <div class="mb-4 p-3 bg-gray-50 rounded">
              <p class="text-sm text-gray-600">
                奖池：<span class="font-medium"><%= @editing_contribution_config.name %></span>
              </p>
              <p class="text-sm text-gray-600">
                当前贡献率：<span class="font-medium"><%= Float.round(Decimal.to_float(@editing_contribution_config.contribution_rate) * 100, 2) %>%</span>
              </p>
              <p class="text-xs text-gray-500 mt-1">
                ID: <%= @editing_contribution_config.id %>
              </p>
            </div>
          <% end %>

          <form phx-submit="update_contribution_rate" class="space-y-4">
            <%= if @editing_contribution_config do %>
              <input type="hidden" name="config_id" value={@editing_contribution_config.id} />
            <% end %>
            
            <div>
              <label class="block text-sm font-medium text-gray-700">新贡献率 (%)</label>
              <input
                type="number"
                name="contribution_rate"
                value={if @editing_contribution_config, do: Float.round(Decimal.to_float(@editing_contribution_config.contribution_rate) * 100, 2), else: "2"}
                step="0.01"
                min="0"
                max="100"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500"
                required
              />
              <p class="mt-1 text-sm text-gray-500">
                只会更新贡献率，不会影响当前奖池余额
              </p>
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                phx-click="cancel_contribution_form"
                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                type="submit"
                class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
              >
                更新贡献率
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  <% end %>
</div>
