<div class="p-2">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">系统日志查看器</h1>
    <p class="text-gray-600">查看和监控系统日志文件</p>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- 左侧文件列表 -->
    <div class="lg:col-span-1">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">日志文件</h2>
        </div>
        <div class="p-4">
          <div class="space-y-4 max-h-96 overflow-y-auto">
            <!-- 系统日志分类 -->
            <div>
              <h3 class="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                <.icon name="hero-server" class="w-4 h-4 mr-1" /> 系统日志
              </h3>
              <div class="space-y-2 ml-4">
                <%= for system_file <- @log_categories.system do %>
                  <div class={[
                    "group p-3 rounded-lg border transition-colors",
                    if(@selected_file == system_file.path,
                      do: "bg-blue-30 border-blue-200",
                      else: "bg-gray-30 border-gray-200 hover:bg-gray-100"
                    )
                  ]}>
                    <div
                      class="cursor-pointer"
                      phx-click="select_file"
                      phx-value-file={system_file.path}
                    >
                      <div
                        class="font-medium text-sm text-gray-900 truncate"
                        title={system_file.name}
                      >
                        {system_file.name}
                      </div>
                      <div class="text-xs text-gray-400 mt-1">
                        <div>{system_file.size}</div>
                        <div>{system_file.modified}</div>
                      </div>
                    </div>
                    <div class="mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button
                        phx-click="delete_file"
                        phx-value-file={system_file.path}
                        phx-confirm="确定要删除日志文件 #{system_file.name} 吗？此操作不可恢复。"
                        class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-30 border border-red-200 rounded hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400"
                      >
                        <.icon name="hero-trash" class="w-3 h-3 mr-1" /> 删除
                      </button>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
            
<!-- 游戏日志分类 -->
            <%= for {game_name, game_files} <- @log_categories.games do %>
              <div>
                <h3 class="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                  <.icon name="hero-puzzle-piece" class="w-4 h-4 mr-1" />
                  {game_name}
                </h3>
                <div class="space-y-2 ml-4">
                  <%= for game_file <- game_files do %>
                    <div class={[
                      "group p-3 rounded-lg border transition-colors",
                      if(@selected_file == game_file.path,
                        do: "bg-blue-30 border-blue-200",
                        else: "bg-gray-30 border-gray-200 hover:bg-gray-100"
                      )
                    ]}>
                      <div
                        class="cursor-pointer"
                        phx-click="select_file"
                        phx-value-file={game_file.path}
                      >
                        <div
                          class="font-medium text-sm text-gray-900 truncate"
                          title={game_file.name}
                        >
                          {game_file.name}
                        </div>
                        <div class="text-xs text-gray-400 mt-1">
                          <div>{game_file.size}</div>
                          <div>{game_file.modified}</div>
                        </div>
                      </div>
                      <div class="mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          phx-click="delete_file"
                          phx-value-file={game_file.path}
                          phx-confirm="确定要删除日志文件 #{game_file.name} 吗？此操作不可恢复。"
                          class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-30 border border-red-200 rounded hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-400"
                        >
                          <.icon name="hero-trash" class="w-3 h-3 mr-1" /> 删除
                        </button>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    
<!-- 右侧日志内容 -->
    <div class="lg:col-span-2">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <!-- 工具栏 -->
        <div class="p-4 border-b border-gray-200">
          <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center space-x-4">
              <h2 class="text-lg font-semibold text-gray-900">
                {if @selected_file, do: @selected_file, else: "请选择日志文件"}
              </h2>

              <%= if @selected_file do %>
                <button
                  phx-click="refresh"
                  class="inline-flex items-center px-3 py-1.3 border border-gray-400 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400"
                >
                  <.icon name="hero-arrow-path" class="w-4 h-4 mr-1" /> 刷新
                </button>

                <button
                  phx-click="download"
                  class="inline-flex items-center px-3 py-1.3 border border-gray-400 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400"
                >
                  <.icon name="hero-arrow-down-tray" class="w-4 h-4 mr-1" /> 下载
                </button>
              <% end %>
            </div>

            <div class="flex items-center space-x-4">
              <!-- 显示行数选择 -->
              <div class="flex items-center space-x-2">
                <label class="text-sm text-gray-700">显示行数:</label>
                <.form for={%{}} as={:lines_form} phx-change="change_lines">
                  <select
                    name="lines"
                    class="block w-20 px-2 py-1 text-sm border-gray-400 rounded-md shadow-sm focus:border-blue-400 focus:ring-blue-400"
                  >
                    <option value="30" selected={@lines_to_show == 30}>30</option>
                    <option value="100" selected={@lines_to_show == 100}>100</option>
                    <option value="200" selected={@lines_to_show == 200}>200</option>
                    <option value="400" selected={@lines_to_show == 400}>400</option>
                    <option value="1000" selected={@lines_to_show == 1000}>1000</option>
                  </select>
                </.form>
              </div>
              
<!-- 自动刷新开关 -->
              <div class="flex items-center space-x-2">
                <label class="text-sm text-gray-700">自动刷新:</label>
                <button
                  phx-click="toggle_auto_refresh"
                  class={[
                    "relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2",
                    if(@auto_refresh, do: "bg-blue-600", else: "bg-gray-200")
                  ]}
                >
                  <span class={[
                    "pointer-events-none inline-block h-3 w-3 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out",
                    if(@auto_refresh, do: "translate-x-3", else: "translate-x-0")
                  ]}>
                  </span>
                </button>
              </div>
            </div>
          </div>
          
<!-- 搜索框 -->
          <%= if @selected_file do %>
            <div class="mt-4">
              <.form
                for={%{}}
                as={:search}
                phx-change="search"
                class="flex items-center space-x-2"
              >
                <div class="flex-1">
                  <input
                    type="text"
                    name="term"
                    value={@search_term}
                    placeholder="搜索日志内容..."
                    class="block w-full px-3 py-2 border border-gray-400 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-400 focus:border-blue-400 sm:text-sm"
                  />
                </div>
              </.form>
            </div>
          <% end %>
        </div>
        
<!-- 日志内容区域 -->
        <div class="p-4 flex-1 overflow-hidden flex flex-col">
          <%= if @selected_file do %>
            <!-- 颜色图例 -->
            <div class="mb-3 p-3 bg-gray-30 rounded-lg border flex-shrink-0">
              <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium text-gray-700">日志颜色说明</h4>
                <button
                  phx-click="toggle_color_legend"
                  class="text-xs text-gray-400 hover:text-gray-700"
                >
                  {if @show_color_legend, do: "隐藏", else: "显示"}
                </button>
              </div>
              <%= if @show_color_legend do %>
                <div class="mt-2 grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                  <!-- 主要日志类型 -->
                  <div class="flex items-center space-x-2 p-3 rounded-lg bg-red-50 border border-red-200 shadow-sm hover:shadow-md transition-shadow">
                    <span class="w-4 h-4 bg-red-400 rounded-full shadow-sm border border-red-300"></span>
                    <span class="text-red-700 font-medium">错误(ERROR)</span>
                  </div>
                  <div class="flex items-center space-x-2 p-3 rounded-lg bg-yellow-50 border border-yellow-200 shadow-sm hover:shadow-md transition-shadow">
                    <span class="w-4 h-4 bg-yellow-400 rounded-full shadow-sm border border-yellow-300"></span>
                    <span class="text-yellow-700 font-medium">警告(WARN)</span>
                  </div>
                  <div class="flex items-center space-x-2 p-3 rounded-lg bg-blue-50 border border-blue-200 shadow-sm hover:shadow-md transition-shadow">
                    <span class="w-4 h-4 bg-blue-400 rounded-full shadow-sm border border-blue-300"></span>
                    <span class="text-blue-700 font-medium">信息(INFO)</span>
                  </div>
                  <div class="flex items-center space-x-2 p-3 rounded-lg bg-green-50 border border-green-200 shadow-sm hover:shadow-md transition-shadow">
                    <span class="w-4 h-4 bg-green-400 rounded-full shadow-sm border border-green-300"></span>
                    <span class="text-green-700 font-medium">成功(SUCCESS)</span>
                  </div>
                  <div class="flex items-center space-x-2 p-3 rounded-lg bg-purple-50 border border-purple-200 shadow-sm hover:shadow-md transition-shadow">
                    <span class="w-4 h-4 bg-purple-400 rounded-full shadow-sm border border-purple-300"></span>
                    <span class="text-purple-700 font-medium">调试(DEBUG)</span>
                  </div>
                </div>
              <% end %>
            </div>

            <div class="flex-1 overflow-hidden bg-gray-900 rounded-lg">
              <div class="h-full overflow-auto p-4 font-mono text-sm">
                <%= for {line, index} <- filter_content(@log_content, @search_term) |> String.split("\n") |> Enum.with_index() do %>
                  <div
                    class={["whitespace-pre-wrap break-words", get_log_line_color_class(line)]}
                    data-line={index}
                  >
                    {line}
                  </div>
                <% end %>
              </div>
            </div>
          <% else %>
            <div class="text-center py-12">
              <.icon name="hero-document-text" class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">未选择日志文件</h3>
              <p class="mt-1 text-sm text-gray-400">请从左侧列表中选择一个日志文件查看内容</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  window.addEventListener("phx:download_file", (e) => {
    const link = document.createElement("a");
    link.href = e.detail.url;
    link.download = e.detail.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  });
</script>
