defmodule Teen.Live.ProductTemplateLive do
  @moduledoc """
  商品模板管理页面

  提供商品模板的创建、查看、编辑和管理功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ShopSystem.ProductTemplate
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "商品模板"

  @impl Backpex.LiveResource
  def plural_name, do: "商品模板"

  @impl Backpex.LiveResource
  def mount(_params, _session, socket) do
    socket = assign(socket, :fluid?, true)
    {:ok, socket}
  end

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      :new -> true
      :edit -> true
      :delete -> true
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    %{
      template_name: %{
        module: Backpex.Fields.Text,
        label: "模板名称",
        searchable: true,
        orderable: true
      },
      product_type: %{
        module: Backpex.Fields.Select,
        label: "商品类型",
        options: [
          {"月卡", :monthly_card},
          {"周卡", :weekly_card},
          {"次卡", :play_card},
          {"金币礼包", :coin_package},
          {"VIP礼包", :vip_package},
          {"特殊道具", :special_item},
          {"充值奖励包", :recharge_bonus}
        ],
        searchable: true,
        orderable: true
      },
      is_default: %{
        module: Backpex.Fields.Boolean,
        label: "默认模板",
        orderable: true
      },
      default_config: %{
        module: Backpex.Fields.Textarea,
        label: "默认配置 (JSON)",
        only: [:show, :edit, :new],
        render: fn assigns ->
          config_json = Jason.encode!(assigns.value, pretty: true)
          assigns = assign(assigns, :config_json, config_json)

          ~H"""
          <pre class="bg-gray-100 p-2 rounded text-sm overflow-auto max-h-60"><%= @config_json %></pre>
          """
        end
      },
      config_schema: %{
        module: Backpex.Fields.Textarea,
        label: "配置说明 (JSON)",
        only: [:show, :edit, :new],
        render: fn assigns ->
          if assigns.value && assigns.value != %{} do
            schema_json = Jason.encode!(assigns.value, pretty: true)
            assigns = assign(assigns, :schema_json, schema_json)

            ~H"""
            <pre class="bg-blue-50 p-2 rounded text-sm overflow-auto max-h-40"><%= @schema_json %></pre>
            """
          else
            ~H"""
            <span class="text-gray-500">无配置说明</span>
            """
          end
        end
      },
      inserted_at: %{
        module: Backpex.Fields.DateTime,
        label: "创建时间",
        readonly: true,
        only: [:index, :show],
        orderable: true
      },
      updated_at: %{
        module: Backpex.Fields.DateTime,
        label: "更新时间",
        readonly: true,
        only: [:show],
        orderable: true
      }
    }
  end

  @impl Backpex.LiveResource
  def filters do
    [
      product_type: %{
        module: Teen.Filters.ProductTypeSelect
      },
      is_default: %{
        module: Teen.Filters.BooleanSelect
      }
    ]
  end

  @impl Backpex.LiveResource
  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      }
    ]
  end

  # @impl Backpex.LiveResource
  # def metrics do
  #   [
  #     total_templates: %{
  #       module: Backpex.Metrics.Simple,
  #       label: "模板总数",
  #       value: fn _socket ->
  #         case Teen.ShopSystem.ProductTemplate.read() do
  #           {:ok, templates} -> length(templates)
  #           {:error, _} -> 0
  #         end
  #       end
  #     }
  #   ]
  # end
end
