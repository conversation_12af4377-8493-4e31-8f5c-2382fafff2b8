<div id="subordinate-management-container" class="h-full flex flex-col" phx-hook="BackpexDynamicLayout">
  <!-- 页面头部 -->
  <div class="flex-shrink-0 flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold">下线管理</h1>
      <%= if @pending_refund_requests_count > 0 do %>
        <div class="badge badge-error badge-lg mt-2">
          {@pending_refund_requests_count} 个待处理退款申请
        </div>
      <% end %>
    </div>
    <button phx-click="show_create_modal" class="btn btn-primary">
      <.icon name="hero-plus" class="w-4 h-4 mr-2" /> 创建下线
    </button>
  </div>

  <!-- 搜索区域 -->
  <div class="flex-shrink-0 card bg-base-100 shadow-xl mb-6">
    <div class="card-body">
      <form phx-submit="search" class="form-control">
        <label class="label">
          <span class="label-text">搜索下线</span>
        </label>
        <div class="input-group">
          <input
            type="text"
            name="search[query]"
            value={@search_query}
            placeholder="输入用户名搜索..."
            class="input input-bordered flex-1"
          />
          <button type="submit" class="btn btn-square btn-primary">
            <.icon name="hero-magnifying-glass" class="w-4 h-4" />
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- 下线列表 -->
  <div class="flex-1 card bg-base-100 shadow-xl flex flex-col min-h-0">
    <div class="card-body flex-1 flex flex-col min-h-0">
      <%= if @subordinates_data && length(@subordinates_data) > 0 do %>
        <div class="flex-1 overflow-auto min-h-0">
          <table class="table table-zebra w-full">
            <thead class="sticky top-0 bg-base-100 z-10">
              <tr>
                <th>用户名</th>
                <th>身份</th>
                <th>积分余额</th>
                <th>抽水比例</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <%= for subordinate <- @subordinates_data do %>
                <tr>
                  <td class="font-medium">{to_string(subordinate.username || "N/A")}</td>
                  <td>
                    <%= if subordinate.is_agent do %>
                      <span class="badge badge-info">代理L{subordinate.agent_level}</span>
                    <% else %>
                      <span class="badge badge-ghost">普通用户</span>
                    <% end %>
                  </td>
                  <td class="font-medium text-primary">{subordinate.points} 积分</td>
                  <td class="font-medium">
                    {:erlang.float_to_binary(subordinate.commission_rate || 0.0, decimals: 2)}%
                  </td>
                  <td>
                    <div class="flex flex-wrap gap-1">
                      <button
                        phx-click="show_reset_password_modal"
                        phx-value-user_id={to_string(subordinate.id)}
                        class="btn btn-ghost btn-xs"
                      >
                        重置密码
                      </button>
                      <button
                        phx-click="show_transfer_modal"
                        phx-value-user_id={to_string(subordinate.id)}
                        class="btn btn-ghost btn-xs"
                      >
                        转账
                      </button>
                      <button
                        phx-click="show_commission_modal"
                        phx-value-user_id={to_string(subordinate.id)}
                        class="btn btn-ghost btn-xs"
                      >
                        调整抽水
                      </button>
                      <.live_component
                        module={PointsHistoryComponent}
                        id={"points_history_#{subordinate.id}"}
                        user_id={subordinate.id}
                        current_user={@current_user}
                        show_admin_actions={true}
                        user_info={
                          %{
                            username: subordinate.username,
                            numeric_id: subordinate.id,
                            current_points: subordinate.points
                          }
                        }
                      />
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <%= if @page_info && @page_info.total_count > @page_info.per_page do %>
          <div class="flex-shrink-0 flex justify-center mt-6 pt-4 border-t border-base-300">
            <div class="join">
              <%= for page_num <- 1..ceil(@page_info.total_count / @page_info.per_page) do %>
                <button
                  phx-click="page_change"
                  phx-value-page={page_num}
                  class={[
                    "join-item btn",
                    if(@page_info.page == page_num, do: "btn-active", else: "")
                  ]}
                >
                  {page_num}
                </button>
              <% end %>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="flex-1 flex items-center justify-center">
          <div class="text-center">
            <div class="text-4xl mb-4">👥</div>
            <p class="text-lg text-base-content/70">暂无下线数据</p>
            <p class="text-sm text-base-content/50">尝试调整搜索条件或创建新下线</p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- 创建下线模态框 -->
<%= if @show_create_modal do %>
  <div class="modal modal-open">
    <div class="modal-backdrop" phx-click="hide_create_modal"></div>
    <div class="modal-box max-w-2xl">
      <h3 class="font-bold text-lg mb-4">创建新下线</h3>
      <form phx-submit="create_subordinate">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">用户名</span>
            </label>
            <input
              type="text"
              name="subordinate[username]"
              value={@create_form["username"] || ""}
              class="input input-bordered"
              required
              minlength="3"
              maxlength="20"
              placeholder="3-20个字符"
            />
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">密码</span>
            </label>
            <input
              type="password"
              name="subordinate[password]"
              value={@create_form["password"] || ""}
              class="input input-bordered"
              required
              minlength="6"
              placeholder="至少6个字符"
            />
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">用户类型</span>
            </label>
            <select name="subordinate[user_type]" class="select select-bordered">
              <option value="normal" selected={(@create_form["user_type"] || "normal") == "normal"}>普通用户</option>
              <option value="agent" selected={@create_form["user_type"] == "agent"}>代理用户</option>
            </select>
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">抽水比例 (%)</span>
            </label>
            <input
              type="number"
              step="0.01"
              name="subordinate[commission_rate]"
              class="input input-bordered"
              value={@create_form["commission_rate"] || "0.0"}
              min="0"
              max="100"
            />
          </div>
          <div class="form-control md:col-span-2">
            <label class="label">
              <span class="label-text">初始积分</span>
            </label>
            <input
              type="number"
              name="subordinate[initial_points]"
              class="input input-bordered"
              value={@create_form["initial_points"] || "0"}
              min="0"
              step="1"
            />
          </div>
        </div>
        <div class="modal-action">
          <button type="button" phx-click="hide_create_modal" class="btn btn-ghost">
            取消
          </button>
          <button type="submit" class="btn btn-primary">
            创建下线
          </button>
        </div>
      </form>
    </div>
  </div>
<% end %>

<!-- 重置密码模态框 -->
<%= if @show_reset_password_modal && @selected_subordinate do %>
  <div class="modal modal-open">
    <div class="modal-backdrop" phx-click="hide_reset_password_modal"></div>
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-4">重置密码: {to_string(@selected_subordinate.username)}</h3>
      <form phx-submit="reset_password">
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">新密码</span>
          </label>
          <input
            type="password"
            name="password[new_password]"
            value={@reset_password_form["new_password"] || ""}
            class="input input-bordered"
            required
            minlength="6"
            placeholder="至少6个字符"
          />
        </div>
        <div class="form-control mb-6">
          <label class="label">
            <span class="label-text">确认新密码</span>
          </label>
          <input
            type="password"
            name="password[confirm_password]"
            value={@reset_password_form["confirm_password"] || ""}
            class="input input-bordered"
            required
            minlength="6"
            placeholder="再次输入新密码"
          />
        </div>
        <div class="modal-action">
          <button type="button" phx-click="hide_reset_password_modal" class="btn btn-ghost">
            取消
          </button>
          <button type="submit" class="btn btn-primary">
            重置密码
          </button>
        </div>
      </form>
    </div>
  </div>
<% end %>

<!-- 转账模态框 -->
<%= if @show_transfer_modal && @selected_subordinate do %>
  <div class="modal modal-open">
    <div class="modal-backdrop" phx-click="hide_transfer_modal"></div>
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-4">转账: {to_string(@selected_subordinate.username)}</h3>
      <form phx-submit="submit_transfer">
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">转账类型</span>
          </label>
          <select name="transfer[transfer_type]" class="select select-bordered">
            <option value="to_subordinate" selected={(@transfer_form["transfer_type"] || "to_subordinate") == "to_subordinate"}>转给下线</option>
            <option value="from_subordinate" selected={@transfer_form["transfer_type"] == "from_subordinate"}>从下线转入</option>
          </select>
        </div>
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">转账金额</span>
          </label>
          <input
            type="number"
            name="transfer[amount]"
            value={@transfer_form["amount"] || ""}
            class="input input-bordered"
            placeholder="请输入转账金额"
            min="1"
            step="1"
            required
          />
        </div>
        <div class="form-control mb-6">
          <label class="label">
            <span class="label-text">转账原因</span>
          </label>
          <textarea
            name="transfer[reason]"
            class="textarea textarea-bordered"
            placeholder="请说明转账原因"
            required
          >{@transfer_form["reason"] || ""}</textarea>
        </div>
        <div class="modal-action">
          <button type="button" phx-click="hide_transfer_modal" class="btn btn-ghost">
            取消
          </button>
          <button type="submit" class="btn btn-primary">
            确认转账
          </button>
        </div>
      </form>
    </div>
  </div>
<% end %>

<!-- 调整抽水模态框 -->
<%= if @show_commission_modal && @selected_subordinate do %>
  <div class="modal modal-open">
    <div class="modal-backdrop" phx-click="hide_commission_modal"></div>
    <div class="modal-box">
      <h3 class="font-bold text-lg mb-4">调整抽水: {to_string(@selected_subordinate.username)}</h3>
      <form phx-submit="update_commission">
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">抽水比例 (%)</span>
          </label>
          <input
            type="number"
            step="0.01"
            name="commission[commission_rate]"
            class="input input-bordered"
            value={@commission_form["commission_rate"] || ""}
            min="0"
            max="100"
            placeholder="0.00 - 100.00"
            required
          />
        </div>
        <div class="form-control mb-6">
          <label class="label">
            <span class="label-text">调整原因</span>
          </label>
          <textarea
            name="commission[reason]"
            class="textarea textarea-bordered"
            placeholder="请说明调整原因"
            required
          >{@commission_form["reason"] || ""}</textarea>
        </div>
        <div class="modal-action">
          <button type="button" phx-click="hide_commission_modal" class="btn btn-ghost">
            取消
          </button>
          <button type="submit" class="btn btn-primary">
            确认调整
          </button>
        </div>
      </form>
    </div>
  </div>
<% end %>
