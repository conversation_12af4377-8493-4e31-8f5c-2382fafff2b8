<div class="p-6">
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">系统配置</h1>
    <p class="text-gray-600">管理系统运行时配置参数</p>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
    <!-- 左侧配置分类 -->
    <div class="lg:col-span-1">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">配置分类</h2>
        </div>
        <div class="p-2">
          <nav class="space-y-1">
            <%= for {section_key, section} <- @config_sections do %>
              <button
                phx-click="switch_tab"
                phx-value-tab={section_key}
                class={[
                  "w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                  if(@current_tab == section_key,
                    do: "bg-blue-100 text-blue-700 border-blue-200",
                    else: "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  )
                ]}
              >
                <.icon name={section.icon} class="w-5 h-5 mr-3" />
                <%= section.name %>
              </button>
            <% end %>
          </nav>
        </div>
      </div>
    </div>

    <!-- 右侧配置内容 -->
    <div class="lg:col-span-3">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <%= if @config_sections[@current_tab] do %>
          <% current_section = @config_sections[@current_tab] %>
          <div class="p-4 border-b border-gray-200">
            <div class="flex items-center">
              <.icon name={current_section.icon} class="w-6 h-6 mr-3 text-gray-600" />
              <h2 class="text-lg font-semibold text-gray-900"><%= current_section.name %></h2>
            </div>
          </div>

          <div class="p-4">
            <div class="space-y-4">
              <%= for config <- current_section.configs do %>
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <div class="flex-1">
                    <div class="flex items-center justify-between">
                      <h3 class="text-sm font-medium text-gray-900"><%= config.name %></h3>
                      <div class="flex items-center space-x-3">
                        <span class={[
                          "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                          case config.type do
                            :boolean when config.value == true -> "bg-green-100 text-green-800"
                            :boolean when config.value == false -> "bg-red-100 text-red-800"
                            _ -> "bg-gray-100 text-gray-800"
                          end
                        ]}>
                          <%= format_config_value(config.value, config.type) %>
                        </span>
                        <button
                          phx-click="edit_config"
                          phx-value-section={@current_tab}
                          phx-value-key={config.key}
                          class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          编辑
                        </button>
                      </div>
                    </div>
                    <p class="text-sm text-gray-500 mt-1"><%= config.description %></p>
                    <div class="flex items-center mt-2 text-xs text-gray-400">
                      <span class="mr-4">键名: <%= config.key %></span>
                      <span>类型: <%= config.type %></span>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- 编辑配置模态框 -->
<%= if @show_edit_modal and @editing_config do %>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" phx-click="close_modal"></div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form phx-submit="save_config">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                  编辑配置
                </h3>
                
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">配置名称</label>
                    <p class="text-sm text-gray-500"><%= get_config_name(@editing_config.section, @editing_config.key) %></p>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700">配置键名</label>
                    <p class="text-sm text-gray-500"><%= @editing_config.key %></p>
                  </div>

                  <div>
                    <label for="config_value" class="block text-sm font-medium text-gray-700">配置值</label>
                    <%= case get_config_type(@editing_config.section, @editing_config.key) do %>
                      <% :boolean -> %>
                        <select name="config[value]" id="config_value" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                          <option value="true" selected={@editing_config.value == true}>启用</option>
                          <option value="false" selected={@editing_config.value == false}>禁用</option>
                        </select>
                      <% :integer -> %>
                        <input
                          type="number"
                          name="config[value]"
                          id="config_value"
                          value={@editing_config.value}
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                      <% _ -> %>
                        <input
                          type="text"
                          name="config[value]"
                          id="config_value"
                          value={@editing_config.value}
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                    <% end %>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700">说明</label>
                    <p class="text-sm text-gray-500"><%= get_config_description(@editing_config.section, @editing_config.key) %></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              保存
            </button>
            <button
              type="button"
              phx-click="close_modal"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
<% end %>
