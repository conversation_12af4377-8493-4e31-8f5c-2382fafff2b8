defmodule Teen.Live.UserPurchaseLive do
  @moduledoc """
  用户购买记录管理页面

  提供用户购买记录的查看、管理和发放功能
  """

  use Backpex.LiveResource,
    adapter: Backpex.Adapters.Ash,
    adapter_config: [
      resource: Teen.ShopSystem.UserPurchase
    ],
    layout: {Teen.Layouts, :admin},
    fluid?: true

  @impl Backpex.LiveResource
  def singular_name, do: "购买记录"

  @impl Backpex.LiveResource
  def plural_name, do: "购买记录"

  @impl Backpex.LiveResource
  def can?(assigns, action, item) do
    case action do
      :index -> true
      :show -> true
      # 购买记录不允许手动创建
      :new -> false
      # 允许编辑状态等
      :edit -> true
      # 不允许删除购买记录
      :delete -> false
      _ -> false
    end
  end

  @impl Backpex.LiveResource
  def fields do
    [
      user_id: %{
        module: Backpex.Fields.Text,
        label: "用户ID",
        searchable: true,
        readonly: true
      },
      product_name: %{
        module: Backpex.Fields.Text,
        label: "商品名称",
        searchable: true,
        readonly: true
      },
      product_type: %{
        module: Backpex.Fields.Select,
        label: "商品类型",
        options: [
          {"月卡", :monthly_card},
          {"周卡", :weekly_card},
          {"次卡", :play_card},
          {"金币礼包", :coin_package},
          {"VIP礼包", :vip_package},
          {"特殊道具", :special_item},
          {"充值奖励包", :recharge_bonus}
        ],
        only: [:index, :show],
        searchable: true
      },
      original_price: %{
        module: Backpex.Fields.Number,
        label: "原价（分）",
        readonly: true
      },
      paid_amount: %{
        module: Backpex.Fields.Number,
        label: "实付金额（分）",
        readonly: true,
        orderable: true
      },
      currency: %{
        module: Backpex.Fields.Select,
        label: "货币",
        options: [
          {"印度卢比", :inr},
          {"美元", :usd},
          {"人民币", :cny}
        ],
        only: [:index, :show]
      },
      payment_status: %{
        module: Backpex.Fields.Select,
        label: "支付状态",
        options: [
          {"待支付", :pending},
          {"已支付", :completed},
          {"支付失败", :failed},
          {"已退款", :refunded}
        ],
        searchable: true,
        orderable: true
      },
      delivery_status: %{
        module: Backpex.Fields.Select,
        label: "发放状态",
        options: [
          {"待发放", :pending},
          {"已发放", :delivered},
          {"发放失败", :failed},
          {"已取消", :cancelled}
        ],
        searchable: true,
        orderable: true
      },
      status_display: %{
        module: Backpex.Fields.Text,
        label: "状态显示",
        readonly: true,
        only: [:index, :show]
      },
      payment_order_id: %{
        module: Backpex.Fields.Text,
        label: "支付订单ID",
        searchable: true,
        readonly: true,
        only: [:show]
      },
      transaction_id: %{
        module: Backpex.Fields.Text,
        label: "交易ID",
        searchable: true,
        readonly: true,
        only: [:show]
      },
      discount_amount: %{
        module: Backpex.Fields.Number,
        label: "优惠金额（分）",
        readonly: true,
        only: [:show]
      },
      discount_percentage: %{
        module: Backpex.Fields.Number,
        label: "优惠百分比",
        readonly: true,
        only: [:show]
      },
      product_config: %{
        module: Backpex.Fields.Textarea,
        label: "商品配置",
        readonly: true,
        only: [:show],
        render: fn assigns ->
          config_json = Jason.encode!(assigns.value, pretty: true)
          assigns = assign(assigns, :config_json, config_json)

          ~H"""
          <pre class="bg-gray-100 p-2 rounded text-sm overflow-auto max-h-40"><%= @config_json %></pre>
          """
        end
      },
      delivery_data: %{
        module: Backpex.Fields.Textarea,
        label: "发放数据",
        readonly: true,
        only: [:show],
        render: fn assigns ->
          if assigns.value && assigns.value != %{} do
            data_json = Jason.encode!(assigns.value, pretty: true)
            assigns = assign(assigns, :data_json, data_json)

            ~H"""
            <pre class="bg-green-50 p-2 rounded text-sm overflow-auto max-h-40"><%= @data_json %></pre>
            """
          else
            ~H"""
            <span class="text-gray-500">暂无发放数据</span>
            """
          end
        end
      },
      refund_reason: %{
        module: Backpex.Fields.Textarea,
        label: "退款原因",
        only: [:show, :edit]
      },
      purchased_at: %{
        module: Backpex.Fields.DateTime,
        label: "购买时间",
        readonly: true,
        orderable: true
      },
      paid_at: %{
        module: Backpex.Fields.DateTime,
        label: "支付时间",
        readonly: true,
        only: [:show],
        orderable: true
      },
      delivered_at: %{
        module: Backpex.Fields.DateTime,
        label: "发放时间",
        readonly: true,
        only: [:show],
        orderable: true
      },
      refunded_at: %{
        module: Backpex.Fields.DateTime,
        label: "退款时间",
        readonly: true,
        only: [:show],
        orderable: true
      }
    ]
  end

  @impl Backpex.LiveResource
  def filters do
    [
      payment_status: %{
        module: Teen.Filters.PurchasePaymentStatusSelect
      },
      delivery_status: %{
        module: Teen.Filters.DeliveryStatusSelect
      },
      product_type: %{
        module: Teen.Filters.ProductTypeSelect
      },
      currency: %{
        module: Teen.Filters.CurrencySelect
      }
    ]
  end

  @impl Backpex.LiveResource
  def item_actions(_) do
    [
      show: %{
        module: Backpex.ItemActions.Show,
        only: [:row]
      },
      edit: %{
        module: Backpex.ItemActions.Edit,
        only: [:row, :show]
      },
      delete: %{
        module: Backpex.ItemActions.Delete,
        only: [:row, :index, :show]
      }
    ]
  end

  # @impl Backpex.LiveResource
  # def metrics do
  #   [
  #     total_orders: %{
  #       module: Backpex.Metrics.Simple,
  #       label: "订单总数",
  #       value: fn _socket ->
  #         case Teen.ShopSystem.UserPurchase.read() do
  #           {:ok, purchases} -> length(purchases)
  #           {:error, _} -> 0
  #         end
  #       end
  #     },
  #     pending_deliveries: %{
  #       module: Backpex.Metrics.Simple,
  #       label: "待发放订单",
  #       value: fn _socket ->
  #         # 这里需要实现查询待发放订单的逻辑
  #         0
  #       end
  #     }
  #   ]
  # end
end
