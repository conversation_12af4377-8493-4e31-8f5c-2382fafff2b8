defmodule Teen.Protocol.BaseInfoProtocol do
  @moduledoc """
  基本信息协议处理器

  处理用户基本信息相关的协议，包括：
  - 获取用户信息
  - 更新用户资料
  - 上传头像
  - 获取系统配置
  - 用户设置管理
  """

  @behaviour Teen.Protocol.ProtocolBehaviour

  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias Cy<PERSON>ridina.Accounts.{User, UserProfile}
  alias Teen.PaymentSystem

  # 主协议ID
  @protocol_id 6

  # 子协议常量定义 - 根据Protocol.ts定义
  @cs_set_nickname_p 0
  @sc_set_nickname_result_p 1
  @sc_set_nickname_p 2
  @cs_set_headid_p 3
  @cs_set_custom_head_p 4
  @cs_change_psw_p 5
  @sc_change_psw_result_p 6
  @sc_set_lottery_p 7
  @cs_change_psw_check_p 8
  @sc_change_psw_check_p 9
  @cs_set_specphone_p 10
  @sc_set_specphone_p 11
  @cs_friend_p 12
  @sc_friend_p 13
  @sc_set_robot_level_p 14
  @sc_change_lottery_p 15
  @cd_set_sex_p 16
  @dc_set_sex_p 17

  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================

  @impl true
  def protocol_id, do: @protocol_id

  @impl true
  def protocol_info do
    %{
      name: "BaseInfo",
      description: "基本信息协议处理器"
    }
  end

  @impl true
  def supported_sub_protocols do
    [
      {@cs_set_nickname_p, "设置昵称"},
      {@cs_set_headid_p, "设置头像ID"},
      {@cs_set_custom_head_p, "设置自定义头像"},
      {@cs_change_psw_p, "修改密码"},
      {@cs_change_psw_check_p, "验证修改后密码有效性"},
      {@cs_set_specphone_p, "设置特殊手机号"},
      {@cs_friend_p, "朋友圈"},
      {@cd_set_sex_p, "设置性别"}
    ]
  end

  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id

    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "BASE_INFO", sub_protocol, user_id, "开始处理")

    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "BASE_INFO",
          sub_protocol,
          user_id,
          "数据验证失败: #{reason}"
        )

        {:error, reason}
    end
  end

  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_set_nickname_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:nickname]},
          {:string_length, :nickname, 1, 20, "昵称"}
        ])

      @cs_set_headid_p ->
        # 验证必需参数
        with :ok <- ProtocolUtils.validate_params(data, [
               {:required, [:id]},
               {:number_range, :id, 0, 999, "头像ID"}
             ]) do
          # 验证可选的frameid参数
          case Map.get(data, "frameid") do
            nil -> :ok
            frameid when is_number(frameid) and frameid >= 0 and frameid <= 999 -> :ok
            _invalid -> {:error, "头像框ID必须是0-999之间的数字"}
          end
        end

      @cs_set_custom_head_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:headdata]},
          {:string_length, :headdata, 1, 500_000, "头像数据"}
        ])

      @cs_change_psw_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:oldpsw, :newpsw]},
          {:string_length, :oldpsw, 6, 20, "旧密码"},
          {:string_length, :newpsw, 6, 20, "新密码"}
        ])

      @cs_change_psw_check_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:psw]},
          {:string_length, :psw, 6, 20, "密码"}
        ])

      @cs_set_specphone_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:phone]},
          {:string_length, :phone, 10, 15, "手机号"}
        ])

      @cd_set_sex_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:sex]},
          {:number_range, :sex, 0, 2, "性别"}
        ])

      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end

  # ============================================================================
  # 私有函数
  # ============================================================================

  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    user_id = context.user_id

    case sub_protocol do
      @cs_set_nickname_p ->
        handle_set_nickname(user_id, data)

      @cs_set_headid_p ->
        handle_set_headid(user_id, data)

      @cs_set_custom_head_p ->
        handle_set_custom_head(user_id, data)

      @cs_change_psw_p ->
        handle_change_password(user_id, data)

      @cs_change_psw_check_p ->
        handle_check_password(user_id, data)

      @cs_set_specphone_p ->
        handle_set_specphone(user_id, data)

      @cs_friend_p ->
        handle_friend_request(user_id, data)

      @cd_set_sex_p ->
        handle_set_sex(user_id, data)

      _ ->
        ProtocolUtils.log_protocol(:warning, "BASE_INFO", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end

  # 处理设置昵称
  defp handle_set_nickname(user_id, data) do
    nickname = Map.get(data, "nickname")

    case Cypridina.Accounts.update_user_nickname(user_id, nickname) do
      {:ok, user} ->
        # 返回设置昵称结果
        response_data = %{
          "code" => 0,
          "nickname" => nickname
        }

        ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_set_nickname_p, user_id, "昵称设置成功")

        # 先返回操作结果
        # Task.start(fn ->
        #   # 然后广播昵称变更通知
        #   Teen.Protocol.ProtocolSender.send_to_user(user_id, @protocol_id, @sc_set_nickname_p, response_data)
        # end)

        {:ok, @sc_set_nickname_p, response_data}

      {:error, :nickname_taken} ->
        error_data = %{
          "code" => 1,
          "msg" => "昵称已被使用"
        }

        {:ok, @sc_set_nickname_result_p, error_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "BASE_INFO",
          @cs_set_nickname_p,
          user_id,
          "设置昵称失败: #{inspect(reason)}"
        )

        error_data = %{
          "code" => -1,
          "msg" => "设置昵称失败"
        }

        {:ok, @sc_set_nickname_result_p, error_data}
    end
  end

  # 处理设置头像ID
  defp handle_set_headid(user_id, data) do
    headid = Map.get(data, "id")
    frameid = Map.get(data, "frameid")

    # 记录接收到的参数
    ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_set_headid_p, user_id, "设置头像ID: headid=#{headid}, frameid=#{frameid}")

    case Cypridina.Accounts.update_user_avatar_id(user_id, headid) do
      {:ok, _user} ->
        # Protocol.ts中CS_SET_HEADID_P没有定义对应的SC响应协议
        # 返回成功后客户端应该会重新获取用户信息
        ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_set_headid_p, user_id, "头像ID设置成功")
        {:noreply}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "BASE_INFO",
          @cs_set_headid_p,
          user_id,
          "设置头像ID失败: #{inspect(reason)}"
        )

        {:noreply}
    end
  end

  # 处理设置自定义头像
  defp handle_set_custom_head(user_id, data) do
    headdata = Map.get(data, "headdata")

    case Cypridina.Accounts.upload_custom_avatar(user_id, headdata) do
      {:ok, _avatar_url} ->
        # Protocol.ts中CS_SET_CUSTOM_HEAD_P没有定义对应的SC响应协议
        ProtocolUtils.log_protocol(
          :info,
          "BASE_INFO",
          @cs_set_custom_head_p,
          user_id,
          "自定义头像上传成功"
        )

        {:noreply}

      {:error, :invalid_format} ->
        ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_set_custom_head_p, user_id, "头像格式不支持")
        {:noreply}

      {:error, :file_too_large} ->
        ProtocolUtils.log_protocol(:error, "BASE_INFO", @cs_set_custom_head_p, user_id, "头像文件过大")
        {:noreply}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "BASE_INFO",
          @cs_set_custom_head_p,
          user_id,
          "自定义头像上传失败: #{inspect(reason)}"
        )

        {:noreply}
    end
  end

  # 处理修改密码
  defp handle_change_password(user_id, data) do
    old_password = Map.get(data, "oldpsw")
    new_password = Map.get(data, "newpsw")

    case Cypridina.Accounts.change_user_password(user_id, old_password, new_password) do
      {:ok, _user} ->
        response_data = %{
          "code" => 0,
          "msg" => "密码修改成功"
        }

        ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_change_psw_p, user_id, "密码修改成功")
        {:ok, @sc_change_psw_result_p, response_data}

      {:error, :invalid_password} ->
        error_data = %{
          "code" => 1,
          "msg" => "旧密码错误"
        }

        {:ok, @sc_change_psw_result_p, error_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "BASE_INFO",
          @cs_change_psw_p,
          user_id,
          "修改密码失败: #{inspect(reason)}"
        )

        error_data = %{
          "code" => -1,
          "msg" => "修改密码失败"
        }

        {:ok, @sc_change_psw_result_p, error_data}
    end
  end

  # 处理验证修改后密码有效性
  defp handle_check_password(user_id, data) do
    password = Map.get(data, "psw")

    case Cypridina.Accounts.verify_user_password(user_id, password) do
      {:ok, true} ->
        response_data = %{
          "valid" => 1,
          "msg" => "密码验证成功"
        }

        ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_change_psw_check_p, user_id, "密码验证成功")
        {:ok, @sc_change_psw_check_p, response_data}

      {:ok, false} ->
        response_data = %{
          "valid" => 0,
          "msg" => "密码验证失败"
        }

        {:ok, @sc_change_psw_check_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "BASE_INFO",
          @cs_change_psw_check_p,
          user_id,
          "验证密码失败: #{inspect(reason)}"
        )

        response_data = %{
          "valid" => 0,
          "msg" => "验证失败"
        }

        {:ok, @sc_change_psw_check_p, response_data}
    end
  end

  # 处理设置特殊手机号
  defp handle_set_specphone(user_id, data) do
    phone = Map.get(data, "phone")

    case Cypridina.Accounts.set_special_phone(user_id, phone) do
      {:ok, _user} ->
        response_data = %{
          "phone" => phone,
          "msg" => "特殊手机号设置成功"
        }

        ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_set_specphone_p, user_id, "特殊手机号设置成功")
        {:ok, @sc_set_specphone_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "BASE_INFO",
          @cs_set_specphone_p,
          user_id,
          "设置特殊手机号失败: #{inspect(reason)}"
        )

        error_data = %{
          "phone" => "",
          "msg" => "设置失败"
        }

        {:ok, @sc_set_specphone_p, error_data}
    end
  end

  # 处理朋友圈请求
  defp handle_friend_request(user_id, data) do
    # Protocol.ts中定义了CS_FRIEND_P但没有具体说明数据格式
    # 这里先返回空的朋友圈数据
    response_data = %{
      "friends" => [],
      "msg" => "朋友圈功能暂未开放"
    }

    ProtocolUtils.log_protocol(:info, "BASE_INFO", @cs_friend_p, user_id, "请求朋友圈数据")
    {:ok, @sc_friend_p, response_data}
  end

  # 处理设置性别
  defp handle_set_sex(user_id, data) do
    sex = Map.get(data, "sex")

    case Cypridina.Accounts.update_user_gender(user_id, sex) do
      {:ok, _user} ->
        response_data = %{
          "sex" => sex,
          "msg" => "性别设置成功"
        }

        ProtocolUtils.log_protocol(:info, "BASE_INFO", @cd_set_sex_p, user_id, "性别设置成功")
        {:ok, @dc_set_sex_p, response_data}

      {:error, reason} ->
        ProtocolUtils.log_protocol(
          :error,
          "BASE_INFO",
          @cd_set_sex_p,
          user_id,
          "设置性别失败: #{inspect(reason)}"
        )

        error_data = %{
          "sex" => 0,
          "msg" => "设置失败"
        }

        {:ok, @dc_set_sex_p, error_data}
    end
  end
end
