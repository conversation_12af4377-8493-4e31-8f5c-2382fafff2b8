defmodule Teen.Protocol.DBServerProtocol do
  @moduledoc """
  数据库服务器协议处理器 (DBServer Protocol)
  
  处理HallServer、GameServer、FCServer与DBServer之间的通信协议，包括：
  - 数据同步请求
  - 批量数据操作
  - 数据库状态查询
  - 缓存管理
  - 统计数据查询
  """
  
  @behaviour Teen.Protocol.ProtocolBehaviour
  
  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias Cypridina.{Repo, Accounts, Ledger}
  
  # 主协议ID
  @protocol_id 34
  
  # 子协议常量定义
  @cs_sync_user_data_p 0
  @sc_sync_user_data_p 1
  @cs_batch_update_p 2
  @sc_batch_update_p 3
  @cs_query_statistics_p 4
  @sc_query_statistics_p 5
  @cs_cache_refresh_p 6
  @sc_cache_refresh_p 7
  @cs_health_check_p 8
  @sc_health_check_p 9
  @cs_backup_data_p 10
  @sc_backup_data_p 11
  @cs_restore_data_p 12
  @sc_restore_data_p 13
  @cs_execute_migration_p 14
  @sc_execute_migration_p 15
  @cs_query_performance_p 16
  @sc_query_performance_p 17
  # 数据库消息相关协议
  @cs_get_db_messages_p 110
  @sc_get_db_messages_p 111
  @cs_get_db_info_p 112
  @sc_get_db_info_p 113
  
  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================
  
  @impl true
  def protocol_id, do: @protocol_id
  
  @impl true
  def protocol_info do
    %{
      name: "DBServer",
      description: "数据库服务器协议处理器"
    }
  end
  
  @impl true
  def supported_sub_protocols do
    [
      {@cs_sync_user_data_p, "同步用户数据"},
      {@cs_batch_update_p, "批量更新操作"},
      {@cs_query_statistics_p, "查询统计数据"},
      {@cs_cache_refresh_p, "刷新缓存"},
      {@cs_health_check_p, "健康检查"},
      {@cs_backup_data_p, "数据备份"},
      {@cs_restore_data_p, "数据恢复"},
      {@cs_execute_migration_p, "执行数据迁移"},
      {@cs_query_performance_p, "查询性能指标"},
      {@cs_get_db_messages_p, "获取数据库消息"},
      {@cs_get_db_info_p, "获取数据库信息"}
    ]
  end
  
  @impl true
  def handle_protocol(sub_protocol, data, context) do
    # DBServer协议通常用于服务器间通信，用户ID可能为系统标识
    user_id = context.user_id || "system"
    
    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "DB_SERVER", sub_protocol, user_id, "开始处理")
    
    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)
        
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end
  
  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      @cs_sync_user_data_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:user_ids]}
        ])
        
      @cs_batch_update_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:operations]},
          {:string_length, :table_name, 1, 50, "表名"}
        ])
        
      @cs_query_statistics_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:stat_type, :date_range]}
        ])
        
      @cs_backup_data_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:tables]},
          {:string_length, :backup_name, 1, 100, "备份名称"}
        ])
        
      @cs_restore_data_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:backup_id]}
        ])
        
      @cs_execute_migration_p ->
        ProtocolUtils.validate_params(data, [
          {:required, [:migration_name]}
        ])
        
      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end
  
  # ============================================================================
  # 私有函数
  # ============================================================================
  
  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    case sub_protocol do
      @cs_sync_user_data_p ->
        handle_sync_user_data(data, context)
        
      @cs_batch_update_p ->
        handle_batch_update(data, context)
        
      @cs_query_statistics_p ->
        handle_query_statistics(data, context)
        
      @cs_cache_refresh_p ->
        handle_cache_refresh(data, context)
        
      @cs_health_check_p ->
        handle_health_check(context)
        
      @cs_backup_data_p ->
        handle_backup_data(data, context)
        
      @cs_restore_data_p ->
        handle_restore_data(data, context)
        
      @cs_execute_migration_p ->
        handle_execute_migration(data, context)
        
      @cs_query_performance_p ->
        handle_query_performance(context)
        
      @cs_get_db_messages_p ->
        handle_get_db_messages(data, context)
        
      @cs_get_db_info_p ->
        handle_get_db_info(data, context)
        
      _ ->
        user_id = context.user_id || "system"
        ProtocolUtils.log_protocol(:warning, "DB_SERVER", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end
  
  # 处理同步用户数据
  defp handle_sync_user_data(data, context) do
    user_ids = Map.get(data, "user_ids")
    fields = Map.get(data, "fields", [])
    
    try do
      synced_data = Enum.map(user_ids, fn user_id ->
        case get_user_sync_data(user_id, fields) do
          {:ok, user_data} -> user_data
          {:error, _} -> %{"user_id" => user_id, "error" => "user_not_found"}
        end
      end)
      
      response_data = ProtocolUtils.success_response(%{
        "syncedUsers" => synced_data,
        "totalCount" => length(user_ids),
        "successCount" => count_successful_syncs(synced_data),
        "syncTime" => ProtocolUtils.current_timestamp()
      })
      
      ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_sync_user_data_p, "system",
        "同步用户数据成功，共#{length(user_ids)}个用户")
      {:ok, @sc_sync_user_data_p, response_data}
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_sync_user_data_p, "system",
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "同步用户数据失败")
        {:ok, @sc_sync_user_data_p, error_data}
    end
  end
  
  # 处理批量更新操作
  defp handle_batch_update(data, context) do
    operations = Map.get(data, "operations")
    table_name = Map.get(data, "table_name")
    transaction_mode = Map.get(data, "transaction_mode", true)
    
    try do
      result = if transaction_mode do
        execute_batch_in_transaction(operations, table_name)
      else
        execute_batch_operations(operations, table_name)
      end
      
      case result do
        {:ok, batch_result} ->
          response_data = ProtocolUtils.success_response(%{
            "processed" => batch_result.processed_count,
            "successful" => batch_result.successful_count,
            "failed" => batch_result.failed_count,
            "errors" => batch_result.errors,
            "executionTime" => batch_result.execution_time
          })
          
          ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_batch_update_p, "system",
            "批量更新完成: 成功#{batch_result.successful_count}/#{batch_result.processed_count}")
          {:ok, @sc_batch_update_p, response_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_batch_update_p, "system",
            "批量更新失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "批量更新失败")
          {:ok, @sc_batch_update_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_batch_update_p, "system",
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "批量更新异常")
        {:ok, @sc_batch_update_p, error_data}
    end
  end
  
  # 处理查询统计数据
  defp handle_query_statistics(data, context) do
    stat_type = Map.get(data, "stat_type")
    date_range = Map.get(data, "date_range")
    filters = Map.get(data, "filters", %{})
    
    try do
      case query_statistics_data(stat_type, date_range, filters) do
        {:ok, stats} ->
          response_data = ProtocolUtils.success_response(%{
            "statType" => stat_type,
            "dateRange" => date_range,
            "statistics" => stats.data,
            "summary" => stats.summary,
            "generatedAt" => ProtocolUtils.current_timestamp()
          })
          
          ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_query_statistics_p, "system",
            "查询统计数据成功: #{stat_type}")
          {:ok, @sc_query_statistics_p, response_data}
          
        {:error, :invalid_stat_type} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "无效的统计类型")
          {:ok, @sc_query_statistics_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_query_statistics_p, "system",
            "查询统计数据失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "查询统计数据失败")
          {:ok, @sc_query_statistics_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_query_statistics_p, "system",
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "查询统计数据异常")
        {:ok, @sc_query_statistics_p, error_data}
    end
  end
  
  # 处理刷新缓存
  defp handle_cache_refresh(data, context) do
    cache_keys = Map.get(data, "cache_keys", [])
    cache_type = Map.get(data, "cache_type", "all")
    
    try do
      case refresh_cache_data(cache_type, cache_keys) do
        {:ok, refresh_result} ->
          response_data = ProtocolUtils.success_response(%{
            "refreshed" => true,
            "cacheType" => cache_type,
            "refreshedKeys" => refresh_result.refreshed_keys,
            "failedKeys" => refresh_result.failed_keys,
            "refreshTime" => ProtocolUtils.current_timestamp()
          })
          
          ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_cache_refresh_p, "system",
            "缓存刷新完成: #{length(refresh_result.refreshed_keys)}个键")
          {:ok, @sc_cache_refresh_p, response_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_cache_refresh_p, "system",
            "缓存刷新失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "缓存刷新失败")
          {:ok, @sc_cache_refresh_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_cache_refresh_p, "system",
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "缓存刷新异常")
        {:ok, @sc_cache_refresh_p, error_data}
    end
  end
  
  # 处理健康检查
  defp handle_health_check(context) do
    try do
      health_status = %{
        "database" => check_database_health(),
        "cache" => check_cache_health(),
        "connections" => check_connection_health(),
        "performance" => check_performance_metrics(),
        "timestamp" => ProtocolUtils.current_timestamp()
      }
      
      overall_status = determine_overall_health(health_status)
      
      response_data = ProtocolUtils.success_response(%{
        "status" => overall_status,
        "details" => health_status,
        "healthy" => overall_status == "healthy"
      })
      
      ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_health_check_p, "system",
        "健康检查完成: #{overall_status}")
      {:ok, @sc_health_check_p, response_data}
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_health_check_p, "system",
          "健康检查异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "健康检查失败")
        {:ok, @sc_health_check_p, error_data}
    end
  end
  
  # 处理数据备份
  defp handle_backup_data(data, context) do
    tables = Map.get(data, "tables")
    backup_name = Map.get(data, "backup_name")
    backup_type = Map.get(data, "backup_type", "full")
    
    try do
      case create_data_backup(tables, backup_name, backup_type) do
        {:ok, backup_info} ->
          response_data = ProtocolUtils.success_response(%{
            "backupId" => backup_info.backup_id,
            "backupName" => backup_name,
            "tables" => tables,
            "backupSize" => backup_info.backup_size,
            "recordCount" => backup_info.record_count,
            "createdAt" => ProtocolUtils.current_timestamp(),
            "msg" => "数据备份完成"
          })
          
          ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_backup_data_p, "system",
            "数据备份完成: #{backup_name}")
          {:ok, @sc_backup_data_p, response_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_backup_data_p, "system",
            "数据备份失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "数据备份失败")
          {:ok, @sc_backup_data_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_backup_data_p, "system",
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "数据备份异常")
        {:ok, @sc_backup_data_p, error_data}
    end
  end
  
  # 处理数据恢复
  defp handle_restore_data(data, context) do
    backup_id = Map.get(data, "backup_id")
    restore_options = Map.get(data, "restore_options", %{})
    
    try do
      case restore_data_from_backup(backup_id, restore_options) do
        {:ok, restore_info} ->
          response_data = ProtocolUtils.success_response(%{
            "restored" => true,
            "backupId" => backup_id,
            "restoredTables" => restore_info.restored_tables,
            "restoredRecords" => restore_info.restored_records,
            "restoredAt" => ProtocolUtils.current_timestamp(),
            "msg" => "数据恢复完成"
          })
          
          ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_restore_data_p, "system",
            "数据恢复完成: #{backup_id}")
          {:ok, @sc_restore_data_p, response_data}
          
        {:error, :backup_not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "备份不存在")
          {:ok, @sc_restore_data_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_restore_data_p, "system",
            "数据恢复失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "数据恢复失败")
          {:ok, @sc_restore_data_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_restore_data_p, "system",
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "数据恢复异常")
        {:ok, @sc_restore_data_p, error_data}
    end
  end
  
  # 处理执行数据迁移
  defp handle_execute_migration(data, context) do
    migration_name = Map.get(data, "migration_name")
    migration_params = Map.get(data, "migration_params", %{})
    
    try do
      case execute_data_migration(migration_name, migration_params) do
        {:ok, migration_result} ->
          response_data = ProtocolUtils.success_response(%{
            "migrationName" => migration_name,
            "executed" => true,
            "affectedRecords" => migration_result.affected_records,
            "executionTime" => migration_result.execution_time,
            "result" => migration_result.result,
            "executedAt" => ProtocolUtils.current_timestamp()
          })
          
          ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_execute_migration_p, "system",
            "数据迁移完成: #{migration_name}")
          {:ok, @sc_execute_migration_p, response_data}
          
        {:error, :migration_not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "迁移脚本不存在")
          {:ok, @sc_execute_migration_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_execute_migration_p, "system",
            "数据迁移失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "数据迁移失败")
          {:ok, @sc_execute_migration_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_execute_migration_p, "system",
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "数据迁移异常")
        {:ok, @sc_execute_migration_p, error_data}
    end
  end
  
  # 处理查询性能指标
  defp handle_query_performance(context) do
    try do
      performance_metrics = %{
        "queryStats" => get_query_performance_stats(),
        "connectionStats" => get_connection_stats(),
        "memoryUsage" => get_memory_usage_stats(),
        "cacheStats" => get_cache_performance_stats(),
        "slowQueries" => get_slow_queries(),
        "timestamp" => ProtocolUtils.current_timestamp()
      }
      
      response_data = ProtocolUtils.success_response(%{
        "performance" => performance_metrics
      })
      
      ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_query_performance_p, "system",
        "查询性能指标完成")
      {:ok, @sc_query_performance_p, response_data}
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_query_performance_p, "system",
          "查询性能指标异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "查询性能指标失败")
        {:ok, @sc_query_performance_p, error_data}
    end
  end
  
  # ============================================================================
  # 工具函数
  # ============================================================================
  
  defp get_user_sync_data(user_id, fields) do
    try do
      case Accounts.get_user(user_id) do
        {:ok, user} ->
          sync_data = %{
            "user_id" => user.id,
            "username" => user.username,
            "points" => user.points,
            "vip_level" => user.vip_level,
            "last_login_at" => format_datetime(user.last_login_at),
            "updated_at" => format_datetime(user.updated_at)
          }
          
          # 如果指定了特定字段，只返回这些字段
          filtered_data = if fields != [] do
            Map.take(sync_data, fields)
          else
            sync_data
          end
          
          {:ok, filtered_data}
          
        {:error, reason} ->
          {:error, reason}
      end
    rescue
      error ->
        {:error, error}
    end
  end
  
  defp count_successful_syncs(synced_data) do
    Enum.count(synced_data, fn data -> not Map.has_key?(data, "error") end)
  end
  
  defp execute_batch_in_transaction(operations, table_name) do
    start_time = System.monotonic_time(:millisecond)
    
    result = Repo.transaction(fn ->
      execute_batch_operations_internal(operations, table_name)
    end)
    
    end_time = System.monotonic_time(:millisecond)
    execution_time = end_time - start_time
    
    case result do
      {:ok, batch_result} ->
        {:ok, Map.put(batch_result, :execution_time, execution_time)}
      {:error, reason} ->
        {:error, reason}
    end
  end
  
  defp execute_batch_operations(operations, table_name) do
    start_time = System.monotonic_time(:millisecond)
    result = execute_batch_operations_internal(operations, table_name)
    end_time = System.monotonic_time(:millisecond)
    execution_time = end_time - start_time
    
    {:ok, Map.put(result, :execution_time, execution_time)}
  end
  
  defp execute_batch_operations_internal(operations, _table_name) do
    results = Enum.map(operations, fn operation ->
      try do
        # 这里应该根据实际的操作类型执行相应的数据库操作
        # 这是一个简化的示例
        case operation["type"] do
          "insert" -> {:ok, "inserted"}
          "update" -> {:ok, "updated"}
          "delete" -> {:ok, "deleted"}
          _ -> {:error, "unknown_operation"}
        end
      rescue
        error -> {:error, inspect(error)}
      end
    end)
    
    successful_count = Enum.count(results, fn {status, _} -> status == :ok end)
    failed_count = length(results) - successful_count
    errors = results
    |> Enum.filter(fn {status, _} -> status == :error end)
    |> Enum.map(fn {_, error} -> error end)
    
    %{
      processed_count: length(operations),
      successful_count: successful_count,
      failed_count: failed_count,
      errors: errors
    }
  end
  
  defp query_statistics_data(stat_type, date_range, filters) do
    case stat_type do
      "user_stats" ->
        {:ok, %{
          data: %{
            "total_users" => 1000,
            "active_users" => 750,
            "new_users" => 50
          },
          summary: "用户统计数据"
        }}
      "financial_stats" ->
        {:ok, %{
          data: %{
            "total_recharge" => 100000,
            "total_withdrawal" => 80000,
            "net_revenue" => 20000
          },
          summary: "财务统计数据"
        }}
      "game_stats" ->
        {:ok, %{
          data: %{
            "total_games" => 5000,
            "total_bets" => 250000,
            "total_winnings" => 200000
          },
          summary: "游戏统计数据"
        }}
      _ ->
        {:error, :invalid_stat_type}
    end
  end
  
  defp refresh_cache_data(cache_type, cache_keys) do
    # 简化的缓存刷新逻辑
    {:ok, %{
      refreshed_keys: cache_keys,
      failed_keys: []
    }}
  end
  
  defp check_database_health() do
    try do
      # 执行简单的数据库查询测试连接
      Repo.query("SELECT 1", [])
      %{"status" => "healthy", "connections" => Repo.get_dynamic_repo().pool_size}
    rescue
      _ -> %{"status" => "unhealthy", "error" => "database_connection_failed"}
    end
  end
  
  defp check_cache_health() do
    # 简化的缓存健康检查
    %{"status" => "healthy", "hit_rate" => 85.5}
  end
  
  defp check_connection_health() do
    %{
      "active_connections" => 25,
      "max_connections" => 100,
      "connection_utilization" => 25.0
    }
  end
  
  defp check_performance_metrics() do
    %{
      "avg_query_time" => 12.5,
      "slow_queries" => 2,
      "queries_per_second" => 150.0
    }
  end
  
  defp determine_overall_health(health_status) do
    db_healthy = health_status["database"]["status"] == "healthy"
    cache_healthy = health_status["cache"]["status"] == "healthy"
    
    if db_healthy and cache_healthy do
      "healthy"
    else
      "unhealthy"
    end
  end
  
  defp create_data_backup(_tables, backup_name, _backup_type) do
    # 简化的备份逻辑
    {:ok, %{
      backup_id: "backup_#{System.system_time(:second)}",
      backup_size: 1024 * 1024,  # 1MB
      record_count: 10000
    }}
  end
  
  defp restore_data_from_backup(backup_id, _restore_options) do
    # 简化的恢复逻辑
    {:ok, %{
      restored_tables: ["users", "transactions"],
      restored_records: 10000
    }}
  end
  
  defp execute_data_migration(migration_name, _migration_params) do
    # 简化的迁移逻辑
    {:ok, %{
      affected_records: 500,
      execution_time: 2500,  # 2.5秒
      result: "migration_successful"
    }}
  end
  
  defp get_query_performance_stats() do
    %{
      "total_queries" => 50000,
      "avg_execution_time" => 15.2,
      "max_execution_time" => 250.0,
      "queries_per_second" => 145.6
    }
  end
  
  defp get_connection_stats() do
    %{
      "active_connections" => 28,
      "idle_connections" => 12,
      "max_connections" => 100,
      "connection_errors" => 0
    }
  end
  
  defp get_memory_usage_stats() do
    %{
      "used_memory" => 512 * 1024 * 1024,  # 512MB
      "available_memory" => 2048 * 1024 * 1024,  # 2GB
      "memory_utilization" => 25.0
    }
  end
  
  defp get_cache_performance_stats() do
    %{
      "cache_hits" => 8500,
      "cache_misses" => 1500,
      "hit_rate" => 85.0,
      "cache_size" => 128 * 1024 * 1024  # 128MB
    }
  end
  
  defp get_slow_queries() do
    [
      %{
        "query" => "SELECT * FROM users WHERE last_login < ?",
        "execution_time" => 450.2,
        "timestamp" => ProtocolUtils.current_timestamp()
      },
      %{
        "query" => "UPDATE game_records SET status = ? WHERE created_at < ?",
        "execution_time" => 320.1,
        "timestamp" => ProtocolUtils.current_timestamp()
      }
    ]
  end
  
  defp format_datetime(nil), do: nil
  defp format_datetime(datetime), do: DateTime.to_unix(datetime, :millisecond)

  # 处理获取数据库消息
  defp handle_get_db_messages(data, context) do
    msg_size = Map.get(data, "msgsize", 100)
    
    try do
      # 模拟获取数据库消息
      messages = [
        %{
          "id" => 1,
          "type" => "info",
          "message" => "数据库连接正常",
          "timestamp" => ProtocolUtils.current_timestamp(),
          "level" => "info"
        },
        %{
          "id" => 2,
          "type" => "warning",
          "message" => "缓存命中率较低",
          "timestamp" => ProtocolUtils.current_timestamp() - 60000,
          "level" => "warning"
        },
        %{
          "id" => 3,
          "type" => "error",
          "message" => "慢查询检测到",
          "timestamp" => ProtocolUtils.current_timestamp() - 120000,
          "level" => "error"
        }
      ]
      
      # 根据msgsize限制返回消息数量
      limited_messages = Enum.take(messages, min(msg_size, length(messages)))
      
      response_data = ProtocolUtils.success_response(%{
        "messages" => limited_messages,
        "totalCount" => length(messages),
        "returnedCount" => length(limited_messages),
        "maxSize" => msg_size
      })
      
      ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_get_db_messages_p, "system",
        "获取数据库消息成功，返回#{length(limited_messages)}条")
      {:ok, @sc_get_db_messages_p, response_data}
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_get_db_messages_p, "system",
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "获取数据库消息失败")
        {:ok, @sc_get_db_messages_p, error_data}
    end
  end

  # 处理获取数据库信息
  defp handle_get_db_info(data, context) do
    try do
      # 模拟数据库信息
      db_info = %{
        "dbType" => "PostgreSQL",
        "version" => "14.9",
        "status" => "running",
        "uptime" => 3600 * 24 * 7,  # 7天
        "connections" => %{
          "active" => 25,
          "idle" => 15,
          "max" => 100
        },
        "storage" => %{
          "used" => 5 * 1024 * 1024 * 1024,      # 5GB
          "available" => 20 * 1024 * 1024 * 1024, # 20GB
          "total" => 25 * 1024 * 1024 * 1024     # 25GB
        },
        "performance" => %{
          "transactions_per_second" => 1250.5,
          "cache_hit_ratio" => 95.2,
          "index_hit_ratio" => 98.7
        },
        "replication" => %{
          "enabled" => true,
          "slaves" => 2,
          "lag" => 0.05  # 50ms
        }
      }
      
      response_data = ProtocolUtils.success_response(%{
        "databaseInfo" => db_info,
        "timestamp" => ProtocolUtils.current_timestamp()
      })
      
      ProtocolUtils.log_protocol(:info, "DB_SERVER", @cs_get_db_info_p, "system",
        "获取数据库信息成功")
      {:ok, @sc_get_db_info_p, response_data}
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "DB_SERVER", @cs_get_db_info_p, "system",
          "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "获取数据库信息失败")
        {:ok, @sc_get_db_info_p, error_data}
    end
  end
end