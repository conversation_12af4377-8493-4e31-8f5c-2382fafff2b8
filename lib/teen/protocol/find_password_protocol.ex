defmodule Teen.Protocol.FindPasswordProtocol do
  @moduledoc """
  找回密码协议处理器
  
  处理用户密码找回相关的协议，包括：
  - 发送验证码
  - 验证验证码
  - 重置密码
  - 密码安全验证
  """
  
  @behaviour Teen.Protocol.ProtocolBehaviour
  
  require Logger
  alias Teen.Protocol.ProtocolUtils
  alias Cy<PERSON><PERSON>ina.Accounts.{User, PasswordService}
  
  # 主协议ID
  @protocol_id 1
  
  # 子协议常量定义 - 保持与websocket_handler一致
  @cs_find_password 0
  @sc_find_password 1
  @cs_request_reset_code_p 2
  @sc_request_reset_code_p 3
  @cs_reset_password_crypt 4
  @sc_reset_password_crypt 5
  @cs_phone_code_verify 5
  @sc_phone_code_verify 6
  @cs_set_new_password 7
  @sc_set_new_password 8
  @cs_check_security_question_p 8
  @sc_check_security_question_p 9
  @cs_answer_security_question_p 10
  @sc_answer_security_question_p 11
  
  # ============================================================================
  # ProtocolBehaviour 回调实现
  # ============================================================================
  
  @impl true
  def protocol_id, do: @protocol_id
  
  @impl true
  def protocol_info do
    %{
      name: "FindPassword",
      description: "找回密码协议处理器"
    }
  end
  
  @impl true
  def supported_sub_protocols do
    [
      {@cs_find_password, "找回密码"},
      {@cs_request_reset_code_p, "请求重置验证码"},
      {@cs_reset_password_crypt, "加密重置密码"},
      {@cs_phone_code_verify, "手机验证码验证"},
      {@cs_set_new_password, "设置新密码"},
      {@cs_check_security_question_p, "检查安全问题"},
      {@cs_answer_security_question_p, "回答安全问题"}
    ]
  end
  
  @impl true
  def handle_protocol(sub_protocol, data, context) do
    user_id = context.user_id || "anonymous"
    
    # 记录协议处理开始
    ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", sub_protocol, user_id, "开始处理")
    
    # 验证数据
    case validate_data(sub_protocol, data) do
      :ok ->
        # 路由到具体的处理函数
        route_to_handler(sub_protocol, data, context)
        
      {:error, reason} ->
        ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", sub_protocol, user_id, "数据验证失败: #{reason}")
        {:error, reason}
    end
  end
  
  @impl true
  def validate_data(sub_protocol, data) do
    case sub_protocol do
      0 -> # @cs_find_password
        :ok
        
      2 -> # @cs_request_reset_code_p
        ProtocolUtils.validate_params(data, [
          {:required, [:identifier, :method]},
          {:string_length, :identifier, 3, 50, "用户标识"}
        ])
        
      4 -> # @cs_reset_password_crypt
        :ok
        
      5 -> # @cs_phone_code_verify
        :ok
        
      7 -> # @cs_set_new_password
        ProtocolUtils.validate_params(data, [
          {:required, [:username, :new_password]},
          {:string_length, :new_password, 6, 20, "新密码"}
        ])
        
      8 -> # @cs_check_security_question_p
        ProtocolUtils.validate_params(data, [
          {:required, [:identifier]}
        ])
        
      10 -> # @cs_answer_security_question_p
        ProtocolUtils.validate_params(data, [
          {:required, [:identifier, :answer]},
          {:string_length, :answer, 1, 100, "安全问题答案"}
        ])
        
      _ ->
        # 其他子协议不需要特殊验证
        :ok
    end
  end
  
  # ============================================================================
  # 私有函数
  # ============================================================================
  
  # 路由到具体的处理函数
  defp route_to_handler(sub_protocol, data, context) do
    case sub_protocol do
      0 -> # @cs_find_password
        handle_find_password(data, context)
        
      2 -> # @cs_request_reset_code_p
        handle_request_reset_code(data, context)
        
      4 -> # @cs_reset_password_crypt
        handle_reset_password_crypt(data, context)
        
      5 -> # @cs_phone_code_verify
        handle_phone_code_verify(data, context)
        
      7 -> # @cs_set_new_password
        handle_set_new_password(data, context)
        
      8 -> # @cs_check_security_question_p
        handle_check_security_question(data, context)
        
      10 -> # @cs_answer_security_question_p
        handle_answer_security_question(data, context)
        
      _ ->
        user_id = context.user_id || "anonymous"
        ProtocolUtils.log_protocol(:warning, "FIND_PASSWORD", sub_protocol, user_id, "未知子协议")
        {:error, :unknown_sub_protocol}
    end
  end
  
  # 处理请求重置验证码
  defp handle_request_reset_code(data, context) do
    identifier = Map.get(data, "identifier")  # 可以是用户名、手机号或邮箱
    method = Map.get(data, "method")          # "sms" 或 "email"
    
    try do
      case PasswordService.send_reset_code(identifier, method) do
        {:ok, result} ->
          response_data = ProtocolUtils.success_response(%{
            "sent" => true,
            "method" => method,
            "masked_target" => result.masked_target,
            "expires_in" => result.expires_in,
            "msg" => case method do
              "sms" -> "验证码已发送到您的手机"
              "email" -> "验证码已发送到您的邮箱"
              _ -> "验证码已发送"
            end
          })
          
          ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", @cs_request_reset_code_p, 
            context.user_id || "anonymous", "验证码发送成功: #{method}")
          {:ok, @sc_request_reset_code_p, response_data}
          
        {:error, :user_not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "用户不存在")
          {:ok, @sc_request_reset_code_p, error_data}
          
        {:error, :no_contact_info} ->
          error_data = ProtocolUtils.error_response(:forbidden, "该账号未绑定手机号或邮箱")
          {:ok, @sc_request_reset_code_p, error_data}
          
        {:error, :rate_limited} ->
          error_data = ProtocolUtils.error_response(:rate_limited, "请求过于频繁，请稍后再试")
          {:ok, @sc_request_reset_code_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_request_reset_code_p,
            context.user_id || "anonymous", "发送验证码失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "发送验证码失败")
          {:ok, @sc_request_reset_code_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_request_reset_code_p,
          context.user_id || "anonymous", "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_request_reset_code_p, error_data}
    end
  end
  
  # 处理验证重置验证码
  defp handle_verify_reset_code(data, context) do
    identifier = Map.get(data, "identifier")
    code = Map.get(data, "code")
    
    try do
      case PasswordService.verify_reset_code(identifier, code) do
        {:ok, verification_token} ->
          response_data = ProtocolUtils.success_response(%{
            "verified" => true,
            "token" => verification_token,
            "expires_in" => 300,  # 5分钟有效期
            "msg" => "验证码验证成功"
          })
          
          ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", @cs_verify_reset_code_p,
            context.user_id || "anonymous", "验证码验证成功")
          {:ok, @sc_verify_reset_code_p, response_data}
          
        {:error, :invalid_code} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "验证码错误")
          {:ok, @sc_verify_reset_code_p, error_data}
          
        {:error, :code_expired} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "验证码已过期")
          {:ok, @sc_verify_reset_code_p, error_data}
          
        {:error, :user_not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "用户不存在")
          {:ok, @sc_verify_reset_code_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_verify_reset_code_p,
            context.user_id || "anonymous", "验证码验证失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "验证码验证失败")
          {:ok, @sc_verify_reset_code_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_verify_reset_code_p,
          context.user_id || "anonymous", "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_verify_reset_code_p, error_data}
    end
  end
  
  # 处理重置密码
  defp handle_reset_password(data, context) do
    identifier = Map.get(data, "identifier")
    code = Map.get(data, "code")
    new_password = Map.get(data, "new_password")
    token = Map.get(data, "token")  # 可选的验证令牌
    
    try do
      case PasswordService.reset_password(identifier, code, new_password, token) do
        {:ok, user} ->
          response_data = ProtocolUtils.success_response(%{
            "reset" => true,
            "userId" => user.id,
            "msg" => "密码重置成功，请重新登录"
          })
          
          ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", @cs_reset_password_p,
            user.id, "密码重置成功")
          {:ok, @sc_reset_password_p, response_data}
          
        {:error, :invalid_code} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "验证码错误")
          {:ok, @sc_reset_password_p, error_data}
          
        {:error, :code_expired} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "验证码已过期")
          {:ok, @sc_reset_password_p, error_data}
          
        {:error, :invalid_token} ->
          error_data = ProtocolUtils.error_response(:unauthorized, "验证令牌无效")
          {:ok, @sc_reset_password_p, error_data}
          
        {:error, :weak_password} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "密码强度不够")
          {:ok, @sc_reset_password_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_reset_password_p,
            context.user_id || "anonymous", "密码重置失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "密码重置失败")
          {:ok, @sc_reset_password_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_reset_password_p,
          context.user_id || "anonymous", "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_reset_password_p, error_data}
    end
  end
  
  # 处理修改密码（已登录用户）
  defp handle_change_password(data, context) do
    user_id = context.user_id
    old_password = Map.get(data, "old_password")
    new_password = Map.get(data, "new_password")
    
    if is_nil(user_id) do
      error_data = ProtocolUtils.error_response(:unauthorized, "用户未登录")
      {:ok, @sc_change_password_p, error_data}
    else
      try do
        case PasswordService.change_password(user_id, old_password, new_password) do
          {:ok, _user} ->
            response_data = ProtocolUtils.success_response(%{
              "changed" => true,
              "msg" => "密码修改成功"
            })
            
            ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", @cs_change_password_p,
              user_id, "密码修改成功")
            {:ok, @sc_change_password_p, response_data}
            
          {:error, :invalid_old_password} ->
            error_data = ProtocolUtils.error_response(:invalid_params, "原密码错误")
            {:ok, @sc_change_password_p, error_data}
            
          {:error, :weak_password} ->
            error_data = ProtocolUtils.error_response(:invalid_params, "新密码强度不够")
            {:ok, @sc_change_password_p, error_data}
            
          {:error, :same_password} ->
            error_data = ProtocolUtils.error_response(:invalid_params, "新密码不能与原密码相同")
            {:ok, @sc_change_password_p, error_data}
            
          {:error, reason} ->
            ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_change_password_p,
              user_id, "密码修改失败: #{inspect(reason)}")
            error_data = ProtocolUtils.error_response(:internal_error, "密码修改失败")
            {:ok, @sc_change_password_p, error_data}
        end
      rescue
        error ->
          ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_change_password_p,
            user_id, "处理异常: #{inspect(error)}")
          error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
          {:ok, @sc_change_password_p, error_data}
      end
    end
  end
  
  # 处理检查安全问题
  defp handle_check_security_question(data, context) do
    identifier = Map.get(data, "identifier")
    
    try do
      case PasswordService.get_security_question(identifier) do
        {:ok, question_info} ->
          response_data = ProtocolUtils.success_response(%{
            "hasQuestion" => true,
            "question" => question_info.question,
            "questionId" => question_info.question_id
          })
          
          ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", @cs_check_security_question_p,
            context.user_id || "anonymous", "获取安全问题成功")
          {:ok, @sc_check_security_question_p, response_data}
          
        {:error, :no_security_question} ->
          response_data = ProtocolUtils.success_response(%{
            "hasQuestion" => false,
            "msg" => "该账号未设置安全问题"
          })
          {:ok, @sc_check_security_question_p, response_data}
          
        {:error, :user_not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "用户不存在")
          {:ok, @sc_check_security_question_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_check_security_question_p,
            context.user_id || "anonymous", "获取安全问题失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "获取安全问题失败")
          {:ok, @sc_check_security_question_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_check_security_question_p,
          context.user_id || "anonymous", "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_check_security_question_p, error_data}
    end
  end
  
  # 处理回答安全问题
  defp handle_answer_security_question(data, context) do
    identifier = Map.get(data, "identifier")
    answer = Map.get(data, "answer")
    question_id = Map.get(data, "question_id")
    
    try do
      case PasswordService.verify_security_answer(identifier, question_id, answer) do
        {:ok, verification_token} ->
          response_data = ProtocolUtils.success_response(%{
            "verified" => true,
            "token" => verification_token,
            "expires_in" => 300,  # 5分钟有效期
            "msg" => "安全问题验证成功"
          })
          
          ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", @cs_answer_security_question_p,
            context.user_id || "anonymous", "安全问题验证成功")
          {:ok, @sc_answer_security_question_p, response_data}
          
        {:error, :wrong_answer} ->
          error_data = ProtocolUtils.error_response(:invalid_params, "安全问题答案错误")
          {:ok, @sc_answer_security_question_p, error_data}
          
        {:error, :user_not_found} ->
          error_data = ProtocolUtils.error_response(:not_found, "用户不存在")
          {:ok, @sc_answer_security_question_p, error_data}
          
        {:error, reason} ->
          ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_answer_security_question_p,
            context.user_id || "anonymous", "安全问题验证失败: #{inspect(reason)}")
          error_data = ProtocolUtils.error_response(:internal_error, "安全问题验证失败")
          {:ok, @sc_answer_security_question_p, error_data}
      end
    rescue
      error ->
        ProtocolUtils.log_protocol(:error, "FIND_PASSWORD", @cs_answer_security_question_p,
          context.user_id || "anonymous", "处理异常: #{inspect(error)}")
        error_data = ProtocolUtils.error_response(:internal_error, "系统异常，请稍后重试")
        {:ok, @sc_answer_security_question_p, error_data}
    end
  end
  
  # 处理找回密码 (sub_id: 0)
  defp handle_find_password(data, context) do
    username = Map.get(data, "username", "")
    
    ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", 0, context.user_id || "anonymous", 
      "找回密码请求: #{username}")
    
    # 模拟响应
    response_data = ProtocolUtils.success_response(%{
      "status" => 0,
      "message" => "请求成功",
      "username" => username
    })
    
    {:ok, 1, response_data}
  end
  
  # 处理加密重置密码 (sub_id: 4)
  defp handle_reset_password_crypt(data, context) do
    username = Map.get(data, "username", "")
    encrypted_data = Map.get(data, "encrypted_data", "")
    
    ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", 4, context.user_id || "anonymous",
      "加密重置密码: #{username}")
    
    # 模拟响应
    response_data = ProtocolUtils.success_response(%{
      "status" => 0,
      "message" => "加密重置处理成功",
      "username" => username
    })
    
    {:ok, 5, response_data}
  end
  
  # 处理手机验证码验证 (sub_id: 5)
  defp handle_phone_code_verify(data, context) do
    phone = Map.get(data, "phone", "")
    code = Map.get(data, "code", "")
    
    ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", 5, context.user_id || "anonymous",
      "手机验证码验证: #{phone}")
    
    # 模拟验证码验证
    is_valid = String.length(code) >= 4
    
    response_data = if is_valid do
      ProtocolUtils.success_response(%{
        "status" => 0,
        "message" => "验证码验证成功",
        "phone" => phone
      })
    else
      ProtocolUtils.error_response(:invalid_params, "验证码错误")
    end
    
    {:ok, 6, response_data}
  end
  
  # 处理设置新密码 (sub_id: 7)
  defp handle_set_new_password(data, context) do
    username = Map.get(data, "username", "")
    new_password = Map.get(data, "new_password", "")
    
    ProtocolUtils.log_protocol(:info, "FIND_PASSWORD", 7, context.user_id || "anonymous",
      "设置新密码: #{username}")
    
    # 验证密码强度
    if String.length(new_password) < 6 do
      error_data = ProtocolUtils.error_response(:invalid_params, "密码长度不能少于6位")
      {:ok, 8, error_data}
    else
      response_data = ProtocolUtils.success_response(%{
        "status" => 0,
        "message" => "密码设置成功",
        "username" => username
      })
      
      {:ok, 8, response_data}
    end
  end
end