defmodule Teen.Protocol.ProtocolBehaviour do
  @moduledoc """
  协议处理器的行为定义
  
  所有协议处理模块都应该实现此行为，以确保统一的接口和处理规范
  """

  @type protocol_result :: 
    {:ok, sub_id :: integer(), response_data :: map()} |
    {:error, reason :: term()} |
    {:noreply}

  @type user_context :: %{
    user_id: String.t(),
    session_id: String.t(),
    socket_info: map()
  }

  @doc """
  获取此协议处理器支持的主协议ID
  """
  @callback protocol_id() :: integer()

  @doc """
  获取协议处理器的名称和描述
  """
  @callback protocol_info() :: %{
    name: String.t(),
    description: String.t()
  }

  @doc """
  处理协议消息
  
  ## 参数
    - sub_id: 子协议ID
    - data: 协议数据
    - context: 用户上下文信息
  
  ## 返回值
    - {:ok, response_sub_id, response_data} - 成功，返回响应子协议ID和数据
    - {:error, reason} - 失败，返回错误原因
    - {:noreply} - 不需要回复
  """
  @callback handle_protocol(sub_id :: integer(), data :: map(), context :: user_context()) :: protocol_result()

  @doc """
  获取支持的子协议列表
  
  返回一个包含子协议ID和描述的列表
  """
  @callback supported_sub_protocols() :: [{sub_id :: integer(), description :: String.t()}]

  @doc """
  验证协议数据
  
  在处理协议前验证数据的有效性
  """
  @callback validate_data(sub_id :: integer(), data :: map()) :: :ok | {:error, reason :: term()}

  @optional_callbacks [validate_data: 2]
end