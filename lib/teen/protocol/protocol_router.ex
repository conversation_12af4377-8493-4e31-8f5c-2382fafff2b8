defmodule Teen.Protocol.ProtocolRouter do
  @moduledoc """
  协议路由器 - 负责将协议消息分发到对应的处理模块
  
  提供统一的协议分发机制，支持动态注册和协议处理器管理
  """

  require Logger

  # 协议处理器映射表
  # 键: 主协议ID，值: 处理模块
  @protocol_handlers %{
    0 => Teen.Protocol.RegLoginProtocol,          # 注册登录
    1 => Teen.Protocol.FindPasswordProtocol,      # 找回密码
    4 => Teen.Protocol.GameProtocol,              # 游戏逻辑
    5 => Teen.Protocol.XCProtocol,                # 子游戏服务器协议
    6 => Teen.Protocol.BaseInfoProtocol,          # 基本信息
    7 => Teen.Protocol.MoneyProtocol,             # 金钱钱包
    14 => Teen.Protocol.MailProtocol,             # 邮件系统
    15 => Teen.Protocol.NoticeProtocol,           # 公告系统
    34 => Teen.Protocol.DBServerProtocol,         # 数据库服务器
    40 => Teen.Protocol.RankProtocol,             # 排行榜
    42 => Teen.Protocol.TaskProtocol,             # 活动任务
    101 => Teen.Protocol.HallActivityProtocol     # 大厅活动
    # 其他协议可以在这里添加
  }

  @doc """
  处理WebSocket消息的统一入口
  
  ## 参数
    - message: 消息结构 %{main_id: integer, sub_id: integer, data: map}
    - context: 用户上下文 %{user_id: string, session_id: string, ...}
  
  ## 返回值
    - {:reply, response_map, new_context} - 需要回复客户端
    - {:ok, new_context} - 处理成功但无需回复
    - {:error, reason} - 处理失败
  """
  def handle_message(%{main_id: main_id, sub_id: sub_id, data: data}, context) do
    start_time = System.monotonic_time(:microsecond)
    user_id = context.user_id || "anonymous"
    
    Logger.info("""
    [PROTOCOL_ROUTER] 处理消息
    主协议: #{main_id}
    子协议: #{sub_id}
    用户: #{user_id}
    数据: #{inspect(data)}
    """)

    # 路由消息到具体的协议处理器
    result = route_protocol(main_id, sub_id, data || %{}, context)
    
    # 记录处理时间
    duration = System.monotonic_time(:microsecond) - start_time
    
    case result do
      {:ok, response_data} ->
        Logger.info("""
        [PROTOCOL_ROUTER] 消息处理成功
        主协议: #{main_id}, 子协议: #{sub_id}
        耗时: #{duration}μs
        响应: #{inspect(response_data)}
        """)
        {:reply, response_data, context}
        
      {:noreply} ->
        Logger.debug("[PROTOCOL_ROUTER] 消息处理成功，无需回复 main=#{main_id} sub=#{sub_id} 耗时=#{duration}μs")
        {:ok, context}
        
      {:error, reason} ->
        Logger.error("""
        [PROTOCOL_ROUTER] 消息处理失败
        主协议: #{main_id}, 子协议: #{sub_id}
        用户: #{user_id}
        错误: #{inspect(reason)}
        耗时: #{duration}μs
        """)
        
        # 构建错误响应
        error_response = build_error_response(main_id, sub_id, reason)
        {:reply, error_response, context}
    end
  end

  @doc """
  路由协议消息到对应的处理器
  
  ## 参数
    - main_id: 主协议ID
    - sub_id: 子协议ID
    - data: 协议数据
    - context: 用户上下文
  
  ## 返回值
    - {:ok, response} - 成功处理并返回响应
    - {:error, reason} - 处理失败
    - {:noreply} - 不需要回复
  """
  def route_protocol(main_id, sub_id, data, context) do
    start_time = System.monotonic_time(:microsecond)
    
    Logger.debug("""
    [PROTOCOL_ROUTER] 路由协议消息
    主协议: #{main_id}
    子协议: #{sub_id}
    用户: #{context.user_id}
    """)

    result = case Map.get(@protocol_handlers, main_id) do
      nil ->
        Logger.warning("[PROTOCOL_ROUTER] 未找到协议处理器: main_id=#{main_id}")
        {:error, :unknown_protocol}
      
      handler_module ->
        try do
          # 验证处理器模块是否加载
          ensure_handler_loaded(handler_module)
          
          # 调用处理器
          case handler_module.handle_protocol(sub_id, data, context) do
            {:ok, response_sub_id, response_data} ->
              log_protocol_success(main_id, sub_id, start_time)
              {:ok, build_response(main_id, response_sub_id, response_data)}
            
            {:error, reason} = error ->
              log_protocol_error(main_id, sub_id, reason, start_time)
              error
            
            {:noreply} ->
              log_protocol_success(main_id, sub_id, start_time)
              {:noreply}
          end
        rescue
          error ->
            Logger.error("""
            [PROTOCOL_ROUTER] 协议处理异常
            处理器: #{handler_module}
            错误: #{inspect(error)}
            堆栈: #{inspect(__STACKTRACE__)}
            """)
            {:error, :internal_error}
        end
    end

    result
  end

  @doc """
  获取所有注册的协议处理器信息
  """
  def list_handlers do
    @protocol_handlers
    |> Enum.map(fn {main_id, module} ->
      info = try do
        module.protocol_info()
      rescue
        _ -> %{name: "Unknown", description: "No description"}
      end
      
      %{
        main_id: main_id,
        module: module,
        name: info.name,
        description: info.description
      }
    end)
    |> Enum.sort_by(& &1.main_id)
  end

  @doc """
  检查协议处理器是否存在
  """
  def handler_exists?(main_id) do
    Map.has_key?(@protocol_handlers, main_id)
  end

  @doc """
  获取协议处理器支持的子协议列表
  """
  def get_supported_sub_protocols(main_id) do
    case Map.get(@protocol_handlers, main_id) do
      nil -> {:error, :handler_not_found}
      module ->
        try do
          {:ok, module.supported_sub_protocols()}
        rescue
          _ -> {:error, :not_implemented}
        end
    end
  end

  @doc """
  构建错误响应
  """
  def build_error_response(main_id, sub_id, reason) do
    alias Teen.Protocol.ProtocolUtils
    
    error_data = case reason do
      :unknown_protocol ->
        ProtocolUtils.error_response(:not_found, "未知协议: #{main_id}")
        
      :unknown_sub_protocol ->
        ProtocolUtils.error_response(:not_found, "未知子协议: #{main_id}.#{sub_id}")
        
      :invalid_params ->
        ProtocolUtils.error_response(:invalid_params, "参数验证失败")
        
      :internal_error ->
        ProtocolUtils.error_response(:internal_error, "服务器内部错误")
        
      _ ->
        ProtocolUtils.error_response(:unknown_error, "未知错误: #{inspect(reason)}")
    end
    
    %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => error_data
    }
  end

  # 私有函数

  defp ensure_handler_loaded(module) do
    case Code.ensure_loaded?(module) do
      true -> :ok
      false -> 
        Logger.error("[PROTOCOL_ROUTER] 无法加载协议处理器模块: #{module}")
        raise "Handler module not loaded: #{module}"
    end
  end

  defp build_response(main_id, sub_id, data) do
    %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => data
    }
  end

  defp log_protocol_success(main_id, sub_id, start_time) do
    duration = System.monotonic_time(:microsecond) - start_time
    Logger.debug("[PROTOCOL_ROUTER] 协议处理成功 main=#{main_id} sub=#{sub_id} 耗时=#{duration}μs")
  end

  defp log_protocol_error(main_id, sub_id, reason, start_time) do
    duration = System.monotonic_time(:microsecond) - start_time
    Logger.error("[PROTOCOL_ROUTER] 协议处理失败 main=#{main_id} sub=#{sub_id} 原因=#{inspect(reason)} 耗时=#{duration}μs")
  end
end