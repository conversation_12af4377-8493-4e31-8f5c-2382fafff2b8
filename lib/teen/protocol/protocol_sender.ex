defmodule Teen.Protocol.ProtocolSender do
  @moduledoc """
  统一的协议发送接口

  确保所有发送到客户端的消息格式一致，遵循客户端Protocol.ts的定义。
  消息格式：{ mainId, subId, data }
  """

  require Logger
  alias CypridinaWeb.Endpoint

  @doc """
  发送消息给指定用户

  ## 参数
    - user_id: 用户ID
    - main_id: 主协议ID (参考Protocol.ts的MainProto)
    - sub_id: 子协议ID
    - data: 协议数据内容

  ## 示例
      ProtocolSender.send_to_user(123, 7, 0, %{"money" => 1000})
  """
  def send_to_user(user_id, main_id, sub_id, data) do
    message = build_message(main_id, sub_id, data)
    user_channel = "user:#{user_id}"

    case Endpoint.broadcast(user_channel, "private_message", message) do
      :ok ->
        Logger.debug("📤 协议发送成功: user=#{user_id}, main=#{main_id}, sub=#{sub_id}")
        :ok
      {:error, reason} ->
        Logger.error("📤 协议发送失败: user=#{user_id}, error=#{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  发送消息给房间内所有用户
  """
  def send_to_room(room_id, main_id, sub_id, data) do
    message = build_message(main_id, sub_id, data)
    room_channel = "room:#{room_id}"

    case Endpoint.broadcast(room_channel, "room_message", message) do
      :ok ->
        Logger.debug("📤 房间广播成功: room=#{room_id}, main=#{main_id}, sub=#{sub_id}")
        :ok
      {:error, reason} ->
        Logger.error("📤 房间广播失败: room=#{room_id}, error=#{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  批量发送消息给多个用户
  """
  def send_to_users(user_ids, main_id, sub_id, data) when is_list(user_ids) do
    Enum.map(user_ids, fn user_id ->
      send_to_user(user_id, main_id, sub_id, data)
    end)
  end

  # ==================== 特定协议快捷方法 ====================

  @doc """
  发送金币变更通知 (SC_SET_MONEY_P)
  """
  def notify_money_change(user_id, new_balance) do
    send_to_user(user_id, 7, 0, %{"money" => new_balance})
  end

  @doc """
  发送钱包余额变更通知 (SC_SET_WALLETMONEY_P)
  """
  def notify_wallet_change(user_id, new_balance) do
    send_to_user(user_id, 7, 1, %{"walletmoney" => new_balance})
  end

  @doc """
  发送游戏中金币变更通知 (SC_SET_GAME_MONEY_P)
  """
  def notify_game_money_change(user_id, total_money) do
    send_to_user(user_id, 7, 7, %{"totalmoney" => total_money})
  end

  @doc """
  发送其他设备登录通知 (SC_OHTER_LOGIN_P)

  通知用户账号在别处登录，你已经被挤下线
  """
  def notify_other_login(user_id) do
    send_to_user(user_id, 0, 10, %{})
  end

  @doc """
  发送顶号成功通知 (SC_LOGIN_OTHER_P)

  通知用户成功将其他设备挤下线
  """
  def notify_login_other(user_id) do
    send_to_user(user_id, 0, 11, %{})
  end

  @doc """
  发送服务器维护通知 (SC_SERVER_STOP_P)
  """
  def notify_server_stop(user_id, reason \\ "服务器维护中") do
    send_to_user(user_id, 0, 12, %{"reason" => reason})
  end

  @doc """
  发送公告消息 (SC_NOTICE_P)
  """
  def send_notice(user_id, notice_data) do
    send_to_user(user_id, 15, 0, notice_data)
  end

  @doc """
  发送新邮件通知 (SC_ADD_MAIL_P)
  """
  def notify_new_mail(user_id, mail_data) do
    send_to_user(user_id, 14, 4, mail_data)
  end

  # ==================== 私有函数 ====================

  defp build_message(main_id, sub_id, data) do
    %{
      "mainId" => main_id,
      "subId" => sub_id,
      "data" => data || %{}
    }
  end
end
