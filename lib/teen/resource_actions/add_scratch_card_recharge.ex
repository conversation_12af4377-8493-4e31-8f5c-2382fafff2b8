defmodule Teen.ResourceActions.AddScratchCardRecharge do
  @moduledoc """
  批量为刮刮卡用户增加充值金额的资源操作
  """

  use Backpex.ResourceAction
  import Phoenix.LiveView, only: [put_flash: 3]

  alias Teen.ActivitySystem.ScratchCardService
  alias Teen.GameManagement.UserVipRecord

  @impl Backpex.ResourceAction
  def title, do: "批量增加刮刮卡充值"

  @impl Backpex.ResourceAction
  def label, do: "增加充值(100元)"

  @impl Backpex.ResourceAction
  def fields, do: []

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    change
    |> Ecto.Changeset.cast(attrs, [])
  end

  @impl Backpex.ResourceAction
  def handle(socket, _data) do
    %{selected_items: selected_items} = socket.assigns

    # 只处理刮刮卡活动记录
    scratch_card_items = Enum.filter(selected_items, fn item -> 
      item.activity_type == :scratch_card 
    end)

    if Enum.empty?(scratch_card_items) do
      socket = put_flash(socket, :error, "请选择刮刮卡活动记录")
      {:ok, socket}
    else
      # 对于增加充值操作，这里固定增加100元的示例
      results =
        Enum.map(scratch_card_items, fn item ->
          amount_in_cents = 10000  # 100元 = 10000分
          
          with {:ok, vip_record} <- get_or_create_vip_record(item.user_id),
               new_amount = Decimal.add(vip_record.total_recharge_amount || Decimal.new(0), Decimal.new(amount_in_cents)),
               {:ok, _} <- UserVipRecord.update(vip_record, %{total_recharge_amount: new_amount}),
               {:ok, _} <- ScratchCardService.update_user_recharge(item.user_id, Decimal.new(amount_in_cents)) do
            {:ok, "用户 #{item.user_id} 增加充值100元成功"}
          else
            {:error, reason} ->
              {:error, "用户 #{item.user_id} 增加充值失败: #{inspect(reason)}"}
          end
        end)

      successful = Enum.filter(results, fn {status, _} -> status == :ok end)
      failed = Enum.filter(results, fn {status, _} -> status == :error end)

      socket =
        if length(successful) > 0 do
          put_flash(socket, :info, "成功处理 #{length(successful)} 条记录，每条增加100元")
        else
          socket
        end

      socket =
        if length(failed) > 0 do
          put_flash(socket, :error, "失败 #{length(failed)} 条记录")
        else
          socket
        end

      {:ok, socket}
    end
  end

  defp get_or_create_vip_record(user_id) do
    case UserVipRecord.get_by_user_id(user_id) do
      {:ok, record} -> 
        {:ok, record}
        
      {:error, %Ash.Error.Invalid{errors: [%Ash.Error.Query.NotFound{}]}} ->
        UserVipRecord.create(%{
          user_id: user_id,
          current_vip_level: 0,
          vip_experience: Decimal.new(0),
          total_recharge_amount: Decimal.new(0)
        })
        
      error -> 
        error
    end
  end
end