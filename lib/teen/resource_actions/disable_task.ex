defmodule Teen.ResourceActions.DisableTask do
  @moduledoc """
  禁用任务资源操作
  """

  use Backpex.ResourceAction

  @impl Backpex.ResourceAction
  def label, do: "禁用任务"

  @impl Backpex.ResourceAction
  def icon, do: "hero-x-mark"

  @impl Backpex.ResourceAction
  def confirm_label, do: "确认禁用选中的任务？"

  @impl Backpex.ResourceAction
  def fields, do: []

  @impl Backpex.ResourceAction
  def handle(socket, items, _params) do
    case disable_tasks(items) do
      {:ok, count} ->
        Phoenix.LiveView.put_flash(socket, :info, "成功禁用 #{count} 个任务")

      {:error, reason} ->
        Phoenix.LiveView.put_flash(socket, :error, "禁用任务失败: #{inspect(reason)}")
    end
  end

  defp disable_tasks(items) do
    try do
      count =
        Enum.reduce(items, 0, fn item, acc ->
          case update_task_status(item, :disabled) do
            {:ok, _} -> acc + 1
            {:error, _} -> acc
          end
        end)

      {:ok, count}
    rescue
      e -> {:error, e}
    end
  end

  defp update_task_status(item, status) do
    # 根据不同的资源类型调用相应的更新方法
    case item.__struct__ do
      Teen.ActivitySystem.GameTask ->
        Teen.ActivitySystem.GameTask.disable_task(item)

      Teen.ActivitySystem.WeeklyCard ->
        Teen.ActivitySystem.WeeklyCard.disable_card(item)

      Teen.ActivitySystem.SevenDayTask ->
        Teen.ActivitySystem.SevenDayTask.disable_task(item)

      Teen.ActivitySystem.VipGift ->
        Teen.ActivitySystem.VipGift.disable_gift(item)

      Teen.ActivitySystem.RechargeTask ->
        Teen.ActivitySystem.RechargeTask.disable_task(item)

      Teen.ActivitySystem.RechargeWheel ->
        Teen.ActivitySystem.RechargeWheel.disable_wheel(item)

      Teen.ActivitySystem.ScratchCardActivity ->
        Teen.ActivitySystem.ScratchCardActivity.disable_activity(item)

      Teen.ActivitySystem.FirstRechargeGift ->
        Teen.ActivitySystem.FirstRechargeGift.disable_gift(item)

      Teen.ActivitySystem.LossRebateJar ->
        Teen.ActivitySystem.LossRebateJar.disable_jar(item)

      Teen.ActivitySystem.InviteCashActivity ->
        Teen.ActivitySystem.InviteCashActivity.disable_activity(item)

      Teen.ActivitySystem.BindingReward ->
        Teen.ActivitySystem.BindingReward.disable_reward(item)

      Teen.ActivitySystem.FreeBonusTask ->
        Teen.ActivitySystem.FreeBonusTask.disable_task(item)

      _ ->
        {:error, :unsupported_resource}
    end
  end
end
