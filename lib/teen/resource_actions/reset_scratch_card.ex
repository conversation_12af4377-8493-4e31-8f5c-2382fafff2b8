defmodule Teen.ResourceActions.ResetScratchCard do
  @moduledoc """
  批量解除刮刮卡限制的资源操作
  """

  use Backpex.ResourceAction
  import Phoenix.LiveView, only: [put_flash: 3]

  alias Teen.ActivitySystem.ScratchCardService

  @impl Backpex.ResourceAction
  def title, do: "批量解除刮刮卡限制"

  @impl Backpex.ResourceAction
  def label, do: "解除刮刮卡限制"

  @impl Backpex.ResourceAction
  def fields, do: []

  @impl Backpex.ResourceAction
  def changeset(change, attrs, _metadata \\ []) do
    change
    |> Ecto.Changeset.cast(attrs, [])
  end

  @impl Backpex.ResourceAction
  def handle(socket, _data) do
    %{selected_items: selected_items} = socket.assigns

    # 只处理刮刮卡活动记录
    scratch_card_items = Enum.filter(selected_items, fn item ->
      item.activity_type == :scratch_card
    end)

    if Enum.empty?(scratch_card_items) do
      socket = put_flash(socket, :error, "请选择刮刮卡活动记录")
      {:ok, socket}
    else
      results =
        Enum.map(scratch_card_items, fn item ->
          case ScratchCardService.reset_user_scratch_card_limit(item.user_id) do
            {:ok, _} ->
              {:ok, "用户 #{item.user_id} 的刮刮卡限制已解除"}

            {:error, reason} ->
              {:error, "解除用户 #{item.user_id} 的限制失败: #{inspect(reason)}"}
          end
        end)

      successful = Enum.filter(results, fn {status, _} -> status == :ok end)
      failed = Enum.filter(results, fn {status, _} -> status == :error end)

      socket =
        if length(successful) > 0 do
          put_flash(socket, :info, "成功处理 #{length(successful)} 条记录")
        else
          socket
        end

      socket =
        if length(failed) > 0 do
          put_flash(socket, :error, "失败 #{length(failed)} 条记录")
        else
          socket
        end

      {:ok, socket}
    end
  end
end
