defmodule Teen.ActivitySystem.CdkeyActivity do
  @moduledoc """
  CDKEY活动资源

  管理CDKEY兑换码的生成、分发和使用
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :code, :batch_name, :status, :used_count, :inserted_at]
  end

  postgres do
    table "cdkey_activities"
    repo Cy<PERSON><PERSON>ina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list
    define :by_code, args: [:code]
    define :by_batch, args: [:batch_name]
    define :list_by_batch
    define :list_unused_keys
    define :list_used_keys
    define :use_cdkey
    define :get_usage_statistics
    define :batch_update_validity
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list do
      pagination offset?: true, keyset?: true, required?: false
    end

    read :by_code do
      argument :code, :string, allow_nil?: false
      filter expr(code == ^arg(:code))
    end

    read :by_batch do
      argument :batch_name, :string, allow_nil?: false
      filter expr(batch_name == ^arg(:batch_name))
    end

    read :list_by_batch do
      argument :batch_name, :string, allow_nil?: false
      filter expr(batch_name == ^arg(:batch_name))
    end

    read :list_unused_keys do
      filter expr(status == :active and used_count < max_uses)
    end

    read :list_used_keys do
      filter expr(status == :used_up or used_count >= max_uses)
    end

    update :use_cdkey do
      argument :user_id, :uuid, allow_nil?: false
      change set_attribute(:status, 1)
      change set_attribute(:used_by_user_id, arg(:user_id))
      change set_attribute(:used_at, &DateTime.utc_now/0)
    end

    create :generate_batch do
      argument :batch_name, :string, allow_nil?: false
      argument :reward_config, :map, allow_nil?: false

      change fn changeset, _context ->
        batch_name = Ash.Changeset.get_argument(changeset, :batch_name)
        reward_config = Ash.Changeset.get_argument(changeset, :reward_config)

        # 生成唯一的CDKEY
        cdkey =
          :crypto.strong_rand_bytes(6)
          |> Base.encode32()
          |> String.slice(0, 12)
          |> String.upcase()

        changeset
        |> Ash.Changeset.change_attribute(:batch_name, batch_name)
        |> Ash.Changeset.change_attribute(:code, cdkey)
        |> Ash.Changeset.change_attribute(:reward_type, Map.get(reward_config, "type", :coins))
        |> Ash.Changeset.change_attribute(:reward_amount, Map.get(reward_config, "amount", 0))
        |> Ash.Changeset.change_attribute(:reward_items, Map.get(reward_config, "items", %{}))
        |> Ash.Changeset.change_attribute(:status, :active)
      end
    end

    read :get_usage_statistics do
      argument :batch_name, :string, allow_nil?: true
      argument :date_from, :date, allow_nil?: true
      argument :date_to, :date, allow_nil?: true

      prepare fn query, _context ->
        query
        |> Ash.Query.load([:claim_records])
      end
    end

    update :batch_update_validity do
      argument :batch_name, :string, allow_nil?: false
      argument :new_valid_to, :utc_datetime, allow_nil?: false

      filter expr(batch_name == ^arg(:batch_name))

      change fn changeset, _context ->
        new_valid_to = Ash.Changeset.get_argument(changeset, :new_valid_to)

        changeset
        |> Ash.Changeset.change_attribute(:valid_to, new_valid_to)
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :code, :string do
      allow_nil? false
      public? true
      description "兑换码"
      constraints max_length: 50
    end

    attribute :batch_name, :string do
      allow_nil? false
      public? true
      description "批次名称"
      constraints max_length: 100
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :cash, :items]
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :reward_items, :map do
      allow_nil? true
      public? true
      description "奖励道具"
      default %{}
    end

    attribute :max_uses, :integer do
      allow_nil? false
      public? true
      description "最大使用次数"
      constraints min: 1
      default 1
    end

    attribute :valid_from, :utc_datetime do
      allow_nil? false
      public? true
      description "生效时间"
      default &DateTime.utc_now/0
    end

    attribute :valid_to, :utc_datetime do
      allow_nil? false
      public? true
      description "失效时间"
    end

    attribute :metadata, :map do
      allow_nil? true
      public? true
      description "元数据"
      default %{}
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:active, :inactive, :used_up]
      default :active
    end

    attribute :used_count, :integer do
      allow_nil? false
      public? true
      description "已使用次数"
      default 0
      constraints min: 0
    end

    timestamps()
  end

  relationships do
    belongs_to :used_by_user, Cypridina.Accounts.User do
      public? true
      source_attribute :used_by_user_id
      destination_attribute :id
    end

    has_many :claim_records, Teen.ActivitySystem.CdkeyClaimRecord do
      public? true
      source_attribute :id
      destination_attribute :cdkey_id
    end
  end

  identities do
    identity :unique_code, [:code]
  end
end
