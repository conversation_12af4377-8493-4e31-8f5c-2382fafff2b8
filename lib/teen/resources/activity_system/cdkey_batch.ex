defmodule Teen.ActivitySystem.CdkeyBatch do
  @moduledoc """
  CDKEY批次管理资源

  管理CDKEY批次信息，包括：
  - 批次基本信息
  - 生成统计
  - 使用统计
  - 批次状态管理
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :batch_name, :template_name, :total_count, :used_count, :status, :inserted_at]
  end

  postgres do
    table "cdkey_batches"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list
    define :by_batch_name, args: [:batch_name]
    define :by_template, args: [:template_id]
    define :active_batches
    define :get_statistics
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list do
      pagination offset?: true, keyset?: true, required?: false
    end

    read :by_batch_name do
      argument :batch_name, :string, allow_nil?: false
      filter expr(batch_name == ^arg(:batch_name))
    end

    read :by_template do
      argument :template_id, :uuid, allow_nil?: false
      filter expr(template_id == ^arg(:template_id))
    end

    read :active_batches do
      filter expr(status == :active)
    end

    read :get_statistics do
      argument :batch_id, :uuid, allow_nil?: false
      filter expr(id == ^arg(:batch_id))

      prepare fn query, _context ->
        query
        |> Ash.Query.load([:cdkeys])
      end
    end

    update :update_statistics do
      accept []

      change fn changeset, context ->
        batch = changeset.data

        # 重新计算统计信息
        case Teen.ActivitySystem.CdkeyActivity.by_batch(batch.batch_name) do
          {:ok, cdkeys} ->
            total_count = length(cdkeys)
            used_count = Enum.count(cdkeys, fn c -> c.used_count > 0 end)

            expired_count =
              Enum.count(cdkeys, fn c ->
                DateTime.compare(DateTime.utc_now(), c.valid_to) == :gt
              end)

            changeset
            |> Ash.Changeset.change_attribute(:total_count, total_count)
            |> Ash.Changeset.change_attribute(:used_count, used_count)
            |> Ash.Changeset.change_attribute(:expired_count, expired_count)
            |> Ash.Changeset.change_attribute(:last_updated_at, DateTime.utc_now())

          _ ->
            changeset
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :batch_name, :string do
      allow_nil? false
      public? true
      description "批次名称"
      constraints max_length: 100
    end

    attribute :template_name, :string do
      allow_nil? false
      public? true
      description "模板名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "批次描述"
      constraints max_length: 500
    end

    attribute :total_count, :integer do
      allow_nil? false
      public? true
      description "总数量"
      default 0
      constraints min: 0
    end

    attribute :used_count, :integer do
      allow_nil? false
      public? true
      description "已使用数量"
      default 0
      constraints min: 0
    end

    attribute :expired_count, :integer do
      allow_nil? false
      public? true
      description "已过期数量"
      default 0
      constraints min: 0
    end

    attribute :valid_from, :utc_datetime do
      allow_nil? false
      public? true
      description "生效时间"
      default &DateTime.utc_now/0
    end

    attribute :valid_to, :utc_datetime do
      allow_nil? false
      public? true
      description "失效时间"
    end

    attribute :creator_id, :uuid do
      allow_nil? true
      public? true
      description "创建者ID"
    end

    attribute :creator_name, :string do
      allow_nil? true
      public? true
      description "创建者名称"
      constraints max_length: 100
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:active, :inactive, :expired, :archived]
      default :active
    end

    attribute :generation_config, :map do
      allow_nil? true
      public? true
      description "生成时的配置快照"
      default %{}
    end

    attribute :last_updated_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后更新时间"
    end

    attribute :metadata, :map do
      allow_nil? true
      public? true
      description "元数据"
      default %{}
    end

    timestamps()
  end

  relationships do
    belongs_to :template, Teen.ActivitySystem.CdkeyTemplate do
      public? true
      source_attribute :template_id
      destination_attribute :id
    end

    belongs_to :creator, Cypridina.Accounts.User do
      public? true
      source_attribute :creator_id
      destination_attribute :id
    end

    has_many :cdkeys, Teen.ActivitySystem.CdkeyActivity do
      public? true
      source_attribute :batch_name
      destination_attribute :batch_name
    end
  end

  identities do
    identity :unique_batch_name, [:batch_name]
  end
end
