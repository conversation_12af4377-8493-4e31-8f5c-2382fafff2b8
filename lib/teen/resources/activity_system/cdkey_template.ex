defmodule Teen.ActivitySystem.CdkeyTemplate do
  @moduledoc """
  CDKEY模板配置资源

  管理CDKEY模板配置，包括：
  - 模板基本信息
  - 兑换条件配置
  - 奖励配置模板
  - 生成规则配置
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :name, :template_type, :status, :max_generate_count, :inserted_at]
  end

  postgres do
    table "cdkey_templates"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list
    define :by_type, args: [:template_type]
    define :active_templates
    define :generate_from_template
  end

  actions do
    defaults [:create, :read, :update, :destroy]

    read :list do
      pagination offset?: true, keyset?: true, required?: false
    end

    read :by_type do
      argument :template_type, :atom, allow_nil?: false
      filter expr(template_type == ^arg(:template_type))
    end

    read :active_templates do
      filter expr(status == :active)
    end

    create :generate_from_template do
      argument :count, :integer, allow_nil?: false
      argument :prefix, :string, allow_nil?: true

      change fn changeset, _context ->
        count = Ash.Changeset.get_argument(changeset, :count)
        prefix = Ash.Changeset.get_argument(changeset, :prefix)

        # 验证生成数量限制
        template = changeset.data
        if template.max_generate_count && template.generated_count + count > template.max_generate_count do
          Ash.Changeset.add_error(changeset, field: :count, message: "超过最大生成数量限制")
        else
          changeset
          |> Ash.Changeset.change_attribute(:generated_count, template.generated_count + count)
          |> Ash.Changeset.change_attribute(:last_generated_at, DateTime.utc_now())
        end
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true
      description "模板名称"
      constraints max_length: 100
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "模板描述"
      constraints max_length: 500
    end

    attribute :template_type, :atom do
      allow_nil? false
      public? true
      description "模板类型"
      constraints one_of: [:general, :newbie, :vip, :event, :compensation]
      default :general
    end

    attribute :code_format, :map do
      allow_nil? false
      public? true
      description "兑换码格式配置"
      default %{
        "length" => 12,
        "prefix" => "",
        "suffix" => "",
        "separator" => "-",
        "charset" => "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"
      }
    end

    attribute :reward_config, :map do
      allow_nil? false
      public? true
      description "奖励配置"
      default %{
        "type" => "coins",
        "amount" => 0,
        "items" => %{},
        "vip_exp" => 0
      }
    end

    attribute :usage_conditions, :map do
      allow_nil? false
      public? true
      description "使用条件配置"
      default %{
        "min_level" => 1,
        "max_level" => nil,
        "vip_level_required" => nil,
        "registration_days_min" => nil,
        "registration_days_max" => nil,
        "max_uses_per_user" => 1,
        "cooldown_hours" => 24
      }
    end

    attribute :generation_config, :map do
      allow_nil? false
      public? true
      description "生成配置"
      default %{
        "max_uses" => 1,
        "default_validity_hours" => 720,
        "batch_size_limit" => 10000
      }
    end

    attribute :max_generate_count, :integer do
      allow_nil? true
      public? true
      description "最大生成数量限制"
      constraints min: 1
    end

    attribute :generated_count, :integer do
      allow_nil? false
      public? true
      description "已生成数量"
      default 0
      constraints min: 0
    end

    attribute :last_generated_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后生成时间"
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:active, :inactive, :archived]
      default :active
    end

    attribute :metadata, :map do
      allow_nil? true
      public? true
      description "元数据"
      default %{}
    end

    timestamps()
  end

  identities do
    identity :unique_name, [:name]
  end
end
