defmodule Teen.ActivitySystem.FirstRechargeGift do
  @moduledoc """
  首充礼包资源（Sale）

  管理新手福利首充礼包配置
  根据用户注册天数限制，提供首充奖励
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :title, :limit_days, :reward_coins, :status, :updated_at]
  end

  postgres do
    table "first_recharge_gifts"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_gifts
    define :get_by_user_days
    define :enable_gift
    define :disable_gift
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:title, :limit_days, :reward_coins, :status]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:status, :enabled)
      end
    end

    read :list_active_gifts do
      filter expr(status == :enabled)
    end

    read :get_by_user_days do
      argument :user_days, :integer, allow_nil?: false
      filter expr(limit_days >= ^arg(:user_days) and status == :enabled)
    end

    update :enable_gift do
      require_atomic? false
      change set_attribute(:status, :enabled)
    end

    update :disable_gift do
      require_atomic? false
      change set_attribute(:status, :disabled)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
      description "标题"
      constraints max_length: 100
    end

    attribute :limit_days, :integer do
      allow_nil? false
      public? true
      description "限制天数（注册天数）"
      constraints min: 1
      default 7
    end

    attribute :reward_coins, :decimal do
      allow_nil? false
      public? true
      description "获得金币（分）"
      constraints min: Decimal.new("0")
    end

    attribute :status, :atom do
      allow_nil? false
      public? true
      description "状态"
      constraints one_of: [:enabled, :disabled]
      default :enabled
    end

    timestamps()
  end
end
