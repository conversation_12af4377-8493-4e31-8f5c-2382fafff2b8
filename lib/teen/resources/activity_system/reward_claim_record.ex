defmodule Teen.ActivitySystem.RewardClaimRecord do
  @moduledoc """
  奖励领取记录资源

  记录用户领取各种活动奖励的详细情况
  包括奖励类型、奖励金额、领取时间等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ActivitySystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :user_id,
      :activity_type,
      :activity_id,
      :reward_type,
      :reward_amount,
      :claimed_at
    ]
  end

  postgres do
    table "reward_claim_records"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_user
    define :list_by_activity
    define :get_user_total_rewards
    define :get_activity_stats
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:user_id, :activity_type, :activity_id, :reward_type, :reward_amount, :reward_data]

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:claimed_at, DateTime.utc_now())
      end
    end

    read :list_by_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
      prepare build(sort: [desc: :inserted_at])
    end

    read :list_by_activity do
      argument :activity_type, :atom, allow_nil?: false
      argument :activity_id, :uuid, allow_nil?: true

      filter expr(
               activity_type == ^arg(:activity_type) and
                 (is_nil(^arg(:activity_id)) or activity_id == ^arg(:activity_id))
             )
    end

    read :get_user_total_rewards do
      argument :user_id, :uuid, allow_nil?: false
      argument :start_date, :date, allow_nil?: true
      argument :end_date, :date, allow_nil?: true
      filter expr(user_id == ^arg(:user_id))
    end

    read :get_activity_stats do
      argument :activity_type, :atom, allow_nil?: false
      argument :start_date, :date, allow_nil?: true
      argument :end_date, :date, allow_nil?: true
      filter expr(activity_type == ^arg(:activity_type))
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :user_id, :uuid do
      allow_nil? false
      public? true
      description "用户ID"
    end

    attribute :activity_type, :atom do
      allow_nil? false
      public? true
      description "活动类型"

      constraints one_of: [
                    :game_task,
                    :weekly_card,
                    :seven_day_task,
                    :vip_gift,
                    :recharge_task,
                    :recharge_wheel,
                    :scratch_card,
                    :first_recharge_gift,
                    :loss_rebate_jar,
                    :invite_cash,
                    :binding_reward,
                    :free_bonus_task,
                    :cdkey_activity,
                    :sign_in_activity
                  ]
    end

    attribute :activity_id, :uuid do
      allow_nil? true
      public? true
      description "活动ID"
    end

    attribute :reward_type, :atom do
      allow_nil? false
      public? true
      description "奖励类型"
      constraints one_of: [:coins, :cash, :items, :points]
    end

    attribute :reward_amount, :decimal do
      allow_nil? false
      public? true
      description "奖励金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :reward_data, :map do
      allow_nil? true
      public? true
      description "奖励数据（JSON）"
    end

    attribute :claimed_at, :utc_datetime do
      allow_nil? false
      public? true
      description "领取时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Cypridina.Accounts.User do
      public? true
      source_attribute :user_id
      destination_attribute :id
    end
  end
end
