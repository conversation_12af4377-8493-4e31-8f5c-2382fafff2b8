defmodule Teen.GameManagement.ManageGameConfig do
  @moduledoc """
  游戏配置资源 - 对应sitegamelist1数据结构
  管理游戏的基本信息、状态和显示配置
  """
  use Ash.Resource,
    domain: Teen.GameManagement,
    data_layer: AshPostgres.DataLayer

  postgres do
    table "game_configs"
    repo Cy<PERSON><PERSON><PERSON>.Repo
  end

  code_interface do
    domain Teen.GameManagement
    define :create, action: :create
    define :update, action: :update
    define :enable, action: :enable
    define :disable, action: :disable
    define :set_status, action: :set_status
    define :destroy, action: :destroy
    define :get_by_id, action: :read, get_by: [:id]
    define :get_by_game_id, action: :read, get_by: [:game_id]
    define :list_enabled, action: :list_enabled
    define :list_by_status, action: :list_by_status
  end

  actions do
    defaults [:read]

    create :create do
      accept [
        :game_id,
        :game_name,
        :display_name,
        :status,
        :mode,
        :icon_url,
        :description,
        :close_notice,
        :display_order,
        :is_enabled,
        :game_class_type,
        :created_by
      ]
    end

    update :update do
      accept [
        :game_name,
        :display_name,
        :status,
        :mode,
        :icon_url,
        :description,
        :close_notice,
        :display_order,
        :is_enabled,
        :game_class_type,
        :updated_by
      ]
    end

    update :enable do
      accept []
      change set_attribute(:is_enabled, true)
      change set_attribute(:updated_by, arg(:actor_id))
    end

    update :disable do
      accept []
      change set_attribute(:is_enabled, false)
      change set_attribute(:updated_by, arg(:actor_id))
    end

    update :set_status do
      accept [:status]
      change set_attribute(:updated_by, arg(:actor_id))
    end

    destroy :destroy

    read :list_enabled do
      filter expr(is_enabled == true)
    end

    read :list_by_status do
      argument :status, :integer, allow_nil?: false
      filter expr(status == ^arg(:status))
    end
  end

  preparations do
    prepare build(sort: [display_order: :asc, game_id: :asc])
  end

  validations do
    validate present([:game_id, :game_name, :display_name])
    validate one_of(:status, [0, 1, 2, 4])
  end

  attributes do
    uuid_primary_key :id

    attribute :game_id, :integer do
      allow_nil? false
      description "游戏ID，对应前端Config.GAME_LIST中的gameId"
    end

    attribute :game_name, :string do
      allow_nil? false
      description "游戏名称，对应前端bundleName"
    end

    attribute :display_name, :string do
      allow_nil? false
      description "显示名称，用于前端展示"
    end

    attribute :status, :integer do
      allow_nil? false
      default 2
      description "游戏状态: 0-停用, 1-维护, 2-正常运行, 4-即将开放"
    end

    attribute :mode, :string do
      allow_nil? false
      default ""
      description "游戏模式"
    end

    attribute :icon_url, :string do
      description "游戏图标URL"
    end

    attribute :description, :string do
      description "游戏描述"
    end

    attribute :close_notice, :string do
      description "游戏关闭提示信息"
    end

    attribute :display_order, :integer do
      allow_nil? false
      default 0
      description "显示顺序，数字越小越靠前"
    end

    attribute :is_enabled, :boolean do
      allow_nil? false
      default true
      description "是否启用"
    end

    attribute :game_class_type, :integer do
      allow_nil? false
      default 1
      description "游戏分类类型: 1-单人游戏, 2-多人游戏, 3-百人游戏"
    end

    attribute :created_by, :uuid do
      description "创建者用户ID"
    end

    attribute :updated_by, :uuid do
      description "更新者用户ID"
    end

    create_timestamp :inserted_at
    update_timestamp :updated_at
  end

  relationships do
    has_many :room_configs, Teen.GameManagement.LeveRoomConfig do
      destination_attribute :game_config_id
    end

    belongs_to :creator, Cypridina.Accounts.User do
      source_attribute :created_by
      destination_attribute :id
    end

    belongs_to :updater, Cypridina.Accounts.User do
      source_attribute :updated_by
      destination_attribute :id
    end
  end

  identities do
    identity :unique_game_id, [:game_id]
  end
end
