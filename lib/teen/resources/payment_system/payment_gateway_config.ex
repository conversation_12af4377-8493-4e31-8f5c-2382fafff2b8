defmodule Teen.PaymentSystem.PaymentGatewayConfig do
  @moduledoc """
  支付网关配置资源

  存储支付网关的配置信息，包括：
  - 网关基本信息
  - 商户配置
  - 通道配置
  - 支持的功能
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.PaymentSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :gateway_name, :gateway_code, :merchant_id, :status]
  end

  postgres do
    table "payment_gateway_configs"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active
    define :get_by_code
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [
        :gateway_name,
        :gateway_code,
        :merchant_id,
        :merchant_key,
        :gateway_url,
        :create_order_path,
        :query_order_path,
        :callback_ip,
        :recharge_channel,
        :withdrawal_channel,
        :supported_currencies,
        :supported_payment_methods,
        :min_amount,
        :max_amount,
        :fee_rate,
        :config_data,
        :status
      ]
    end

    read :list_active do
      filter expr(status == "active")
    end

    read :get_by_code do
      argument :gateway_code, :string, allow_nil?: false
      filter expr(gateway_code == ^arg(:gateway_code))
      get? true
    end

    update :activate do
      require_atomic? false
      change set_attribute(:status, "active")
    end

    update :deactivate do
      require_atomic? false
      change set_attribute(:status, "inactive")
    end

    update :update_config do
      require_atomic? false
      accept [:config_data]
    end
  end

  validations do
    validate present(:gateway_name), message: "网关名称不能为空"
    validate present(:gateway_code), message: "网关代码不能为空"
    validate present(:merchant_id), message: "商户ID不能为空"
    validate present(:merchant_key), message: "商户密钥不能为空"
    validate present(:gateway_url), message: "网关地址不能为空"
    validate present(:callback_ip), message: "回调IP不能为空"

    validate compare(:min_amount, less_than_or_equal_to: :max_amount),
      message: "最小金额不能大于最大金额"

    validate compare(:fee_rate, greater_than_or_equal_to: 0),
      message: "手续费率不能为负数"

    validate compare(:fee_rate, less_than_or_equal_to: 1),
      message: "手续费率不能大于100%"
  end

  attributes do
    uuid_primary_key :id

    attribute :gateway_name, :string do
      allow_nil? false
      public? true
      description "网关名称"
      constraints max_length: 100
    end

    attribute :gateway_code, :string do
      allow_nil? false
      public? true
      description "网关代码"
      constraints max_length: 50
    end

    attribute :merchant_id, :string do
      allow_nil? false
      public? true
      description "商户ID"
      constraints max_length: 100
    end

    attribute :merchant_key, :string do
      allow_nil? false
      public? true
      description "商户密钥"
      constraints max_length: 255
      sensitive? true
    end

    attribute :gateway_url, :string do
      allow_nil? false
      public? true
      description "网关地址"
      constraints max_length: 255
    end

    attribute :create_order_path, :string do
      allow_nil? false
      public? true
      description "创建订单路径"
      constraints max_length: 255
    end

    attribute :query_order_path, :string do
      allow_nil? false
      public? true
      description "查询订单路径"
      constraints max_length: 255
    end

    attribute :callback_ip, :string do
      allow_nil? false
      public? true
      description "回调IP白名单"
      constraints max_length: 50
    end

    attribute :recharge_channel, :string do
      allow_nil? false
      public? true
      description "充值通道ID"
      constraints max_length: 50
    end

    attribute :withdrawal_channel, :string do
      allow_nil? false
      public? true
      description "提现通道ID"
      constraints max_length: 50
    end

    attribute :supported_currencies, {:array, :string} do
      allow_nil? false
      public? true
      description "支持的货币"
      default %{}
    end

    attribute :supported_payment_methods, {:array, :string} do
      allow_nil? false
      public? true
      description "支持的支付方式"
      default %{}
    end

    attribute :min_amount, :decimal do
      allow_nil? false
      public? true
      description "最小金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :max_amount, :decimal do
      allow_nil? false
      public? true
      description "最大金额（分）"
      constraints min: Decimal.new("0")
      default Decimal.new("0")
    end

    attribute :fee_rate, :decimal do
      allow_nil? false
      public? true
      description "手续费率"
      constraints min: Decimal.new("0"), max: Decimal.new("1")
      default Decimal.new("0")
    end

    attribute :status, :string do
      allow_nil? false
      public? true
      description "状态"
      constraints max_length: 20
      default "active"
    end

    attribute :config_data, :map do
      allow_nil? false
      public? true
      description "扩展配置数据"
      default %{}
    end

    timestamps()
  end

  calculations do
    calculate :is_active, :boolean do
      public? true
      description "是否激活"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          record.status == "active"
        end)
      end
    end

    calculate :display_name, :string do
      public? true
      description "显示名称"

      calculation fn records, _context ->
        Enum.map(records, fn record ->
          "#{record.gateway_name} (#{record.gateway_code})"
        end)
      end
    end
  end

  identities do
    identity :unique_gateway_code, [:gateway_code]
  end
end
