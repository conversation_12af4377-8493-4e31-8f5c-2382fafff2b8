defmodule Teen.PaymentSystem.CompleteWithdrawalReactor do
  @moduledoc """
  基于 Reactor 的提现支付执行工作流

  处理审核通过后的提现支付流程：
  - 验证提现申请状态（必须已审核通过）
  - 从待审核账户转移积分到已支付账户（成功时）
  - 调用支付网关处理实际支付
  - 更新提现记录状态
  - 处理支付失败时的积分回滚
  
  注意：此Reactor在新流程中不再直接扣除用户余额，
  因为积分已在创建时扣除到待审核账户
  """
  use Ash.Reactor

  require Logger

  # 定义输入参数
  input :withdrawal_id
  input :gateway_config

  # 步骤1: 验证提现申请状态
  step :validate_withdrawal do
    argument :withdrawal_id, input(:withdrawal_id)

    run fn %{withdrawal_id: withdrawal_id}, _context ->
      Logger.info("💸 [PAYMENT_REACTOR] 验证提现申请: #{withdrawal_id}")

      with {:ok, withdrawal} <- Teen.PaymentSystem.WithdrawalRecord.read(withdrawal_id),
           :ok <- validate_withdrawal_status(withdrawal),
           :ok <- validate_user_permissions(withdrawal) do
        Logger.info("💸 [PAYMENT_REACTOR] 提现申请验证成功: #{withdrawal.order_id}")
        {:ok, withdrawal}
      else
        {:error, reason} ->
          Logger.error("💸 [PAYMENT_REACTOR] 提现申请验证失败: #{inspect(reason)}")
          {:error, "Withdrawal validation failed: #{inspect(reason)}"}
      end
    end
  end

  # 步骤2: 验证待审核账户余额
  step :verify_pending_balance do
    argument :withdrawal, result(:validate_withdrawal)

    run fn %{withdrawal: withdrawal}, _context ->
      Logger.info("💸 [PAYMENT_REACTOR] 验证待审核账户余额")

      pending_identifier = Cypridina.Ledger.AccountIdentifier.system(:withdrawal_pending, :XAA)
      withdrawal_amount = Decimal.to_integer(withdrawal.withdrawal_amount)

      # 这一步主要是为了确保数据一致性
      # 正常情况下待审核账户应该有足够的余额
      {:ok, %{
        pending_identifier: pending_identifier,
        withdrawal_amount: withdrawal_amount
      }}
    end
  end

  # 步骤3: 选择支付网关
  step :select_gateway do
    argument :withdrawal, result(:validate_withdrawal)
    argument :gateway_config, input(:gateway_config)

    run fn %{withdrawal: withdrawal, gateway_config: gateway_config}, _context ->
      Logger.info("💸 [PAYMENT_REACTOR] 选择支付网关")

      case gateway_config || select_withdrawal_gateway(withdrawal) do
        {:ok, config} ->
          Logger.info("💸 [PAYMENT_REACTOR] 网关选择成功: #{config.gateway_name}")
          {:ok, config}

        {:error, reason} ->
          Logger.error("💸 [PAYMENT_REACTOR] 网关选择失败: #{inspect(reason)}")
          {:error, "Failed to select gateway: #{inspect(reason)}"}

        config when not is_nil(config) ->
          {:ok, config}

        _ ->
          {:error, "No gateway configuration available"}
      end
    end
  end

  # 步骤4: 提交到支付网关
  step :submit_to_gateway do
    argument :withdrawal, result(:validate_withdrawal)
    argument :gateway_config, result(:select_gateway)

    run fn %{withdrawal: withdrawal, gateway_config: gateway_config}, _context ->
      Logger.info("💸 [PAYMENT_REACTOR] 提交到支付网关: #{gateway_config.gateway_name}")

      case submit_withdrawal_to_gateway(withdrawal, gateway_config) do
        {:ok, gateway_response} ->
          Logger.info("💸 [PAYMENT_REACTOR] 网关提交成功: #{inspect(gateway_response)}")
          {:ok, gateway_response}

        {:error, reason} ->
          Logger.error("💸 [PAYMENT_REACTOR] 网关提交失败: #{inspect(reason)}")
          {:error, "Gateway submission failed: #{inspect(reason)}"}
      end
    end
  end

  # 步骤5: 处理网关失败（如果发生）
  step :handle_gateway_failure do
    argument :withdrawal, result(:validate_withdrawal)
    argument :balance_info, result(:verify_pending_balance)
    argument :gateway_result, result(:submit_to_gateway)

    run fn args, _context ->
      case args.gateway_result do
        {:error, _reason} ->
          Logger.info("💸 [PAYMENT_REACTOR] 处理网关失败，准备回滚积分")
          
          # 从待审核账户退回到用户账户
          pending_identifier = args.balance_info.pending_identifier
          user_identifier = Cypridina.Ledger.AccountIdentifier.user(args.withdrawal.user_id, :XAA)
          
          case Cypridina.Ledger.transfer(
            pending_identifier,
            user_identifier,
            args.balance_info.withdrawal_amount,
            transaction_type: :refund,
            description: "提现支付失败退款: #{args.withdrawal.order_id}",
            metadata: %{
              withdrawal_id: args.withdrawal.id,
              reason: "payment_gateway_failure"
            }
          ) do
            {:ok, refund_transfer} ->
              # 更新提现状态为失败
              Teen.PaymentSystem.WithdrawalRecord.update_progress(args.withdrawal, %{
                progress_status: 3,  # 支付失败
                result_status: 2,    # 失败
                feedback: "支付网关处理失败，积分已退回"
              })
              
              {:ok, %{refunded: true, transfer: refund_transfer}}
            
            {:error, refund_error} ->
              Logger.error("💸 [PAYMENT_REACTOR] 积分回滚失败: #{inspect(refund_error)}")
              {:error, "Failed to refund balance: #{inspect(refund_error)}"}
          end
        
        _ ->
          {:ok, :skip}
      end
    end
  end

  # 步骤6: 更新提现记录状态（成功情况）
  step :update_withdrawal_status do
    argument :withdrawal, result(:validate_withdrawal)
    argument :gateway_response, result(:submit_to_gateway)
    argument :failure_handled, result(:handle_gateway_failure)

    run fn args, _context ->
      # 如果已经处理了失败，跳过此步骤
      case args.failure_handled do
        %{refunded: true} ->
          {:ok, :already_handled}
        
        _ ->
          Logger.info("💸 [PAYMENT_REACTOR] 更新提现记录状态为处理中")
          
          case update_withdrawal_progress(args.withdrawal, args.gateway_response) do
            {:ok, updated_withdrawal} ->
              Logger.info("💸 [PAYMENT_REACTOR] 状态更新成功")
              {:ok, updated_withdrawal}

            {:error, reason} ->
              Logger.error("💸 [PAYMENT_REACTOR] 状态更新失败: #{inspect(reason)}")
              {:error, "Failed to update withdrawal status: #{inspect(reason)}"}
          end
      end
    end
  end

  # 步骤7: 发布事件
  step :publish_event do
    argument :withdrawal, result(:validate_withdrawal)
    argument :gateway_response, result(:submit_to_gateway)
    argument :status_update, result(:update_withdrawal_status)

    run fn args, _context ->
      Logger.info("💸 [PAYMENT_REACTOR] 发布支付提交事件")
      
      event_type = case args.gateway_response do
        {:ok, _} -> :withdrawal_payment_submitted
        {:error, _} -> :withdrawal_payment_failed
      end
      
      Phoenix.PubSub.broadcast(
        Cypridina.PubSub,
        "withdrawal:#{args.withdrawal.user_id}",
        %{
          event: event_type,
          withdrawal_id: args.withdrawal.id,
          order_id: args.withdrawal.order_id,
          user_id: args.withdrawal.user_id,
          amount: args.withdrawal.withdrawal_amount,
          payment_method: args.withdrawal.payment_method,
          gateway_response: args.gateway_response,
          timestamp: DateTime.utc_now()
        }
      )
      
      {:ok, :event_published}
    end
  end

  # 私有辅助函数
  defp validate_withdrawal_status(withdrawal) do
    cond do
      withdrawal.audit_status != 1 ->
        {:error, "提现未审核通过，当前状态: #{audit_status_text(withdrawal.audit_status)}"}

      withdrawal.progress_status != 0 ->
        {:error, "提现已在处理中，当前进度: #{progress_status_text(withdrawal.progress_status)}"}

      withdrawal.result_status != 0 ->
        {:error, "提现已完成，结果: #{result_status_text(withdrawal.result_status)}"}

      true ->
        :ok
    end
  end

  defp validate_user_permissions(_withdrawal) do
    # 可以在这里添加额外的验证逻辑
    :ok
  end

  defp select_withdrawal_gateway(withdrawal) do
    Teen.PaymentSystem.GatewaySelector.select_withdrawal_gateway(
      withdrawal.payment_method,
      withdrawal.withdrawal_amount
    )
  end

  defp submit_withdrawal_to_gateway(withdrawal, gateway_config) do
    params = build_gateway_params(withdrawal, gateway_config)
    
    # 调用实际的支付网关API
    case call_payment_gateway(gateway_config, params) do
      {:ok, response} ->
        {:ok, response}
      
      {:error, reason} ->
        {:error, reason}
    end
  end

  defp build_gateway_params(withdrawal, gateway_config) do
    %{
      order_id: withdrawal.order_id,
      amount: Decimal.to_string(withdrawal.withdrawal_amount),
      actual_amount: Decimal.to_string(withdrawal.actual_amount || withdrawal.withdrawal_amount),
      payment_method: withdrawal.payment_method,
      bank_info: withdrawal.bank_info,
      alipay_info: withdrawal.alipay_info,
      upi_info: withdrawal.upi_info,
      callback_url: "#{gateway_config.callback_url}/withdrawal/callback",
      merchant_id: gateway_config.merchant_id,
      notify_url: gateway_config.notify_url
    }
  end

  defp call_payment_gateway(gateway_config, params) do
    # 根据不同的网关调用相应的API
    case gateway_config.gateway_name do
      "YidunPay" ->
        Teen.Services.PaymentGateway.YidunPay.withdrawal(params, gateway_config)
      
      "SanQiPay" ->
        Teen.Services.PaymentGateway.SanQiPay.withdrawal(params, gateway_config)
      
      "DuoDuoPay" ->
        Teen.Services.PaymentGateway.DuoDuoPay.withdrawal(params, gateway_config)
      
      _ ->
        # 开发环境模拟
        simulate_gateway_call(gateway_config, params)
    end
  end

  defp simulate_gateway_call(_gateway_config, params) do
    # 模拟网关响应
    {:ok, %{
      status: "processing",
      gateway_order_id: "GW#{System.unique_integer([:positive])}",
      message: "Withdrawal request submitted successfully",
      order_id: params.order_id,
      processing_time: "1-2 business days"
    }}
  end

  defp update_withdrawal_progress(withdrawal, gateway_response) do
    Teen.PaymentSystem.WithdrawalRecord.update_progress(withdrawal, %{
      progress_status: 1,  # 处理中
      gateway_response: Jason.encode!(gateway_response),
      gateway_order_id: gateway_response[:gateway_order_id] || gateway_response["gateway_order_id"]
    })
  end

  # 状态文本辅助函数
  defp audit_status_text(0), do: "待审核"
  defp audit_status_text(1), do: "已通过"
  defp audit_status_text(2), do: "已拒绝"
  defp audit_status_text(_), do: "未知"

  defp progress_status_text(0), do: "待处理"
  defp progress_status_text(1), do: "处理中"
  defp progress_status_text(2), do: "支付成功"
  defp progress_status_text(3), do: "支付失败"
  defp progress_status_text(_), do: "未知"

  defp result_status_text(0), do: "进行中"
  defp result_status_text(1), do: "成功"
  defp result_status_text(2), do: "失败"
  defp result_status_text(_), do: "未知"
end