defmodule Teen.PaymentSystem.CreateWithdrawalReactor do
  @moduledoc """
  处理提现申请创建的Reactor

  主要职责：
  1. 验证用户提现资格（余额、流水要求等）
  2. 创建提现记录
  3. 将积分从用户账户转入待审核账户
  4. 发布创建事件

  补偿机制：
  - 如果积分扣除失败，自动删除创建的提现记录
  - 如果任何步骤失败，确保数据一致性
  """
  use Ash.Reactor

  require Logger

  # 输入参数
  input :user_id
  input :withdrawal_amount
  input :payment_method
  input :bank_info
  input :alipay_info
  input :upi_info
  input :ip_address

  # 步骤1: 验证用户提现资格
  step :validate_eligibility do
    argument :user_id, input(:user_id)
    argument :amount, input(:withdrawal_amount)

    run fn %{user_id: user_id, amount: amount}, _context ->
      Logger.info("💸 [CREATE_WITHDRAWAL] 验证用户 #{user_id} 提现资格，金额: #{amount}")

      with {:ok, user} <- Cypridina.Accounts.User.read(user_id),
           :ok <- validate_user_status(user),
           {:ok, balance_info} <- get_user_balance(user_id),
           :ok <- validate_balance(balance_info.balance, Decimal.to_integer(amount)),
           {:ok, turnover_info} <- Teen.Services.TurnoverService.get_user_turnover_info(user_id),
           :ok <- validate_turnover(turnover_info, amount) do
        Logger.info("💸 [CREATE_WITHDRAWAL] 用户资格验证通过")

        {:ok,
         %{
           user: user,
           balance_info: balance_info,
           turnover_info: turnover_info
         }}
      else
        {:error, reason} ->
          Logger.error("💸 [CREATE_WITHDRAWAL] 资格验证失败: #{inspect(reason)}")
          {:error, reason}
      end
    end
  end

  # 步骤2: 计算费用和实际到账金额
  step :calculate_fees do
    argument :amount, input(:withdrawal_amount)
    argument :payment_method, input(:payment_method)
    argument :validation_result, result(:validate_eligibility)

    run fn %{amount: amount, payment_method: payment_method, validation_result: %{user: user}},
           _context ->
      Logger.info("💸 [CREATE_WITHDRAWAL] 计算提现费用")

      # 获取VIP等级和费率配置
      vip_level = get_user_vip_level(user.id)

      config =
        Teen.Services.PaymentConfigService.get_payment_method_config(
          payment_method,
          amount,
          vip_level
        )

      fee_info = List.first(config.fee_calculation)
      fee_amount = calculate_fee(amount, fee_info)
      tax_amount = calculate_tax(amount, fee_info)
      actual_amount = Decimal.sub(amount, Decimal.add(fee_amount, tax_amount))

      Logger.info("💸 [CREATE_WITHDRAWAL] 费用计算完成 - 手续费: #{fee_amount}, 税费: #{tax_amount}")

      {:ok,
       %{
         fee_amount: fee_amount,
         tax_amount: tax_amount,
         actual_amount: actual_amount,
         config: config
       }}
    end
  end

  # 步骤3: 创建提现记录
  step :create_withdrawal_record do
    argument :user_id, input(:user_id)
    argument :withdrawal_amount, input(:withdrawal_amount)
    argument :payment_method, input(:payment_method)
    argument :bank_info, input(:bank_info)
    # argument :alipay_info, input(:alipay_info)
    # argument :upi_info, input(:upi_info)
    argument :ip_address, input(:ip_address)
    argument :validation_result, result(:validate_eligibility)
    argument :fee_calculation, result(:calculate_fees)

    run fn args, _context ->
      Logger.info("💸 [CREATE_WITHDRAWAL] 创建提现记录")

      attrs = %{
        user_id: args.user_id,
        withdrawal_amount: args.withdrawal_amount,
        payment_method: args.payment_method,
        bank_info: args.bank_info,
        # alipay_info: args.alipay_info,
        # upi_info: args.upi_info,
        ip_address: args.ip_address,
        current_balance: args.validation_result.balance_info.balance,
        withdrawable_balance: args.validation_result.balance_info.balance,
        required_turnover: args.validation_result.turnover_info.required_turnover,
        completed_turnover: args.validation_result.turnover_info.completed_turnover,
        fee_amount: args.fee_calculation.fee_amount,
        tax_amount: args.fee_calculation.tax_amount,
        actual_amount: args.fee_calculation.actual_amount
      }

      case Teen.PaymentSystem.WithdrawalRecord.create_withdrawal(attrs) do
        {:ok, withdrawal} ->
          Logger.info("💸 [CREATE_WITHDRAWAL] 提现记录创建成功: #{withdrawal.order_id}")
          {:ok, withdrawal}

        {:error, reason} ->
          Logger.error("💸 [CREATE_WITHDRAWAL] 提现记录创建失败: #{inspect(reason)}")
          {:error, reason}
      end
    end
  end

  # 步骤4: 扣除用户积分到待审核账户
  step :deduct_points_to_pending do
    argument :withdrawal, result(:create_withdrawal_record)
    argument :user_id, input(:user_id)

    run fn %{withdrawal: withdrawal, user_id: user_id}, _context ->
      Logger.info("💸 [CREATE_WITHDRAWAL] 扣除用户积分到待审核账户")

      user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)
      pending_identifier = Cypridina.Ledger.AccountIdentifier.system(:withdrawal_pending, :XAA)
      amount = Decimal.to_integer(withdrawal.withdrawal_amount)

      case Cypridina.Ledger.transfer(
             user_identifier,
             pending_identifier,
             amount,
             transaction_type: :withdrawal_request,
             description: "提现申请: #{withdrawal.order_id}",
             metadata: %{
               withdrawal_id: withdrawal.id,
               order_id: withdrawal.order_id,
               status: "pending_audit"
             }
           ) do
        {:ok, transfer} ->
          Logger.info("💸 [CREATE_WITHDRAWAL] 积分扣除成功，交易ID: #{transfer.id}")
          {:ok, %{withdrawal: withdrawal, transfer: transfer}}

        {:error, reason} ->
          Logger.error("💸 [CREATE_WITHDRAWAL] 积分扣除失败: #{inspect(reason)}")
          {:error, reason}
      end
    end
  end

  # 步骤5: 更新提现记录关联交易信息
  step :update_withdrawal_with_transfer do
    argument :deduction_result, result(:deduct_points_to_pending)

    run fn %{deduction_result: %{withdrawal: withdrawal, transfer: transfer}}, _context ->
      Logger.info("💸 [CREATE_WITHDRAWAL] 更新提现记录交易信息")

      case Teen.PaymentSystem.WithdrawalRecord.update(withdrawal, %{
             ledger_transaction_id: transfer.id,
             metadata: %{initial_transfer_id: transfer.id}
           }) do
        {:ok, updated_withdrawal} ->
          {:ok, updated_withdrawal}

        {:error, reason} ->
          Logger.error("💸 [CREATE_WITHDRAWAL] 更新交易信息失败: #{inspect(reason)}")
          # 这个错误不应该阻止流程，只记录日志
          {:ok, withdrawal}
      end
    end
  end

  # 步骤6: 发布创建成功事件
  step :publish_created_event do
    argument :withdrawal, result(:update_withdrawal_with_transfer)

    run fn %{withdrawal: withdrawal}, _context ->
      Logger.info("💸 [CREATE_WITHDRAWAL] 发布创建成功事件")

      Phoenix.PubSub.broadcast(
        Cypridina.PubSub,
        "withdrawal:#{withdrawal.user_id}",
        %{
          event: :withdrawal_created,
          withdrawal_id: withdrawal.id,
          order_id: withdrawal.order_id,
          user_id: withdrawal.user_id,
          amount: withdrawal.withdrawal_amount,
          payment_method: withdrawal.payment_method,
          status: "pending_audit",
          created_at: DateTime.utc_now()
        }
      )

      {:ok, withdrawal}
    end
  end

  # 定义一个错误处理步骤，当积分扣除失败时删除提现记录
  step :handle_deduction_failure do
    argument :withdrawal, result(:create_withdrawal_record)
    argument :deduction_error, result(:deduct_points_to_pending)

    run fn args, _context ->
      case args.deduction_error do
        {:error, _reason} ->
          Logger.info("💸 [CREATE_WITHDRAWAL] 积分扣除失败，删除提现记录")

          case Teen.PaymentSystem.WithdrawalRecord.destroy(args.withdrawal) do
            :ok ->
              Logger.info("💸 [CREATE_WITHDRAWAL] 提现记录删除成功")
              {:error, "提现创建失败：积分扣除失败"}

            {:error, reason} ->
              Logger.error("💸 [CREATE_WITHDRAWAL] 提现记录删除失败: #{inspect(reason)}")
              {:error, "提现创建失败：积分扣除失败且记录清理失败"}
          end

        _ ->
          {:ok, :skip}
      end
    end
  end

  # 私有辅助函数
  defp validate_user_status(user) do
    cond do
      user.status != "active" ->
        {:error, "用户状态异常"}

      user.payment_banned ->
        {:error, "用户支付功能已被限制"}

      true ->
        :ok
    end
  end

  defp get_user_balance(user_id) do
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)

    case Cypridina.Ledger.BalanceCache.get_balance(user_identifier) do
      {:ok, balance} ->
        {:ok, %{balance: balance, identifier: user_identifier}}

      {:error, reason} ->
        {:error, "获取余额失败: #{inspect(reason)}"}
    end
  end

  defp validate_balance(balance, amount) do
    if balance >= amount do
      :ok
    else
      {:error, "余额不足，当前余额: #{balance}，申请金额: #{amount}"}
    end
  end

  defp validate_turnover(turnover_info, amount) do
    if turnover_info.is_eligible do
      :ok
    else
      {:error,
       "流水未达标，需要流水: #{turnover_info.required_turnover}，已完成: #{turnover_info.completed_turnover}"}
    end
  end

  defp get_user_vip_level(user_id) do
    # 从用户信息或VIP系统获取VIP等级
    # 暂时返回默认值
    0
  end

  defp calculate_fee(amount, fee_info) when is_map(fee_info) do
    fee_rate = Map.get(fee_info, :fee_rate, 0.02)
    fee_min = Map.get(fee_info, :fee_min, 0)
    fee_max = Map.get(fee_info, :fee_max, 1000)

    fee = Decimal.mult(amount, Decimal.new(fee_rate))

    fee
    |> Decimal.max(Decimal.new(fee_min))
    |> Decimal.min(Decimal.new(fee_max))
  end

  defp calculate_fee(_amount, _), do: Decimal.new(0)

  defp calculate_tax(amount, fee_info) when is_map(fee_info) do
    tax_rate = Map.get(fee_info, :tax_rate, 0.05)
    Decimal.mult(amount, Decimal.new(tax_rate))
  end

  defp calculate_tax(_amount, _), do: Decimal.new(0)
end
