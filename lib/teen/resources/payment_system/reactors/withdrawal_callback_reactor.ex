defmodule Teen.PaymentSystem.WithdrawalCallbackReactor do
  @moduledoc """
  基于 Reactor 的提现回调处理工作流
  
  处理支付网关的提现回调，根据支付结果执行相应操作：
  - 成功：将积分从待审核账户转移到已支付账户，更新状态
  - 失败：将积分从待审核账户退回用户账户，更新状态
  - 处理中：仅更新状态，等待下次回调
  
  补偿机制：
  - 确保积分转移的原子性
  - 处理异常情况并发送告警
  """
  use Ash.Reactor

  require Logger

  # 定义输入参数
  input :order_id
  input :callback_data
  input :gateway_signature

  # 步骤1: 验证回调数据
  step :validate_callback do
    argument :order_id, input(:order_id)
    argument :callback_data, input(:callback_data)
    argument :gateway_signature, input(:gateway_signature)
    
    run fn %{order_id: order_id, callback_data: callback_data, gateway_signature: signature}, _context ->
      Logger.info("💸 [CALLBACK_REACTOR] 验证回调数据: #{order_id}")
      
      with {:ok, withdrawal} <- get_withdrawal_by_order_id(order_id),
           :ok <- validate_callback_signature(callback_data, signature, withdrawal),
           :ok <- validate_callback_data(callback_data),
           {:ok, status} <- parse_callback_status(callback_data) do
        
        Logger.info("💸 [CALLBACK_REACTOR] 回调验证成功，状态: #{status}")
        {:ok, %{withdrawal: withdrawal, callback_data: callback_data, status: status}}
      else
        {:error, reason} ->
          Logger.error("💸 [CALLBACK_REACTOR] 回调验证失败: #{inspect(reason)}")
          {:error, reason}
      end
    end
  end

  # 步骤2: 根据状态路由处理
  step :route_by_status do
    argument :validation_result, result(:validate_callback)
    
    run fn %{validation_result: %{status: status}}, _context ->
      Logger.info("💸 [CALLBACK_REACTOR] 路由回调状态: #{status}")
      
      case status do
        :success -> {:ok, :route_success}
        :failed -> {:ok, :route_failed}
        :pending -> {:ok, :route_pending}
        _ -> {:error, "未知的回调状态: #{status}"}
      end
    end
  end

  # 步骤3A: 处理成功回调
  step :handle_success do
    argument :route, result(:route_by_status)
    argument :validation_result, result(:validate_callback)
    
    run fn args, _context ->
      if args.route == :route_success do
        %{withdrawal: withdrawal, callback_data: callback_data} = args.validation_result
        Logger.info("💸 [CALLBACK_REACTOR] 处理成功回调: #{withdrawal.order_id}")
        
        # 将积分从待审核账户转移到已支付账户
        pending_identifier = Cypridina.Ledger.AccountIdentifier.system(:withdrawal_pending, :XAA)
        paid_identifier = Cypridina.Ledger.AccountIdentifier.system(:withdrawal_paid, :XAA)
        amount = Decimal.to_integer(withdrawal.withdrawal_amount)
        
        with {:ok, transfer} <- Cypridina.Ledger.transfer(
               pending_identifier,
               paid_identifier,
               amount,
               transaction_type: :withdrawal_completed,
               description: "提现完成: #{withdrawal.order_id}",
               metadata: %{
                 withdrawal_id: withdrawal.id,
                 gateway_order_id: callback_data["gateway_order_id"],
                 completed_at: DateTime.utc_now()
               }
             ),
             {:ok, updated} <- update_withdrawal_success(withdrawal, callback_data) do
          
          Logger.info("💸 [CALLBACK_REACTOR] 成功处理完成")
          {:ok, %{withdrawal: updated, transfer: transfer, processed: true}}
        else
          {:error, reason} ->
            Logger.error("💸 [CALLBACK_REACTOR] 成功处理失败: #{inspect(reason)}")
            {:error, reason}
        end
      else
        {:ok, :skip}
      end
    end
  end

  # 步骤3B: 处理失败回调
  step :handle_failure do
    argument :route, result(:route_by_status)
    argument :validation_result, result(:validate_callback)
    
    run fn args, _context ->
      if args.route == :route_failed do
        %{withdrawal: withdrawal, callback_data: callback_data} = args.validation_result
        Logger.info("💸 [CALLBACK_REACTOR] 处理失败回调: #{withdrawal.order_id}")
        
        # 将积分从待审核账户退回用户账户
        pending_identifier = Cypridina.Ledger.AccountIdentifier.system(:withdrawal_pending, :XAA)
        user_identifier = Cypridina.Ledger.AccountIdentifier.user(withdrawal.user_id, :XAA)
        amount = Decimal.to_integer(withdrawal.withdrawal_amount)
        
        with {:ok, transfer} <- Cypridina.Ledger.transfer(
               pending_identifier,
               user_identifier,
               amount,
               transaction_type: :refund,
               description: "提现失败退款: #{withdrawal.order_id}",
               metadata: %{
                 withdrawal_id: withdrawal.id,
                 reason: callback_data["message"] || "支付失败",
                 refunded_at: DateTime.utc_now()
               }
             ),
             {:ok, updated} <- update_withdrawal_failure(withdrawal, callback_data) do
          
          Logger.info("💸 [CALLBACK_REACTOR] 失败处理完成，积分已退回")
          {:ok, %{withdrawal: updated, transfer: transfer, refunded: true}}
        else
          {:error, reason} ->
            Logger.error("💸 [CALLBACK_REACTOR] 失败处理出错: #{inspect(reason)}")
            {:error, reason}
        end
      else
        {:ok, :skip}
      end
    end
  end

  # 步骤3C: 处理待处理回调
  step :handle_pending do
    argument :route, result(:route_by_status)
    argument :validation_result, result(:validate_callback)
    
    run fn args, _context ->
      if args.route == :route_pending do
        %{withdrawal: withdrawal, callback_data: callback_data} = args.validation_result
        Logger.info("💸 [CALLBACK_REACTOR] 处理待处理回调: #{withdrawal.order_id}")
        
        # 仅更新状态，不移动积分
        case update_withdrawal_pending(withdrawal, callback_data) do
          {:ok, updated} ->
            Logger.info("💸 [CALLBACK_REACTOR] 待处理状态更新成功")
            {:ok, %{withdrawal: updated, status: :pending}}
          
          {:error, reason} ->
            Logger.error("💸 [CALLBACK_REACTOR] 待处理状态更新失败: #{inspect(reason)}")
            {:error, reason}
        end
      else
        {:ok, :skip}
      end
    end
  end

  # 步骤4: 发布回调处理事件
  step :publish_event do
    argument :success_result, result(:handle_success)
    argument :failure_result, result(:handle_failure)
    argument :pending_result, result(:handle_pending)
    
    run fn args, _context ->
      result = args.success_result || args.failure_result || args.pending_result
      
      case result do
        %{withdrawal: withdrawal} = data ->
          Logger.info("💸 [CALLBACK_REACTOR] 发布回调处理事件")
          
          status = cond do
            Map.get(data, :processed) -> :completed
            Map.get(data, :refunded) -> :refunded
            true -> :pending
          end
          
          event_data = %{
            event: :"withdrawal_callback_#{status}",
            withdrawal_id: withdrawal.id,
            order_id: withdrawal.order_id,
            user_id: withdrawal.user_id,
            amount: withdrawal.withdrawal_amount,
            status: status,
            timestamp: DateTime.utc_now()
          }
          
          # 添加特定状态的额外信息
          event_data = case status do
            :refunded -> Map.put(event_data, :refund_reason, withdrawal.feedback)
            :completed -> Map.put(event_data, :completed_at, withdrawal.completed_time)
            _ -> event_data
          end
          
          Phoenix.PubSub.broadcast(
            Cypridina.PubSub,
            "withdrawal:#{withdrawal.user_id}",
            event_data
          )
          
          # 管理员通知
          Phoenix.PubSub.broadcast(
            Cypridina.PubSub,
            "withdrawal:admin",
            Map.put(event_data, :channel, :admin)
          )
          
          {:ok, :event_published}
        
        _ ->
          {:ok, :no_event}
      end
    end
  end

  # 步骤5: 处理异常情况
  step :handle_exceptions do
    argument :success_result, result(:handle_success)
    argument :failure_result, result(:handle_failure)
    
    run fn args, _context ->
      # 检查是否有需要告警的情况
      cond do
        match?({:error, _}, args.success_result) ->
          send_alert("提现成功回调处理失败", args.success_result)
          
        match?({:error, _}, args.failure_result) ->
          send_alert("提现失败回调退款失败", args.failure_result)
          
        true ->
          {:ok, :no_exceptions}
      end
    end
  end

  # 私有辅助函数
  defp get_withdrawal_by_order_id(order_id) do
    case Teen.PaymentSystem.WithdrawalRecord.get_by_order_id(order_id) do
      {:ok, [withdrawal | _]} -> {:ok, withdrawal}
      {:ok, []} -> {:error, "提现记录不存在: #{order_id}"}
      {:error, reason} -> {:error, reason}
    end
  end

  defp validate_callback_signature(_callback_data, nil, _withdrawal) do
    # 开发环境可能没有签名
    :ok
  end

  defp validate_callback_signature(callback_data, signature, withdrawal) do
    # 根据不同网关验证签名
    # 这里应该调用对应网关的签名验证方法
    Logger.info("💸 [CALLBACK_REACTOR] 验证签名: #{signature}")
    :ok
  end

  defp validate_callback_data(callback_data) do
    required_fields = ["order_id", "status"]
    
    missing_fields = 
      required_fields
      |> Enum.reject(fn field -> 
        Map.has_key?(callback_data, field) || Map.has_key?(callback_data, String.to_atom(field))
      end)
    
    if missing_fields == [] do
      :ok
    else
      {:error, "缺少必要字段: #{Enum.join(missing_fields, ", ")}"}
    end
  end

  defp parse_callback_status(callback_data) do
    status = callback_data["status"] || callback_data[:status]
    
    case String.downcase(to_string(status)) do
      s when s in ["success", "completed", "paid"] -> {:ok, :success}
      s when s in ["failed", "failure", "rejected"] -> {:ok, :failed}
      s when s in ["pending", "processing"] -> {:ok, :pending}
      _ -> {:error, "未知状态: #{status}"}
    end
  end

  defp update_withdrawal_success(withdrawal, callback_data) do
    Teen.PaymentSystem.WithdrawalRecord.update(withdrawal, %{
      progress_status: 2,  # 支付成功
      result_status: 1,    # 成功
      gateway_response: Jason.encode!(callback_data),
      completed_time: DateTime.utc_now(),
      gateway_order_id: callback_data["gateway_order_id"] || callback_data[:gateway_order_id],
      actual_paid_amount: callback_data["actual_amount"] || withdrawal.actual_amount
    })
  end

  defp update_withdrawal_failure(withdrawal, callback_data) do
    Teen.PaymentSystem.WithdrawalRecord.update(withdrawal, %{
      progress_status: 3,  # 支付失败
      result_status: 2,    # 失败
      gateway_response: Jason.encode!(callback_data),
      feedback: callback_data["message"] || callback_data[:message] || "支付失败",
      failed_time: DateTime.utc_now()
    })
  end

  defp update_withdrawal_pending(withdrawal, callback_data) do
    Teen.PaymentSystem.WithdrawalRecord.update(withdrawal, %{
      progress_status: 1,  # 处理中
      gateway_response: Jason.encode!(callback_data),
      last_callback_time: DateTime.utc_now()
    })
  end

  defp send_alert(message, error_detail) do
    Logger.error("💸 [CALLBACK_REACTOR] 发送告警: #{message}")
    
    Phoenix.PubSub.broadcast(
      Cypridina.PubSub,
      "system:alerts",
      %{
        alert: :withdrawal_callback_error,
        message: message,
        detail: inspect(error_detail),
        severity: :high,
        timestamp: DateTime.utc_now()
      }
    )
    
    {:ok, :alert_sent}
  end
end