defmodule Teen.PaymentSystem.WithdrawalConfig do
  @moduledoc """
  提现配置资源

  管理提现相关的配置，包括：
  - 提现限额和费率
  - 支付方式配置
  - VIP等级要求
  - 流水倍数要求
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.PaymentSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :config_name,
      :payment_method,
      :min_amount,
      :max_amount,
      :fee_rate,
      :status
    ]
  end

  postgres do
    table "withdrawal_configs"
    repo Cypridina.Repo
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_active_configs
    define :list_by_payment_method
    define :get_by_name
    define :enable
    define :disable
    define :get_config_for_withdrawal
    define :validate_withdrawal_amount
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [
        :config_name,
        :payment_method,
        :min_amount,
        :max_amount,
        :daily_limit,
        :monthly_limit,
        :fee_rate,
        :tax_rate,
        :min_fee_amount,
        :max_fee_amount,
        :status,
        :vip_level_required,
        :turnover_multiplier,
        :min_deposit_required,
        :processing_time_hours,
        :auto_approve_limit,
        :gateway_channel_id,
        :description,
        :business_hours_only,
        :weekend_processing,
        :vip_fee_discount,
        :vip_processing_priority
      ]
    end

    read :list_active_configs do
      filter expr(status == 1)
    end

    read :list_by_payment_method do
      argument :payment_method, :string, allow_nil?: false
      filter expr(payment_method == ^arg(:payment_method) and status == 1)
    end

    read :get_by_name do
      argument :config_name, :string, allow_nil?: false
      filter expr(config_name == ^arg(:config_name))
    end

    update :enable do
      change set_attribute(:status, 1)
    end

    update :disable do
      change set_attribute(:status, 0)
    end

    read :get_config_for_withdrawal do
      argument :payment_method, :string, allow_nil?: false
      argument :amount, :decimal, allow_nil?: false
      argument :vip_level, :integer, default: 0
      
      filter expr(payment_method == ^arg(:payment_method) and status == 1)
      
      prepare fn query, context ->
        query
        |> Ash.Query.filter(expr(min_amount <= ^arg(:amount) and max_amount >= ^arg(:amount)))
        |> Ash.Query.filter(expr(vip_level_required <= ^arg(:vip_level)))
        |> Ash.Query.sort(vip_level_required: :desc)
        |> Ash.Query.limit(1)
      end
    end

    read :validate_withdrawal_amount do
      argument :payment_method, :string, allow_nil?: false
      argument :amount, :decimal, allow_nil?: false
      
      filter expr(payment_method == ^arg(:payment_method) and status == 1)
      
      prepare fn query, _context ->
        query
        |> Ash.Query.load([:amount_validation])
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :config_name, :string do
      allow_nil? false
      public? true
      description "配置名称"
      constraints max_length: 100
    end

    attribute :payment_method, :string do
      allow_nil? false
      public? true
      description "支付方式：bank_card-银行卡，alipay-支付宝，upi-UPI"
      constraints max_length: 20
    end

    attribute :min_amount, :decimal do
      allow_nil? false
      public? true
      description "最小提现金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :max_amount, :decimal do
      allow_nil? false
      public? true
      description "最大提现金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :daily_limit, :decimal do
      allow_nil? true
      public? true
      description "每日提现限额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :monthly_limit, :decimal do
      allow_nil? true
      public? true
      description "每月提现限额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :fee_rate, :decimal do
      allow_nil? false
      public? true
      description "手续费率（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :tax_rate, :decimal do
      allow_nil? false
      public? true
      description "税率（%）"
      default Decimal.new("0")
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :min_fee_amount, :decimal do
      allow_nil? true
      public? true
      description "最小手续费金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :max_fee_amount, :decimal do
      allow_nil? true
      public? true
      description "最大手续费金额（分）"
      constraints min: Decimal.new("0")
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0-禁用，1-启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :vip_level_required, :integer do
      allow_nil? true
      public? true
      description "所需VIP等级"
      default 0
      constraints min: 0
    end

    attribute :turnover_multiplier, :decimal do
      allow_nil? false
      public? true
      description "流水倍数要求"
      default Decimal.new("1")
      constraints min: Decimal.new("0")
    end

    attribute :min_deposit_required, :decimal do
      allow_nil? true
      public? true
      description "最小充值要求（分）"
      constraints min: Decimal.new("0")
    end

    attribute :processing_time_hours, :integer do
      allow_nil? false
      public? true
      description "预计处理时间（小时）"
      default 24
      constraints min: 1
    end

    attribute :auto_approve_limit, :decimal do
      allow_nil? true
      public? true
      description "自动审核限额（分），超过此金额需人工审核"
      constraints min: Decimal.new("0")
    end

    attribute :gateway_channel_id, :string do
      allow_nil? true
      public? true
      description "对应的支付网关通道ID"
      constraints max_length: 50
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "配置描述"
      constraints max_length: 500
    end

    attribute :business_hours_only, :boolean do
      allow_nil? false
      public? true
      description "是否仅在工作时间处理"
      default false
    end

    attribute :weekend_processing, :boolean do
      allow_nil? false
      public? true
      description "是否支持周末处理"
      default true
    end

    # VIP用户优惠配置
    attribute :vip_fee_discount, :decimal do
      allow_nil? true
      public? true
      description "VIP用户手续费折扣（%）"
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :vip_processing_priority, :boolean do
      allow_nil? false
      public? true
      description "VIP用户是否享受优先处理"
      default false
    end

    timestamps()
  end

  identities do
    identity :unique_config_name, [:config_name]
    identity :unique_payment_method_config, [:payment_method, :config_name]
  end

  calculations do
    calculate :amount_validation, :map do
      public? true
      description "验证提现金额是否符合配置"
      argument :amount, :decimal
      
      calculation fn records, context ->
        amount = context.arguments[:amount] || Decimal.new(0)
        
        Enum.map(records, fn config ->
          cond do
            Decimal.compare(amount, config.min_amount) == :lt ->
              %{valid: false, reason: "提现金额低于最小限额 #{config.min_amount}"}
            Decimal.compare(amount, config.max_amount) == :gt ->
              %{valid: false, reason: "提现金额超过最大限额 #{config.max_amount}"}
            true ->
              %{valid: true, reason: nil}
          end
        end)
      end
    end

    calculate :fee_calculation, :map do
      public? true
      description "计算提现费用明细"
      argument :amount, :decimal
      argument :vip_level, :integer
      
      calculation fn records, context ->
        amount = context.arguments[:amount] || Decimal.new(0)
        vip_level = context.arguments[:vip_level] || 0
        
        Enum.map(records, fn config ->
          fee_amount = calculate_fee_amount(config, amount, vip_level)
          tax_amount = calculate_tax_amount(config, amount)
          actual_amount = Decimal.sub(amount, Decimal.add(fee_amount, tax_amount))
          
          %{
            original_amount: amount,
            fee_amount: fee_amount,
            tax_amount: tax_amount,
            actual_amount: actual_amount,
            fee_rate: config.fee_rate,
            tax_rate: config.tax_rate,
            vip_discount: if(vip_level > 0 && config.vip_fee_discount, do: config.vip_fee_discount, else: Decimal.new(0))
          }
        end)
      end
    end

    calculate :processing_info, :map do
      public? true
      description "获取处理信息"
      argument :vip_level, :integer
      
      calculation fn records, context ->
        vip_level = context.arguments[:vip_level] || 0
        
        Enum.map(records, fn config ->
          %{
            processing_time_hours: config.processing_time_hours,
            auto_approve_limit: config.auto_approve_limit,
            business_hours_only: config.business_hours_only,
            weekend_processing: config.weekend_processing,
            vip_priority: vip_level > 0 && config.vip_processing_priority
          }
        end)
      end
    end
  end

  # 计算实际手续费
  def calculate_fee(config, amount, user_vip_level \\ 0) do
    calculate_fee_amount(config, amount, user_vip_level)
  end

  defp calculate_fee_amount(config, amount, user_vip_level) do
    base_fee = Decimal.mult(amount, Decimal.div(config.fee_rate, Decimal.new("100")))

    # 应用VIP折扣
    fee_with_discount =
      if config.vip_fee_discount && user_vip_level > 0 do
        discount = Decimal.div(config.vip_fee_discount, Decimal.new("100"))
        Decimal.mult(base_fee, Decimal.sub(Decimal.new("1"), discount))
      else
        base_fee
      end

    # 应用最小和最大手续费限制
    final_fee =
      cond do
        config.min_fee_amount && Decimal.compare(fee_with_discount, config.min_fee_amount) == :lt ->
          config.min_fee_amount

        config.max_fee_amount && Decimal.compare(fee_with_discount, config.max_fee_amount) == :gt ->
          config.max_fee_amount

        true ->
          fee_with_discount
      end

    final_fee
  end

  # 计算税费
  def calculate_tax(config, amount) do
    calculate_tax_amount(config, amount)
  end

  defp calculate_tax_amount(config, amount) do
    Decimal.mult(amount, Decimal.div(config.tax_rate, Decimal.new("100")))
  end

  # 验证提现金额是否符合配置
  def validate_amount(config, amount) do
    cond do
      Decimal.compare(amount, config.min_amount) == :lt ->
        {:error, "提现金额低于最小限额 #{config.min_amount}"}

      Decimal.compare(amount, config.max_amount) == :gt ->
        {:error, "提现金额超过最大限额 #{config.max_amount}"}

      true ->
        :ok
    end
  end

  @doc """
  获取适用的提现配置
  根据支付方式、金额和VIP等级返回最合适的配置
  """
  def get_withdrawal_config(payment_method, amount, vip_level \\ 0) do
    case get_config_for_withdrawal(%{payment_method: payment_method, amount: amount, vip_level: vip_level}) do
      {:ok, [config | _]} -> 
        # 加载费用计算
        config_with_fees = Ash.load!(config, [fee_calculation: %{amount: amount, vip_level: vip_level}], authorize?: false)
        {:ok, config_with_fees}
      {:ok, []} -> 
        {:error, "该支付方式暂不可用或金额不符合要求"}
      {:error, reason} -> 
        {:error, reason}
    end
  end

  @doc """
  计算提现费用明细
  返回包含手续费、税费和实际到账金额的详细信息
  """
  def calculate_withdrawal_fees(payment_method, amount, vip_level \\ 0) do
    with {:ok, config} <- get_withdrawal_config(payment_method, amount, vip_level) do
      fee_info = List.first(config.fee_calculation)
      {:ok, fee_info}
    end
  end

  @doc """
  验证提现请求
  检查金额是否符合配置要求
  """
  def validate_withdrawal_request(payment_method, amount) do
    case validate_withdrawal_amount(%{payment_method: payment_method, amount: amount}) do
      {:ok, [config | _]} ->
        validation = List.first(config.amount_validation)
        if validation.valid do
          :ok
        else
          {:error, validation.reason}
        end
      {:ok, []} ->
        {:error, "该支付方式暂不可用"}
      {:error, reason} ->
        {:error, reason}
    end
  end
end
