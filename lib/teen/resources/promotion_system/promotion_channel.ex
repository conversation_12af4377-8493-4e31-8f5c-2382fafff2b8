defmodule Teen.PromotionSystem.PromotionChannel do
  @moduledoc """
  推广渠道资源

  管理推广员的推广渠道，包括渠道类型、链接、二维码、统计数据等
  """

  use Ash.Resource,
    otp_app: :cypridina,
    domain: Teen.PromotionSystem,
    data_layer: AshPostgres.DataLayer,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [
      :id,
      :promoter_id,
      :channel_name,
      :channel_type,
      :status,
      :click_count,
      :register_count,
      :conversion_rate,
      :inserted_at
    ]
  end

  postgres do
    table "promotion_channels"
    repo Cypridina.Repo
  end

  actions do
    defaults [:read, :destroy]

    create :create do
      accept [
        :promoter_id,
        :channel_name,
        :channel_type,
        :channel_url,
        :qr_code_url,
        :description,
        :tags
      ]

      change fn changeset, _context ->
        # 生成推广链接
        if Ash.Changeset.get_attribute(changeset, :channel_url) == nil do
          promoter_id = Ash.Changeset.get_attribute(changeset, :promoter_id)
          channel_name = Ash.Changeset.get_attribute(changeset, :channel_name)
          url = generate_promotion_url(promoter_id, channel_name)
          Ash.Changeset.change_attribute(changeset, :channel_url, url)
        else
          changeset
        end
      end
    end

    update :update do
      accept [
        :channel_name,
        :channel_type,
        :channel_url,
        :qr_code_url,
        :description,
        :tags,
        :status
      ]
    end

    update :activate do
      accept []

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :status, 1)
      end
    end

    update :deactivate do
      accept []

      change fn changeset, _context ->
        Ash.Changeset.change_attribute(changeset, :status, 0)
      end
    end

    update :record_click do
      accept []

      change fn changeset, _context ->
        current_count = changeset.data.click_count || 0

        changeset
        |> Ash.Changeset.change_attribute(:click_count, current_count + 1)
        |> Ash.Changeset.change_attribute(:last_click_at, DateTime.utc_now())
        |> update_conversion_rate()
      end
    end

    update :record_register do
      accept []

      change fn changeset, _context ->
        current_count = changeset.data.register_count || 0

        changeset
        |> Ash.Changeset.change_attribute(:register_count, current_count + 1)
        |> update_conversion_rate()
      end
    end

    update :reset_stats do
      accept []

      change fn changeset, _context ->
        changeset
        |> Ash.Changeset.change_attribute(:click_count, 0)
        |> Ash.Changeset.change_attribute(:register_count, 0)
        |> Ash.Changeset.change_attribute(:conversion_rate, Decimal.new("0"))
        |> Ash.Changeset.change_attribute(:last_click_at, nil)
      end
    end
  end

  validations do
    validate match(:channel_type, ~r/^(link|qr_code|social|email|sms)$/) do
      message "渠道类型必须是：link, qr_code, social, email, sms 之一"
    end

    validate present(:channel_url) do
      message "推广链接不能为空"
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :promoter_id, :uuid do
      allow_nil? false
      public? true
      description "推广员ID"
    end

    attribute :channel_name, :string do
      allow_nil? false
      public? true
      description "渠道名称"
      constraints max_length: 100
    end

    attribute :channel_type, :string do
      allow_nil? false
      public? true
      description "渠道类型：link=链接，qr_code=二维码，social=社交媒体，email=邮件，sms=短信"
      constraints max_length: 20
    end

    attribute :channel_url, :string do
      allow_nil? true
      public? true
      description "推广链接"
      constraints max_length: 500
    end

    attribute :qr_code_url, :string do
      allow_nil? true
      public? true
      description "二维码图片URL"
      constraints max_length: 500
    end

    attribute :status, :integer do
      allow_nil? false
      public? true
      description "状态：0=禁用，1=启用"
      default 1
      constraints min: 0, max: 1
    end

    attribute :click_count, :integer do
      allow_nil? false
      public? true
      description "点击次数"
      default 0
      constraints min: 0
    end

    attribute :register_count, :integer do
      allow_nil? false
      public? true
      description "注册转化次数"
      default 0
      constraints min: 0
    end

    attribute :conversion_rate, :decimal do
      allow_nil? false
      public? true
      description "转化率（百分比）"
      default Decimal.new("0")
      constraints min: Decimal.new("0"), max: Decimal.new("100")
    end

    attribute :description, :string do
      allow_nil? true
      public? true
      description "渠道描述"
      constraints max_length: 500
    end

    attribute :tags, {:array, :string} do
      allow_nil? true
      public? true
      description "标签列表"
      default %{}
    end

    attribute :last_click_at, :utc_datetime do
      allow_nil? true
      public? true
      description "最后点击时间"
    end

    timestamps()
  end

  relationships do
    belongs_to :promoter, Teen.PromotionSystem.Promoter do
      destination_attribute :id
      source_attribute :promoter_id
    end
  end

  calculations do
    calculate :is_active, :boolean, expr(status == 1)

    calculate :channel_type_name, :string do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          case record.channel_type do
            "link" -> "推广链接"
            "qr_code" -> "二维码"
            "social" -> "社交媒体"
            "email" -> "邮件推广"
            "sms" -> "短信推广"
            _ -> "其他"
          end
        end)
      end
    end

    calculate :performance_score, :decimal do
      calculation fn records, _context ->
        Enum.map(records, fn record ->
          click_count = record.click_count || 0
          register_count = record.register_count || 0

          # 简单的性能评分算法
          base_score = register_count * 10
          click_bonus = min(click_count, 1000) * 0.01

          Decimal.new(Float.to_string(base_score + click_bonus))
        end)
      end
    end
  end

  identities do
    identity :unique_promoter_channel, [:promoter_id, :channel_name]
  end

  defp generate_promotion_url(promoter_id, channel_name) do
    base_url = Application.get_env(:cypridina, :promotion_base_url, "https://example.com")
    channel_code = Base.encode64("#{promoter_id}:#{channel_name}", padding: false)
    "#{base_url}/register?ref=#{channel_code}"
  end

  defp update_conversion_rate(changeset) do
    click_count =
      Ash.Changeset.get_attribute(changeset, :click_count) || changeset.data.click_count || 0

    register_count =
      Ash.Changeset.get_attribute(changeset, :register_count) || changeset.data.register_count ||
        0

    rate =
      if click_count > 0 do
        (register_count / click_count * 100) |> Float.round(2) |> Decimal.new()
      else
        Decimal.new("0")
      end

    Ash.Changeset.change_attribute(changeset, :conversion_rate, rate)
  end
end
