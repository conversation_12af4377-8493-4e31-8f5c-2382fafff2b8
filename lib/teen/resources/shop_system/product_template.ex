defmodule Teen.ShopSystem.ProductTemplate do
  @moduledoc """
  商品配置模板资源

  预定义不同类型商品的配置模板，方便快速创建商品
  """

  use Ash.Resource,
    otp_app: :cypridina,
    data_layer: AshPostgres.DataLayer,
    domain: Teen.ShopSystem,
    extensions: [AshAdmin.Resource]

  admin do
    table_columns [:id, :template_name, :product_type, :is_default, :inserted_at]
  end

  postgres do
    table "product_templates"
    repo Cypridina.Repo

    identity_wheres_to_sql unique_default_per_type: "is_default = true"
  end

  code_interface do
    define :create
    define :read
    define :update
    define :destroy
    define :list_by_type
    define :get_default_template
    define :create_product_from_template
  end

  actions do
    defaults [:read, :update, :destroy]

    create :create do
      accept [:template_name, :product_type, :default_config, :config_schema, :is_default]
    end

    read :list_by_type do
      argument :product_type, :atom, allow_nil?: false
      filter expr(product_type == ^arg(:product_type))
      prepare build(sort: [desc: :is_default, asc: :template_name])
    end

    read :get_default_template do
      argument :product_type, :atom, allow_nil?: false
      filter expr(product_type == ^arg(:product_type) and is_default == true)
      get? true
    end

    create :create_product_from_template do
      argument :template_id, :uuid, allow_nil?: false
      argument :product_name, :string, allow_nil?: false
      argument :price, :decimal, allow_nil?: false
      argument :custom_config, :map, allow_nil?: true

      change fn changeset, _context ->
        # 这里会根据模板创建商品
        # 具体实现在业务逻辑中
        changeset
      end
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :template_name, :string do
      allow_nil? false
      public? true
      description "模板名称"
      constraints max_length: 100
    end

    attribute :product_type, :atom do
      allow_nil? false
      public? true
      description "商品类型"

      constraints one_of: [
                    :monthly_card,
                    :weekly_card,
                    :play_card,
                    :coin_package,
                    :vip_package,
                    :special_item,
                    :recharge_bonus
                  ]
    end

    attribute :default_config, :map do
      allow_nil? false
      public? true
      description "默认配置"
      default %{}
    end

    attribute :config_schema, :map do
      allow_nil? true
      public? true
      description "配置字段说明"
      default %{}
    end

    attribute :is_default, :boolean do
      allow_nil? false
      public? true
      description "是否为默认模板"
      default false
    end

    timestamps()
  end

  identities do
    identity :unique_default_per_type, [:product_type, :is_default],
      where: expr(is_default == true)
  end
end
