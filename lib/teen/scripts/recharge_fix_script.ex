defmodule Teen.Scripts.RechargeFix do
  @moduledoc """
  充值问题修复脚本
  
  用于检查和修复充值记录状态为完成但余额未更新的问题
  """
  
  require Logger
  alias Teen.PaymentSystem.RechargeRecord
  alias Cypridina.Ledger
  
  def check_user_recharge_status(user_id) do
    Logger.info("🔍 检查用户 #{user_id} 的充值状态")
    
    # 获取用户所有已完成的充值记录
    case RechargeRecord.read(
      filter: %{user_id: user_id, status: :completed},
      sort: [desc: :completed_at]
    ) do
      {:ok, recharge_records} ->
        Logger.info("📊 找到 #{length(recharge_records)} 条已完成的充值记录")
        
        Enum.each(recharge_records, fn record ->
          check_single_recharge(record)
        end)
        
        # 检查用户当前余额
        user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)
        case Cypridina.Ledger.BalanceCache.get_balance(user_identifier) do
          {:ok, current_balance} ->
            Logger.info("💰 用户当前余额: #{current_balance}")
            
            # 计算总充值金额
            total_recharged = Enum.reduce(recharge_records, Decimal.new(0), fn record, acc ->
              Decimal.add(acc, record.amount)
            end)
            
            Logger.info("💳 总充值金额: #{total_recharged}")
            
          {:error, reason} ->
            Logger.error("❌ 获取用户余额失败: #{inspect(reason)}")
        end
        
      {:error, reason} ->
        Logger.error("❌ 查询充值记录失败: #{inspect(reason)}")
    end
  end
  
  def check_single_recharge(record) do
    Logger.info("🔍 检查充值记录 #{record.order_id}")
    Logger.info("   用户: #{record.user_id}")
    Logger.info("   金额: #{record.amount}")
    Logger.info("   完成时间: #{record.completed_at}")
    
    # 检查是否有对应的转账记录
    case check_transfer_exists(record) do
      {:ok, transfer} ->
        Logger.info("✅ 找到对应转账记录: #{transfer.id}")
      {:error, :not_found} ->
        Logger.error("❌ 未找到对应转账记录，需要补发")
        {:missing_transfer, record}
      {:error, reason} ->
        Logger.error("❌ 检查转账记录失败: #{inspect(reason)}")
    end
  end
  
  defp check_transfer_exists(record) do
    # 在 ledger_transfers 表中查找对应的转账记录
    # 根据时间范围和金额匹配
    completed_time = record.completed_at
    start_time = DateTime.add(completed_time, -30, :second)
    end_time = DateTime.add(completed_time, 30, :second)
    
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(record.user_id, :XAA)
    
    case Cypridina.Ledger.Transfer.read(
      filter: %{
        to_account: user_identifier,
        timestamp: %{gte: start_time, lte: end_time},
        transaction_type: :deposit
      }
    ) do
      {:ok, []} -> {:error, :not_found}
      {:ok, [transfer | _]} -> {:ok, transfer}
      {:error, reason} -> {:error, reason}
    end
  end
  
  def fix_missing_recharge(record) do
    Logger.info("🔧 开始修复充值记录: #{record.order_id}")
    
    case add_coins_to_user_directly(record.user_id, record.amount) do
      {:ok, transfer} ->
        Logger.info("✅ 补发充值成功: #{transfer.id}")
        {:ok, transfer}
      {:error, reason} ->
        Logger.error("❌ 补发充值失败: #{inspect(reason)}")
        {:error, reason}
    end
  end
  
  defp add_coins_to_user_directly(user_id, amount) do
    system_identifier = Cypridina.Ledger.AccountIdentifier.system(:main, :XAA)
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)
    
    amount_integer = if is_struct(amount, Decimal) do
      Decimal.to_integer(amount)
    else
      amount
    end

    Cypridina.Ledger.transfer(
      system_identifier,
      user_identifier,
      amount_integer,
      transaction_type: :deposit,
      description: "补发充值 - 修复充值未到账问题"
    )
  end
  
  def check_system_account_balance do
    Logger.info("🔍 检查系统主账户余额")
    
    system_identifier = Cypridina.Ledger.AccountIdentifier.system(:main, :XAA)
    case Cypridina.Ledger.BalanceCache.get_balance(system_identifier) do
      {:ok, balance} ->
        Logger.info("💰 系统主账户余额: #{balance}")
        {:ok, balance}
      {:error, reason} ->
        Logger.error("❌ 获取系统账户余额失败: #{inspect(reason)}")
        {:error, reason}
    end
  end
  
  def fix_user_recharges(user_id) do
    Logger.info("🔧 开始修复用户 #{user_id} 的充值问题")
    
    # 检查用户状态
    check_user_recharge_status(user_id)
    
    # 检查系统账户
    check_system_account_balance()
  end
end