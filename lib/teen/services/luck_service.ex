defmodule Teen.Services.LuckService do
  @moduledoc """
  幸运值服务 - 基于用户ID的幸运值管理
  幸运值与用户绑定，不再区分游戏类型
  """

  require Logger
  alias Teen.UserLuckValue
  import Ash.Query

  @doc """
  根据用户ID获取幸运值
  支持UUID和numeric_id两种查询方式
  """
  def get_user_luck(user_identifier) do
    case resolve_user_id(user_identifier) do
      {:ok, user_id} ->
        case UserLuckValue.get_by_user(user_id) do
          {:ok, luck_record} -> {:ok, luck_record.current_luck}
          {:error, _} -> {:ok, -1}
        end
      {:error, reason} ->
        Logger.error("🍀 [LUCK_SERVICE] 无法解析用户标识 #{inspect(user_identifier)}: #{inspect(reason)}")
        {:ok, -1}
    end
  end

  @doc """
  充值成功后更新幸运值
  """
  def handle_recharge_success(user_identifier) do
    case resolve_user_id(user_identifier) do
      {:ok, user_id} ->
        case UserLuckValue.get_by_user(user_id) do
          {:ok, luck_record} ->
            # 记录存在，更新幸运值
            new_luck = calculate_new_luck(luck_record)
            UserLuckValue.update_luck(luck_record, new_luck)

          {:error, _} ->
            # 记录不存在，创建新记录
            Logger.info("🍀 [LUCK_SERVICE] 创建用户 #{user_id} 的幸运值记录")
            # 第一次充值750
            initial_luck = 750

            case UserLuckValue.create_for_user(user_id) do
              {:ok, luck_record} ->
                # 创建成功后立即更新为首次充值的幸运值
                UserLuckValue.update_luck(luck_record, initial_luck)

              error ->
                Logger.error("🍀 [LUCK_SERVICE] 创建幸运值记录失败: #{inspect(error)}")
                error
            end
        end
      {:error, reason} ->
        Logger.error("🍀 [LUCK_SERVICE] 充值成功处理失败，无法解析用户标识: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  管理员重置幸运值
  """
  def admin_reset_luck(user_identifier, new_value) do
    case resolve_user_id(user_identifier) do
      {:ok, user_id} ->
        case UserLuckValue.get_by_user(user_id) do
          {:ok, luck_record} ->
            UserLuckValue.admin_reset(luck_record, new_value)

          {:error, _} ->
            {:error, :user_not_found}
        end
      {:error, reason} ->
        Logger.error("🍀 [LUCK_SERVICE] 重置幸运值失败，无法解析用户标识: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取所有幸运值记录
  """
  def get_all_luck_records(opts \\ []) do
    query =
      UserLuckValue
      |> sort(last_updated_at: :desc)
      # 加载用户关联信息和充值次数
      |> load([:user, :recharge_count])

    # 应用筛选条件
    query =
      if opts[:min_luck] do
        filter(query, current_luck >= ^opts[:min_luck])
      else
        query
      end

    query =
      if opts[:max_luck] do
        filter(query, current_luck <= ^opts[:max_luck])
      else
        query
      end

    Ash.read(query)
  end

  @doc """
  获取幸运值统计信息
  """
  def get_luck_statistics do
    case UserLuckValue |> load(:recharge_count) |> Ash.read() do
      {:ok, records} ->
        total_users = length(records)
        never_recharged = Enum.count(records, &(&1.current_luck == -1))
        active_users = total_users - never_recharged
        total_recharges = Enum.sum(Enum.map(records, & &1.recharge_count))

        luck_values = Enum.map(records, & &1.current_luck) |> Enum.reject(&(&1 == -1))

        {average_luck, max_luck, min_luck} =
          if length(luck_values) > 0 do
            avg = luck_values |> Enum.sum() |> div(length(luck_values))
            max_val = Enum.max(luck_values)
            min_val = Enum.min(luck_values)
            {avg, max_val, min_val}
          else
            {0, 0, 0}
          end

        stats = %{
          total_users: total_users,
          never_recharged: never_recharged,
          active_users: active_users,
          total_recharges: total_recharges,
          average_luck: average_luck,
          max_luck: max_luck,
          min_luck: min_luck
        }

        {:ok, stats}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取用户充值次数（通过虚拟字段）
  """
  def get_user_recharge_count(user_identifier) do
    case resolve_user_id(user_identifier) do
      {:ok, user_id} ->
        case UserLuckValue.get_by_user(user_id) |> Ash.load(:recharge_count) do
          {:ok, luck_record} -> {:ok, luck_record.recharge_count}
          {:error, _} -> {:ok, 0}
        end
      {:error, _} ->
        {:ok, 0}
    end
  end

  @doc """
  为新用户创建幸运值记录
  """
  def create_user_luck_record(user_identifier) do
    case resolve_user_id(user_identifier) do
      {:ok, user_id} ->
        Logger.info("🍀 [LUCK_SERVICE] 为用户 #{user_id} 创建幸运值记录")
        UserLuckValue.create_for_user(user_id)
      {:error, reason} ->
        Logger.error("🍀 [LUCK_SERVICE] 创建幸运值记录失败，无法解析用户标识: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  批量重置幸运值
  """
  def batch_reset_luck(user_ids, new_value) do
    results =
      Enum.map(user_ids, fn user_id ->
        admin_reset_luck(user_id, new_value)
      end)

    {:ok, results}
  end

  # ==================== 私有函数 ====================

  # 根据当前幸运值记录计算新幸运值
  defp calculate_new_luck(luck_record) do
    # 通过虚拟字段获取充值次数
    recharge_count = luck_record.recharge_count || 0

    cond do
      # 第一次充值（幸运值为-1或充值次数为0）
      luck_record.current_luck == -1 or recharge_count == 0 -> 750
      # 后续充值：在当前幸运值基础上增加300-400，但不超过1000
      true -> min(luck_record.current_luck + Enum.random(300..400), 1000)
    end
  end

  @doc """
  解析用户标识，支持UUID和numeric_id
  """
  defp resolve_user_id(user_identifier) when is_binary(user_identifier) do
    # 尝试解析为UUID
    case Ecto.UUID.cast(user_identifier) do
      {:ok, _} -> {:ok, user_identifier}
      :error -> {:error, :invalid_uuid}
    end
  end

  defp resolve_user_id(user_identifier) when is_integer(user_identifier) do
    # 通过numeric_id查找用户的UUID
    case Cypridina.Accounts.User |> Ash.Query.filter(numeric_id == ^user_identifier) |> Ash.read() do
      {:ok, [user]} -> {:ok, user.id}
      {:ok, []} -> {:error, :user_not_found}
      {:ok, _multiple} -> {:error, :multiple_users_found}
      {:error, reason} -> {:error, reason}
    end
  end

  defp resolve_user_id(user_identifier) do
    {:error, {:invalid_identifier_type, user_identifier}}
  end
end
