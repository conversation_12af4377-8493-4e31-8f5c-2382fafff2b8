defmodule Teen.Services.MasterPayService do
  @moduledoc """
  MasterPay支付服务集成

  提供完整的MasterPay支付解决方案，包括：
  - 充值订单创建和处理
  - 提现订单创建和处理
  - 订单状态查询
  - 回调处理
  - 安全验证

  网关信息：
  - 商户ID: 10236
  - 网关地址: https://api.masterpay88.in/app-api
  - 代收通道: 3021
  - 代付通道: 3020
  - 回调IP: *************
  """

  require Logger

  alias Teen.PaymentSystem.{PaymentGatewayConfig, PaymentOrder, PaymentCallback}
  alias Teen.PaymentSystem.Gateways.MasterPayGateway
  alias Teen.Services.NotificationService
  alias Cypridina.Ledger
  alias Cypridina.Ledger.{BalanceCache, AccountIdentifier}

  @gateway_code "masterpay"


  def generate_payment_signature(params, secret_key \\ nil) do
    secret_key = secret_key || Application.get_env(:cypridina, :payment)[:secret_key]

    Logger.info("generate_payment_signature: #{secret_key}")
    # 按照字母顺序排序参数，排除空值和sign字段
    sorted_params =
      params
      # |> Enum.reject(fn {key, value} -> key == "sign" or is_nil(value) or value == "" end)
      |> Enum.map(fn {key, value} -> "#{key}=#{value}&" end)
      |> Enum.sort_by(fn x -> String.downcase(x) end)
      |> Enum.join("")

    # 添加密钥
    sign_string = "#{sorted_params}secretKey=#{secret_key}"

    Logger.info("generate_payment_sign_string: #{sign_string}")

    # MD5签名
    :crypto.hash(:md5, sign_string)
    |> Base.encode16(case: :upper)
  end

  @doc """
  创建充值订单
  """
  def create_recharge_order(user_id, amount, opts \\ []) do
    with {:ok, gateway_config} <- get_gateway_config(),
         {:ok, order_params} <- prepare_recharge_order_params(user_id, amount, opts),
         {:ok, payment_order} <- create_payment_order_record(order_params, gateway_config),
         {:ok, gateway_result} <- MasterPayGateway.create_recharge_order(order_params),
         {:ok, updated_order} <-
           update_payment_order_with_gateway_result(payment_order, gateway_result) do
      Logger.info("MasterPay充值订单创建成功: #{payment_order.order_id}")

      # 发送通知
      NotificationService.send_payment_status_notification(user_id, %{
        order_id: payment_order.order_id,
        payment_type: :recharge,
        status: :pending,
        amount: amount,
        message: "充值订单已创建，请完成支付"
      })

      {:ok,
       %{
         order_id: payment_order.order_id,
         payment_url: gateway_result.payment_url,
         qr_code: gateway_result.qr_code,
         amount: amount,
         gateway_order_id: gateway_result.gateway_order_id
       }}
    else
      {:error, reason} ->
        Logger.error("创建充值订单失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  创建提现订单
  """
  def create_withdrawal_order(user_id, amount, bank_info, opts \\ []) do
    with {:ok, gateway_config} <- get_gateway_config(),
         :ok <- validate_withdrawal_eligibility(user_id, amount),
         {:ok, order_params} <- prepare_withdrawal_order_params(user_id, amount, bank_info, opts),
         {:ok, payment_order} <- create_payment_order_record(order_params, gateway_config),
         {:ok, _} <- deduct_withdrawal_amount(user_id, amount),
         {:ok, gateway_result} <- MasterPayGateway.create_withdrawal_order(order_params),
         {:ok, updated_order} <-
           update_payment_order_with_gateway_result(payment_order, gateway_result) do
      Logger.info("MasterPay提现订单创建成功: #{payment_order.order_id}")

      # 发送通知
      NotificationService.send_withdrawal_status_notification(user_id, %{
        withdrawal_id: payment_order.id,
        status: :processing,
        amount: amount,
        fee_amount: gateway_result.fee || 0,
        actual_amount: amount - (gateway_result.fee || 0),
        estimated_time: gateway_result.estimated_time,
        message: "提现订单已提交，正在处理中"
      })

      {:ok,
       %{
         order_id: payment_order.order_id,
         gateway_order_id: gateway_result.gateway_order_id,
         amount: amount,
         fee: gateway_result.fee || 0,
         estimated_time: gateway_result.estimated_time
       }}
    else
      {:error, reason} ->
        Logger.error("创建提现订单失败: #{inspect(reason)}")
        # 如果扣款成功但后续步骤失败，需要退款
        refund_on_failure(user_id, amount, reason)
        {:error, reason}
    end
  end

  @doc """
  查询订单状态
  """
  def query_order_status(order_id) do
    with {:ok, payment_order} <- get_payment_order(order_id),
         {:ok, gateway_result} <- MasterPayGateway.query_order(order_id) do
      # 更新本地订单状态
      update_order_status_from_gateway(payment_order, gateway_result)

      {:ok,
       %{
         order_id: order_id,
         gateway_order_id: gateway_result.gateway_order_id,
         status: gateway_result.status,
         amount: gateway_result.amount,
         created_at: gateway_result.created_at,
         updated_at: gateway_result.updated_at
       }}
    else
      {:error, reason} ->
        Logger.error("查询订单状态失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  处理支付回调
  """
  def handle_payment_callback(callback_params, callback_type) do
    with {:ok, callback_result} <-
           MasterPayGateway.handle_callback(callback_params, callback_type),
         {:ok, payment_order} <- get_payment_order(callback_params["merchantOrderId"]),
         {:ok, callback_record} <-
           create_callback_record(payment_order, callback_type, callback_params),
         {:ok, processed_result} <-
           process_callback_result(payment_order, callback_result, callback_type) do
      # 更新回调记录
      PaymentCallback.mark_processed(callback_record, %{process_result: processed_result})

      Logger.info("支付回调处理成功: #{payment_order.order_id}")
      {:ok, processed_result}
    else
      {:error, reason} ->
        Logger.error("支付回调处理失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  获取网关配置信息
  """
  def get_gateway_info do
    case get_gateway_config() do
      {:ok, config} ->
        {:ok,
         %{
           gateway_name: config.gateway_name,
           supported_currencies: config.supported_currencies,
           supported_payment_methods: config.supported_payment_methods,
           min_amount: config.min_amount,
           max_amount: config.max_amount,
           fee_rate: config.fee_rate,
           status: config.status
         }}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取用户支付统计
  """
  def get_user_payment_stats(user_id, date_range \\ nil) do
    with {:ok, recharge_orders} <- get_user_recharge_orders(user_id, date_range),
         {:ok, withdrawal_orders} <- get_user_withdrawal_orders(user_id, date_range) do
      stats = %{
        recharge: %{
          total_count: length(recharge_orders),
          success_count: count_successful_orders(recharge_orders),
          total_amount: sum_order_amounts(recharge_orders),
          success_amount: sum_successful_order_amounts(recharge_orders)
        },
        withdrawal: %{
          total_count: length(withdrawal_orders),
          success_count: count_successful_orders(withdrawal_orders),
          total_amount: sum_order_amounts(withdrawal_orders),
          success_amount: sum_successful_order_amounts(withdrawal_orders)
        }
      }

      {:ok, stats}
    else
      {:error, reason} ->
        {:error, reason}
    end
  end

  # ==================== 私有函数 ====================

  defp get_gateway_config do
    case PaymentGatewayConfig.get_by_code(@gateway_code) do
      {:ok, config} ->
        if config.status == "active" do
          {:ok, config}
        else
          {:error, "Gateway is not active"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp prepare_recharge_order_params(user_id, amount, opts) do
    order_id = generate_order_id("RCH")

    params = %{
      order_id: order_id,
      amount: amount,
      customer_name: opts[:customer_name] || "Customer",
      customer_email: opts[:customer_email] || "<EMAIL>",
      customer_phone: opts[:customer_phone] || "",
      remark: opts[:remark] || "Recharge Order"
    }

    {:ok, params}
  end

  defp prepare_withdrawal_order_params(user_id, amount, bank_info, opts) do
    order_id = generate_order_id("WDL")

    params = %{
      order_id: order_id,
      amount: amount,
      customer_name: bank_info.account_name,
      customer_email: opts[:customer_email] || "",
      customer_phone: opts[:customer_phone] || "",
      bank_name: bank_info.bank_name,
      bank_code: bank_info.bank_code,
      account_number: bank_info.account_number,
      account_name: bank_info.account_name,
      ifsc_code: bank_info.ifsc_code,
      remark: opts[:remark] || "Withdrawal Order"
    }

    {:ok, params}
  end

  defp create_payment_order_record(order_params, gateway_config) do
    order_data = %{
      order_id: order_params.order_id,
      user_id: order_params.user_id || get_user_id_from_context(),
      gateway_config_id: gateway_config.id,
      order_type: determine_order_type(order_params),
      amount: order_params.amount,
      currency: "INR",
      payment_method: order_params.payment_method || "gateway",
      customer_info: extract_customer_info(order_params),
      bank_info: extract_bank_info(order_params),
      gateway_request: order_params
    }

    PaymentOrder.create(order_data)
  end

  defp update_payment_order_with_gateway_result(payment_order, gateway_result) do
    update_data = %{
      gateway_order_id: gateway_result.gateway_order_id,
      payment_url: gateway_result.payment_url,
      qr_code: gateway_result.qr_code,
      gateway_response: gateway_result
    }

    PaymentOrder.update_gateway_info(payment_order, update_data)
  end

  defp validate_withdrawal_eligibility(user_id, amount) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)

    case BalanceCache.get_balance(user_identifier) do
      {:ok, balance} when balance >= amount ->
        :ok

      {:ok, balance} ->
        {:error, "余额不足，当前余额: #{balance}, 提现金额: #{amount}"}

      {:error, reason} ->
        {:error, "获取余额失败: #{inspect(reason)}"}
    end
  end

  defp deduct_withdrawal_amount(user_id, amount) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    withdrawal_identifier = AccountIdentifier.system(:withdrawal_pending, :XAA)

    case Ledger.transfer(user_identifier, withdrawal_identifier, amount,
           transaction_type: :withdrawal_request,
           description: "提现申请扣款"
         ) do
      {:ok, transfer} ->
        Logger.info("提现扣款成功: 用户#{user_id}, 金额#{amount}")
        {:ok, transfer}

      {:error, reason} ->
        Logger.error("提现扣款失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp refund_on_failure(user_id, amount, reason) do
    # 如果提现订单创建失败，需要退回用户余额
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    withdrawal_identifier = AccountIdentifier.system(:withdrawal_pending, :XAA)

    case Ledger.transfer(withdrawal_identifier, user_identifier, amount,
           transaction_type: :refund,
           description: "提现订单创建失败退款"
         ) do
      {:ok, _} ->
        Logger.info("提现失败退款成功: 用户#{user_id}, 金额#{amount}")

        # 发送退款通知
        NotificationService.send_system_message(user_id, %{
          title: "提现失败",
          content: "提现订单创建失败，已退回余额。原因: #{inspect(reason)}",
          category: :error,
          action_required: false
        })

      {:error, refund_error} ->
        Logger.error("提现失败退款失败: #{inspect(refund_error)}")

        # 发送紧急通知
        NotificationService.send_system_message(user_id, %{
          title: "系统异常",
          content: "提现订单创建失败，退款也失败，请联系客服处理。",
          category: :error,
          action_required: true
        })
    end
  end

  defp get_payment_order(order_id) do
    PaymentOrder.get_by_order_id(order_id)
  end

  defp create_callback_record(payment_order, callback_type, callback_params) do
    callback_data = %{
      payment_order_id: payment_order.id,
      callback_type: callback_type,
      client_ip: callback_params["client_ip"] || "unknown",
      callback_data: callback_params,
      signature_verified: callback_params["signature_verified"] || false,
      ip_verified: callback_params["ip_verified"] || false
    }

    PaymentCallback.create(callback_data)
  end

  defp process_callback_result(payment_order, callback_result, callback_type) do
    case callback_type do
      "recharge" ->
        process_recharge_callback(payment_order, callback_result)

      "withdrawal" ->
        process_withdrawal_callback(payment_order, callback_result)

      _ ->
        {:error, "Unknown callback type"}
    end
  end

  defp process_recharge_callback(payment_order, callback_result) do
    case callback_result.status do
      :success ->
        # 充值成功，增加用户余额
        with {:ok, _} <-
               PaymentOrder.complete_order(payment_order, %{
                 gateway_response: callback_result,
                 callback_data: callback_result,
                 fee_amount: callback_result.gateway_fee || 0,
                 actual_amount: callback_result.amount
               }),
             {:ok, _} <- add_user_balance(payment_order.user_id, callback_result.amount) do
          {:ok, %{status: :success, message: "充值成功"}}
        else
          {:error, reason} ->
            {:error, reason}
        end

      :failed ->
        PaymentOrder.fail_order(payment_order, %{
          error_message: "Gateway payment failed",
          callback_data: callback_result
        })

        {:ok, %{status: :failed, message: "充值失败"}}

      _ ->
        {:ok, %{status: callback_result.status, message: "状态更新"}}
    end
  end

  defp process_withdrawal_callback(payment_order, callback_result) do
    case callback_result.status do
      :success ->
        # 提现成功
        PaymentOrder.complete_order(payment_order, %{
          gateway_response: callback_result,
          callback_data: callback_result,
          fee_amount: callback_result.gateway_fee || 0,
          actual_amount: callback_result.amount
        })

        {:ok, %{status: :success, message: "提现成功"}}

      :failed ->
        # 提现失败，需要退回用户余额
        with {:ok, _} <-
               PaymentOrder.fail_order(payment_order, %{
                 error_message: "Gateway withdrawal failed",
                 callback_data: callback_result
               }),
             {:ok, _} <- refund_withdrawal_amount(payment_order.user_id, callback_result.amount) do
          {:ok, %{status: :failed, message: "提现失败，已退回余额"}}
        else
          {:error, reason} ->
            {:error, reason}
        end

      _ ->
        {:ok, %{status: callback_result.status, message: "状态更新"}}
    end
  end

  defp add_user_balance(user_id, amount) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    recharge_identifier = AccountIdentifier.system(:recharge_income, :XAA)

    case Ledger.transfer(recharge_identifier, user_identifier, amount,
           transaction_type: :deposit,
           description: "MasterPay充值到账"
         ) do
      {:ok, transfer} ->
        Logger.info("充值到账成功: 用户#{user_id}, 金额#{amount}")
        {:ok, transfer}

      {:error, reason} ->
        Logger.error("充值到账失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp refund_withdrawal_amount(user_id, amount) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    withdrawal_identifier = AccountIdentifier.system(:withdrawal_pending, :XAA)

    case Ledger.transfer(withdrawal_identifier, user_identifier, amount,
           transaction_type: :refund,
           description: "MasterPay提现失败退款"
         ) do
      {:ok, transfer} ->
        Logger.info("提现失败退款成功: 用户#{user_id}, 金额#{amount}")
        {:ok, transfer}

      {:error, reason} ->
        Logger.error("提现失败退款失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp update_order_status_from_gateway(payment_order, gateway_result) do
    PaymentOrder.update_status(payment_order, %{
      gateway_status: gateway_result.status,
      gateway_response: gateway_result
    })
  end

  defp generate_order_id(prefix) do
    timestamp = DateTime.utc_now() |> DateTime.to_unix(:millisecond)
    random = :crypto.strong_rand_bytes(4) |> Base.encode16(case: :lower)
    "#{prefix}#{timestamp}#{random}"
  end

  defp determine_order_type(order_params) do
    cond do
      Map.has_key?(order_params, :bank_name) -> "withdrawal"
      true -> "recharge"
    end
  end

  defp extract_customer_info(order_params) do
    %{
      name: order_params[:customer_name],
      email: order_params[:customer_email],
      phone: order_params[:customer_phone]
    }
  end

  defp extract_bank_info(order_params) do
    %{
      bank_name: order_params[:bank_name],
      bank_code: order_params[:bank_code],
      account_number: order_params[:account_number],
      account_name: order_params[:account_name],
      ifsc_code: order_params[:ifsc_code]
    }
  end

  defp get_user_id_from_context do
    # 从当前上下文获取用户ID
    # 这里需要根据实际情况实现
    nil
  end

  defp get_user_recharge_orders(user_id, date_range) do
    PaymentOrder.list_by_user(user_id)
    |> case do
      {:ok, orders} ->
        filtered_orders =
          Enum.filter(orders, fn order ->
            order.order_type == "recharge" and
              (date_range == nil or in_date_range?(order.created_at, date_range))
          end)

        {:ok, filtered_orders}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp get_user_withdrawal_orders(user_id, date_range) do
    PaymentOrder.list_by_user(user_id)
    |> case do
      {:ok, orders} ->
        filtered_orders =
          Enum.filter(orders, fn order ->
            order.order_type == "withdrawal" and
              (date_range == nil or in_date_range?(order.created_at, date_range))
          end)

        {:ok, filtered_orders}

      {:error, reason} ->
        {:error, reason}
    end
  end

  defp count_successful_orders(orders) do
    Enum.count(orders, fn order -> order.status == "completed" end)
  end

  defp sum_order_amounts(orders) do
    Enum.reduce(orders, Decimal.new("0"), fn order, acc ->
      Decimal.add(acc, order.amount)
    end)
  end

  defp sum_successful_order_amounts(orders) do
    orders
    |> Enum.filter(fn order -> order.status == "completed" end)
    |> sum_order_amounts()
  end

  defp in_date_range?(datetime, {start_date, end_date}) do
    DateTime.compare(datetime, start_date) != :lt and
      DateTime.compare(datetime, end_date) != :gt
  end

  defp in_date_range?(datetime, date_range) when is_nil(date_range), do: true
end
