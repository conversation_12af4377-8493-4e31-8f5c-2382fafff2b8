defmodule Teen.Services.RechargeService do
  @moduledoc """
  充值服务 - 协调充值流程和跨系统集成

  主要职责：
  - 充值回调处理的入口点
  - 协调支付网关和充值记录的交互
  - 处理复杂的充值业务逻辑
  """

  require Logger
  alias Teen.PaymentSystem.RechargeRecord
  alias Teen.PaymentSystem.CompleteRechargeReactorSimple

  @doc """
  完成充值 - 供支付网关回调使用

  这是支付网关回调的统一入口，负责协调充值记录的更新
  """
  def complete_recharge(order_id, callback_result) do
    Logger.info("Processing recharge callback for order #{order_id}")

    # 先通过order_id获取record_id，然后使用 Reactor 处理充值流程
    with {:ok, record} <- RechargeRecord.get_by_order_id(order_id) do
      Reactor.run(CompleteRechargeReactorSimple, %{
        record_id: record.id,
        external_order_id: Map.get(callback_result, :gateway_order_id),
        callback_data: callback_result
      })
    else
      {:error, reason} ->
        Logger.error("Failed to find recharge record for order #{order_id}: #{inspect(reason)}")
        {:error, "Record not found: #{inspect(reason)}"}
    end
  end

  @doc """
  创建充值记录 - 便捷方法
  """
  def create_recharge_record(user_id, amount, payment_method, currency \\ "XAA") do
    RechargeRecord.create_recharge_order(user_id, amount, payment_method, currency)
  end

  @doc """
  获取用户的充值历史 - 便捷方法
  """
  def get_user_recharge_history(user_id, opts \\ []) do
    params = %{
      user_id: user_id,
      limit: Keyword.get(opts, :limit, 20),
      offset: Keyword.get(opts, :offset, 0)
    }

    params = if status = Keyword.get(opts, :status) do
      Map.put(params, :status, status)
    else
      params
    end

    RechargeRecord.get_user_history(params)
  end

  @doc """
  获取充值统计信息 - 便捷方法
  """
  def get_recharge_stats(user_id, date_range \\ nil) do
    RechargeRecord.get_recharge_statistics(user_id, date_range)
  end

  @doc """
  处理批量充值（如管理员充值）
  这种跨系统的复杂操作适合保留在服务层
  """
  def process_admin_recharge(admin_id, user_id, amount, reason) do
    # 记录管理员操作日志
    Logger.info("Admin #{admin_id} recharging #{amount} to user #{user_id}, reason: #{reason}")

    # 创建充值记录
    with {:ok, record} <- create_recharge_record(user_id, amount, "admin", "XAA"),
         # 直接完成充值
         {:ok, completed} <- RechargeRecord.complete_recharge_order(record.order_id, %{
           status: :success,
           gateway_order_id: "ADMIN-#{record.order_id}",
           admin_id: admin_id,
           reason: reason
         }) do

      # 记录管理员操作审计日志
      log_admin_action(admin_id, user_id, amount, reason)

      {:ok, completed}
    end
  end

  defp log_admin_action(admin_id, user_id, amount, reason) do
    # 这里应该有一个审计日志系统
    Logger.info("AUDIT: Admin recharge - admin: #{admin_id}, user: #{user_id}, amount: #{amount}, reason: #{reason}")
  end
end
