defmodule Teen.VipSystem.VipService do
  @moduledoc """
  VIP系统服务（精简版）

  仅保留跨系统协调和复杂业务逻辑：
  - 跨系统事件处理
  - 复杂的多资源协调
  """

  alias Teen.VipSystem.{VipLevel, UserVipInfo}
  alias Phoenix.PubSub

  require Logger

  @doc """
  处理VIP等级变更事件
  
  当用户VIP等级提升时，协调各系统的更新
  """
  def handle_vip_level_change(user_id, old_level, new_level) do
    Logger.info("VIP等级变更: 用户=#{user_id}, #{old_level} -> #{new_level}")
    
    # 1. 发布VIP等级变更事件
    PubSub.broadcast(
      Cypridina.PubSub,
      "vip:level:#{user_id}",
      {:level_changed, %{
        user_id: user_id,
        old_level: old_level,
        new_level: new_level,
        timestamp: DateTime.utc_now()
      }}
    )
    
    # 2. 通知其他系统
    notify_payment_system(user_id, new_level)
    notify_activity_system(user_id, new_level)
    
    # 3. 记录升级日志
    log_level_change(user_id, old_level, new_level)
    
    :ok
  end

  @doc """
  批量重置每日VIP数据
  
  由定时任务调用，重置所有用户的每日数据
  """
  def batch_reset_daily_stats do
    Logger.info("开始批量重置VIP每日数据")
    
    # 这里可以分批处理，避免一次性加载太多数据
    # 实际实现时应该使用流式处理或分页
    case UserVipInfo.reset_daily_stats() do
      {:ok, count} ->
        Logger.info("VIP每日数据重置完成，影响用户数: #{count}")
        {:ok, count}
        
      {:error, reason} ->
        Logger.error("VIP每日数据重置失败: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  处理VIP特权激活
  
  当用户达到某个VIP等级时，激活对应的特权
  """
  def activate_vip_privileges(user_id, vip_level) do
    with {:ok, level_config} <- VipLevel.get_by_level(vip_level),
         privileges <- level_config.privileges || [] do
      
      Enum.each(privileges, fn privilege ->
        activate_single_privilege(user_id, privilege)
      end)
      
      {:ok, length(privileges)}
    end
  end

  # 私有函数

  defp notify_payment_system(user_id, vip_level) do
    # 通知支付系统更新用户的支付限额等
    # 这里可以调用支付系统的API或发送消息
    Logger.debug("通知支付系统: 用户#{user_id} VIP等级更新为 #{vip_level}")
  end

  defp notify_activity_system(user_id, vip_level) do
    # 通知活动系统解锁新的活动
    Logger.debug("通知活动系统: 用户#{user_id} VIP等级更新为 #{vip_level}")
    
    # 发布VIP等级变更事件给活动系统
    Phoenix.PubSub.broadcast(
      Cypridina.PubSub,
      "activity:vip:level_changed",
      {:vip_level_changed, %{
        user_id: user_id,
        new_level: vip_level,
        timestamp: DateTime.utc_now()
      }}
    )
    
    # 检查并解锁新的VIP礼包
    unlock_vip_gifts(user_id, vip_level)
  end

  defp log_level_change(user_id, old_level, new_level) do
    # 记录VIP等级变更日志，用于数据分析
    Logger.info("VIP等级变更记录: user_id=#{user_id}, old=#{old_level}, new=#{new_level}")
  end

  defp activate_single_privilege(user_id, privilege) do
    case privilege do
      "exclusive_games" ->
        # 解锁专属游戏
        Logger.debug("为用户#{user_id}解锁专属游戏")
        
      "priority_withdrawal" ->
        # 开启优先提现
        Logger.debug("为用户#{user_id}开启优先提现")
        
      "exclusive_customer_service" ->
        # 分配专属客服
        Logger.debug("为用户#{user_id}分配专属客服")
        
      _ ->
        Logger.debug("未知的VIP特权: #{privilege}")
    end
  end

  defp unlock_vip_gifts(user_id, vip_level) do
    # 检查并确保该VIP等级的礼包配置存在
    case Teen.ActivitySystem.VipGift.get_by_vip_level(vip_level) do
      {:ok, _vip_gift} ->
        Logger.info("VIP礼包已存在: VIP等级#{vip_level}")
        
        # 发送通知告知用户新的VIP礼包已解锁
        Phoenix.PubSub.broadcast(
          Cypridina.PubSub,
          "user:#{user_id}:notifications",
          {:vip_gift_unlocked, %{
            user_id: user_id,
            vip_level: vip_level,
            message: "恭喜！您已解锁VIP#{vip_level}礼包，可以领取更丰厚的奖励！"
          }}
        )
        
      {:error, _} ->
        Logger.warning("VIP等级#{vip_level}礼包配置不存在，请检查配置")
    end
  end

  @doc """
  处理VIP礼包相关的日常任务
  """
  def handle_daily_vip_tasks do
    Logger.info("开始处理VIP礼包日常任务")
    
    # 自动为符合条件的用户发放VIP礼包
    Teen.ActivitySystem.VipGift.auto_claim_daily_rewards()
    
    # 重置每日VIP数据
    batch_reset_daily_stats()
    
    Logger.info("VIP礼包日常任务处理完成")
  end

  @doc """
  初始化VIP系统
  """
  def initialize_vip_system do
    Logger.info("初始化VIP系统和礼包配置")
    
    # 设置默认VIP礼包配置
    Teen.ActivitySystem.VipGift.setup_default_configs()
    
    Logger.info("VIP系统初始化完成")
  end
end