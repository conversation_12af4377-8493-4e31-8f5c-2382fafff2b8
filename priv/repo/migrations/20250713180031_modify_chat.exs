defmodule Cypridina.Repo.Migrations.ModifyChat do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:broke_award_activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :activity_name, :text, null: false
      add :description, :text
      add :min_balance_threshold, :decimal, null: false, default: "1000"
      add :award_amount, :decimal, null: false, default: "500"
      add :max_claims_per_day, :bigint, null: false, default: 3
      add :max_claims_total, :bigint, null: false, default: 10
      add :cooldown_hours, :bigint, null: false, default: 8
      add :start_date, :utc_datetime, null: false
      add :end_date, :utc_datetime, null: false
      add :status, :text, null: false, default: "active"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:free_cash_activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :activity_name, :text, null: false
      add :description, :text
      add :base_amount, :decimal, null: false, default: "1000"
      add :invitation_bonus, :decimal, null: false, default: "100"
      add :min_invites, :bigint, null: false, default: 0
      add :max_invites, :bigint, null: false, default: 50
      add :max_claims_per_user, :bigint, null: false, default: 1
      add :invite_requirements, :map, null: false
      add :start_date, :utc_datetime, null: false
      add :end_date, :utc_datetime, null: false
      add :status, :text, null: false, default: "active"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:login_cash_activities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :activity_name, :text, null: false
      add :description, :text
      add :reward_type, :text, null: false, default: "cash"
      add :total_reward, :decimal, null: false, default: "0"
      add :task_config, :map, null: false, default: %{}
      add :reward_config, :map, null: false, default: %{}
      add :start_date, :utc_datetime, null: false
      add :end_date, :utc_datetime, null: false
      add :status, :text, null: false, default: "active"

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end
  end

  def down do
    drop table(:login_cash_activities)

    drop table(:free_cash_activities)

    drop table(:broke_award_activities)
  end
end
