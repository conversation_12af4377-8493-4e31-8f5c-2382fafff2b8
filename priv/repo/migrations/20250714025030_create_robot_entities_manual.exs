defmodule Cypridina.Repo.Migrations.CreateRobotEntitiesManual do
  @moduledoc """
  手动创建机器人实体表
  """
  use Ecto.Migration

  def up do
    create table(:robot_entities, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      
      add :robot_id, :integer, null: false
      add :nickname, :string, null: false
      add :avatar_id, :integer, default: 1
      add :level, :integer, default: 1
      
      # 详细的状态管理
      add :status, :string, default: "idle"
      
      # 游戏状态信息
      add :current_game_type, :string
      add :current_room_id, :string
      add :seat_number, :integer
      add :is_in_round, :boolean, default: false
      
      # 积分状态
      add :current_points, :integer, default: 100_000
      add :min_points_threshold, :integer, default: 5_000
      add :last_bet_amount, :integer, default: 0
      
      # 状态时间戳
      add :status_changed_at, :utc_datetime_usec
      add :game_joined_at, :utc_datetime_usec
      add :last_activity_at, :utc_datetime_usec
      
      # 踢出状态
      add :kick_reason, :string
      add :kicked_by, :string
      add :can_be_kicked, :boolean, default: true
      
      # 机器人配置
      add :robot_config, :map, default: %{}
      
      # 管理信息
      add :is_auto_created, :boolean, default: true
      add :creator_admin_id, :string
      add :tags, {:array, :string}, default: []

      timestamps()
    end

    create unique_index(:robot_entities, [:robot_id])
    create index(:robot_entities, [:status])
    create index(:robot_entities, [:current_game_type])
    create index(:robot_entities, [:current_room_id])
    create index(:robot_entities, [:is_auto_created])
  end

  def down do
    drop table(:robot_entities)
  end
end
