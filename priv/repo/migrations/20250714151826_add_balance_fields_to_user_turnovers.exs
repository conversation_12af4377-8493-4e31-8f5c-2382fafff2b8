defmodule Cypridina.Repo.Migrations.AddBalanceFieldsToUserTurnovers do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:user_turnovers) do
      add :bonusmoney, :bigint, null: false, default: 0
      add :bonuscashmoney, :bigint, null: false, default: 0
      add :winningmoney, :bigint, null: false, default: 0
    end
  end

  def down do
    alter table(:user_turnovers) do
      remove :winningmoney
      remove :bonuscashmoney
      remove :bonusmoney
    end
  end
end
