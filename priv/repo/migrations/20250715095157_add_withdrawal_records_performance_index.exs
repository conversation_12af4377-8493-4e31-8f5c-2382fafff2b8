defmodule Cypridina.Repo.Migrations.AddWithdrawalRecordsPerformanceIndex do
  use Ecto.Migration

  def change do
    # 添加复合索引优化提现记录查询性能
    # 这个索引优化了 Home/GetMoneyExLogList 接口的查询
    create index(:withdrawal_records, [:user_id, :inserted_at], 
      name: :withdrawal_records_user_id_inserted_at_index,
      comment: "优化用户提现记录查询性能"
    )
    
    # 添加订单号索引
    create index(:withdrawal_records, [:order_id], 
      name: :withdrawal_records_order_id_index,
      comment: "优化订单号查询性能"
    )
    
    # 添加审核状态索引
    create index(:withdrawal_records, [:audit_status], 
      name: :withdrawal_records_audit_status_index,
      comment: "优化审核状态查询性能"
    )
    
    # 添加处理状态索引
    create index(:withdrawal_records, [:progress_status], 
      name: :withdrawal_records_progress_status_index,
      comment: "优化处理状态查询性能"
    )
  end
end
