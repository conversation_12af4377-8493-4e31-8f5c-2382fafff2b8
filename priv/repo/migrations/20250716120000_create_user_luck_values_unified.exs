defmodule Cypridina.Repo.Migrations.CreateUserLuckValuesUnified do
  @moduledoc """
  创建 user_luck_values 表的统一迁移
  合并了之前的多个迁移文件，创建最终的表结构
  """
  
  use Ecto.Migration

  def up do
    create table(:user_luck_values, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :user_id, references(:users, type: :uuid, on_delete: :delete_all), null: false
      add :current_luck, :integer, null: false, default: -1
      add :last_updated_at, :utc_datetime_usec

      timestamps()
    end

    # 创建索引
    create unique_index(:user_luck_values, [:user_id])
    create index(:user_luck_values, [:current_luck])
    create index(:user_luck_values, [:last_updated_at])
  end

  def down do
    drop table(:user_luck_values)
  end
end