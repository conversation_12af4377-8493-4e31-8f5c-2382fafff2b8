{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "ip_address", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "ip_range", "type": "text"}, {"allow_nil?": false, "default": "\"single\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "type", "type": "text"}, {"allow_nil?": false, "default": "1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "status", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "description", "type": "text"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": {"deferrable": false, "destination_attribute": "id", "destination_attribute_default": null, "destination_attribute_generated": null, "index?": false, "match_type": null, "match_with": null, "multitenancy": {"attribute": null, "global": null, "strategy": null}, "name": "ip_whitelists_created_by_fkey", "on_delete": null, "on_update": null, "primary_key?": true, "schema": "public", "table": "admin_users"}, "scale": null, "size": null, "source": "created_by", "type": "uuid"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "last_used_at", "type": "utc_datetime"}, {"allow_nil?": false, "default": "0", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "use_count", "type": "bigint"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "6170404D8BABFA4228B185FAEF7FE3FB278148F3548C011DF787F8CAC0DA604B", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "ip_whitelists_unique_ip_address_index", "keys": [{"type": "atom", "value": "ip_address"}, {"type": "atom", "value": "type"}], "name": "unique_ip_address", "nils_distinct?": true, "where": "type = 'single'"}, {"all_tenants?": false, "base_filter": null, "index_name": "ip_whitelists_unique_ip_range_index", "keys": [{"type": "atom", "value": "ip_range"}, {"type": "atom", "value": "type"}], "name": "unique_ip_range", "nils_distinct?": true, "where": "type = 'range'"}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "ip_whitelists"}