{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "user_id", "type": "bigint"}, {"allow_nil?": true, "default": "\"teen_patti\"", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "game_type", "type": "text"}, {"allow_nil?": true, "default": "-1", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "current_luck", "type": "bigint"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "last_updated_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "inserted_at", "type": "utc_datetime_usec"}, {"allow_nil?": false, "default": "fragment(\"(now() AT TIME ZONE 'utc')\")", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "updated_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "706EA1C5F861A4E49F320734794D2512FC90EA73F1CEBCD14F7DFB531FE1AA25", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "user_luck_values_unique_user_game_index", "keys": [{"type": "atom", "value": "user_id"}, {"type": "atom", "value": "game_type"}], "name": "unique_user_game", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.Cypridina.Repo", "schema": null, "table": "user_luck_values"}