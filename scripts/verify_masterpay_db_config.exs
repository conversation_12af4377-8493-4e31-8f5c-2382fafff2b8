#!/usr/bin/env elixir

# MasterPay 数据库配置验证脚本
# 运行方式: elixir scripts/verify_masterpay_db_config.exs

IO.puts("🔍 验证MasterPay数据库配置")
IO.puts("=" |> String.duplicate(50))

# 这里需要在实际应用中运行，检查代码加载
IO.puts("""
请在Elixir控制台中运行以下验证命令:

# 1. 验证配置是否可以正常加载
iex> alias Teen.PaymentSystem.PaymentGatewayConfig
iex> {:ok, config} = PaymentGatewayConfig.get_by_code("MASTERPAY")

# 2. 检查配置完整性
iex> config.gateway_name
# 应该返回: "MasterPay Production"

iex> config.status 
# 应该返回: "active"

iex> config.supported_currencies
# 应该返回: ["INR", "USD", "CNY"]

iex> config.supported_payment_methods  
# 应该返回: ["UPI", "NETBANKING", "CARDS", "WALLET"]

# 3. 验证金额限制
iex> config.min_amount
# 应该返回: #Decimal<100> (1 INR in paise)

iex> config.max_amount
# 应该返回: #Decimal<5000000> (50,000 INR in paise)

# 4. 验证费率
iex> config.fee_rate
# 应该返回: #Decimal<0.025> (2.5%)

# 5. 验证配置数据
iex> config.config_data["production_mode"]
# 应该返回: true

iex> config.config_data["signature_method"]
# 应该返回: "MD5"

如果所有验证都通过，说明MasterPay生产配置已正确设置！
""")

IO.puts("\n✅ 配置验证脚本已生成")
IO.puts("📝 请使用 'iex -S mix' 进入控制台执行上述验证命令")