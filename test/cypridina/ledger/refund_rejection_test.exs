defmodule <PERSON>pridina.Ledger.RefundRejectionTest do
  @moduledoc """
  测试退费申请拒绝时的余额刷新功能
  """
  use Cypridina.DataCase

  alias <PERSON><PERSON>ridina.Accounts.User
  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.AgentRelationship
  alias <PERSON><PERSON><PERSON><PERSON>.Accounts.AccountIdentifier
  alias <PERSON><PERSON><PERSON><PERSON>.Ledger
  alias <PERSON><PERSON>ridina.BalanceCache

  describe "reject_refund_request/3" do
    test "拒绝退费申请时正确刷新用户和代理的余额缓存" do
      # 创建用户和代理
      {:ok, user} = User.create_guest_user()
      {:ok, agent} = User.create_guest_user()

      # 建立代理关系
      {:ok, _relationship} =
        AgentRelationship.create(%{
          user_id: user.id,
          agent_id: agent.id,
          status: :active
        })

      # 给用户初始积分
      initial_user_balance = 1000
      {:ok, _} = Cypridina.Accounts.add_points(user.id, initial_user_balance, description: "初始积分")

      # 给代理初始积分
      initial_agent_balance = 500

      {:ok, _} =
        Cypridina.Accounts.add_points(agent.id, initial_agent_balance, description: "代理初始积分")

      # 等待缓存更新
      Process.sleep(100)

      # 验证初始余额
      user_identifier = AccountIdentifier.user(user.id, :XAA)
      agent_identifier = AccountIdentifier.user(agent.id, :XAA)
      {:ok, user_balance_before} = BalanceCache.get_balance(user_identifier)
      {:ok, agent_balance_before} = BalanceCache.get_balance(agent_identifier)

      assert user_balance_before == initial_user_balance
      assert agent_balance_before == initial_agent_balance

      # 创建退费申请（这会将积分从用户转给代理）
      refund_amount = 200
      {:ok, user_account} = Ledger.get_user_account(user.id)
      {:ok, agent_account} = Ledger.get_user_account(agent.id)

      {:ok, refund_transfer} =
        Cypridina.Ledger.Transfer
        |> Ash.Changeset.for_create(:transfer, %{
          amount: Money.new(:XAA, refund_amount),
          from_account_id: user_account.id,
          to_account_id: agent_account.id,
          transaction_type: :refund,
          status: :pending,
          description: "测试退费申请",
          metadata: %{
            refund_reason: "测试原因",
            requested_at: DateTime.utc_now(),
            request_type: "refund",
            requester_id: user.id,
            agent_id: agent.id
          }
        })
        |> Ash.create!()

      # 手动更新缓存（模拟退费申请时的缓存更新）
      BalanceCache.subtract_balance(user_identifier, refund_amount)
      BalanceCache.add_balance(agent_identifier, refund_amount)

      # 等待缓存更新
      Process.sleep(100)

      # 验证退费申请后的余额
      {:ok, user_balance_after_request} = BalanceCache.get_balance(user_identifier)
      {:ok, agent_balance_after_request} = BalanceCache.get_balance(agent_identifier)

      assert user_balance_after_request == initial_user_balance - refund_amount
      assert agent_balance_after_request == initial_agent_balance + refund_amount

      # 拒绝退费申请
      {:ok, _rejected_transfer} =
        Ledger.reject_refund_request(refund_transfer.id, agent.id, "测试拒绝原因")

      # 等待缓存更新
      Process.sleep(100)

      # 验证拒绝后的余额（应该恢复到原来的状态）
      {:ok, user_balance_after_rejection} = BalanceCache.get_balance(user_identifier)
      {:ok, agent_balance_after_rejection} = BalanceCache.get_balance(agent_identifier)

      # 用户余额应该恢复
      assert user_balance_after_rejection == initial_user_balance
      # 代理余额应该恢复
      assert agent_balance_after_rejection == initial_agent_balance

      # 验证从Ledger系统获取的余额也是正确的
      ledger_user_balance = Ledger.get_user_balance(user.id, :XAA)
      ledger_agent_balance = Ledger.get_user_balance(agent.id, :XAA)

      assert ledger_user_balance == initial_user_balance
      assert ledger_agent_balance == initial_agent_balance
    end

    test "拒绝退费申请时正确处理系统账户的余额缓存" do
      # 创建用户
      {:ok, user} = User.create_guest_user()

      # 给用户初始积分
      initial_balance = 1000
      {:ok, _} = Cypridina.Accounts.add_points(user.id, initial_balance, description: "初始积分")

      # 创建一个向系统账户的退费申请
      refund_amount = 200
      {:ok, user_account} = Ledger.get_user_account(user.id)
      system_account = Ledger.get_system_account(:refund)

      {:ok, refund_transfer} =
        Cypridina.Ledger.Transfer
        |> Ash.Changeset.for_create(:transfer, %{
          amount: Money.new(:XAA, refund_amount),
          from_account_id: user_account.id,
          to_account_id: system_account.id,
          transaction_type: :refund,
          status: :pending,
          description: "测试系统退费申请",
          metadata: %{
            refund_reason: "测试系统退费",
            requested_at: DateTime.utc_now(),
            request_type: "refund",
            requester_id: user.id
          }
        })
        |> Ash.create!()

      # 手动更新缓存
      user_identifier = AccountIdentifier.user(user.id, :XAA)
      system_identifier = AccountIdentifier.system(:refund, :XAA)
      BalanceCache.subtract_balance(user_identifier, refund_amount)
      BalanceCache.add_balance(system_identifier, refund_amount)

      # 等待缓存更新
      Process.sleep(100)

      # 验证退费申请后的余额
      {:ok, user_balance_after_request} = BalanceCache.get_balance(user_identifier)
      {:ok, system_balance_after_request} = BalanceCache.get_balance(system_identifier)

      assert user_balance_after_request == initial_balance - refund_amount
      assert system_balance_after_request >= refund_amount

      # 拒绝退费申请
      {:ok, _rejected_transfer} =
        Ledger.reject_refund_request(refund_transfer.id, "system", "测试拒绝原因")

      # 等待缓存更新
      Process.sleep(100)

      # 验证拒绝后的余额
      {:ok, user_balance_after_rejection} = BalanceCache.get_balance(user_identifier)

      # 用户余额应该恢复
      assert user_balance_after_rejection == initial_balance
    end
  end
end
