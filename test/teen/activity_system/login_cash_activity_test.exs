defmodule Teen.ActivitySystem.LoginCashActivityTest do
  use Cypridina.DataCase, async: false

  alias Teen.ActivitySystem.LoginCashActivity

  @valid_attrs %{
    activity_name: "测试登录现金奖励",
    description: "测试活动描述",
    reward_type: :cash,
    total_reward: Decimal.new("2000"),
    task_config: %{
      "login_target" => 1,
      "recharge_target" => 100,
      "game_target" => 10
    },
    reward_config: %{
      "login_reward" => "100",
      "recharge_reward" => "200"
    },
    start_date: DateTime.utc_now(),
    end_date: DateTime.add(DateTime.utc_now(), 30, :day)
  }

  describe "基本CRUD操作" do
    test "创建登录现金活动" do
      assert {:ok, activity} = LoginCashActivity.create(@valid_attrs)
      assert activity.activity_name == "测试登录现金奖励"
      assert activity.reward_type == :cash
      assert activity.status == :active
    end

    test "读取活动信息" do
      {:ok, activity} = LoginCashActivity.create(@valid_attrs)
      
      assert {:ok, found_activity} = LoginCashActivity.read(activity.id)
      assert found_activity.id == activity.id
      assert found_activity.activity_name == @valid_attrs.activity_name
    end

    test "更新活动信息" do
      {:ok, activity} = LoginCashActivity.create(@valid_attrs)
      
      update_attrs = %{activity_name: "更新后的活动名称"}
      assert {:ok, updated_activity} = LoginCashActivity.update(activity, update_attrs)
      assert updated_activity.activity_name == "更新后的活动名称"
    end

    test "删除活动" do
      {:ok, activity} = LoginCashActivity.create(@valid_attrs)
      
      assert {:ok, _} = LoginCashActivity.destroy(activity)
      assert {:error, %Ash.Error.Query.NotFound{}} = LoginCashActivity.read(activity.id)
    end
  end

  describe "活动查询" do
    test "获取活跃活动列表" do
      # 创建活跃活动
      {:ok, _active_activity} = LoginCashActivity.create(@valid_attrs)
      
      # 创建非活跃活动
      inactive_attrs = Map.put(@valid_attrs, :status, :inactive)
      {:ok, _inactive_activity} = LoginCashActivity.create(Map.put(inactive_attrs, :activity_name, "非活跃活动"))
      
      assert {:ok, active_activities} = LoginCashActivity.list_active_activities()
      assert length(active_activities) >= 1
      assert Enum.all?(active_activities, fn activity -> activity.status == :active end)
    end
  end

  describe "业务逻辑" do
    test "获取登录现金数据" do
      user_id = Ecto.UUID.generate()
      
      {:ok, data} = LoginCashActivity.get_login_cash_data(user_id)
      
      assert %{logincash: logincash_data} = data
      assert Map.has_key?(logincash_data, "logincount")
      assert Map.has_key?(logincash_data, "targets")
    end

    test "处理登录现金奖励领取" do
      user_id = Ecto.UUID.generate()
      claim_data = %{"fetchtype" => 0}  # 登录奖励
      
      # 由于没有活动数据，应该返回错误
      assert {:error, %{code: 1, msg: _}} = LoginCashActivity.process_login_cash_claim(user_id, claim_data)
    end
  end

  describe "数据验证" do
    test "活动名称不能为空" do
      invalid_attrs = Map.put(@valid_attrs, :activity_name, nil)
      
      assert {:error, %Ash.Error.Invalid{}} = LoginCashActivity.create(invalid_attrs)
    end

    test "开始时间不能为空" do
      invalid_attrs = Map.put(@valid_attrs, :start_date, nil)
      
      assert {:error, %Ash.Error.Invalid{}} = LoginCashActivity.create(invalid_attrs)
    end

    test "结束时间不能为空" do
      invalid_attrs = Map.put(@valid_attrs, :end_date, nil)
      
      assert {:error, %Ash.Error.Invalid{}} = LoginCashActivity.create(invalid_attrs)
    end
  end
end