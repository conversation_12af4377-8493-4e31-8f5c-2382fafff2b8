defmodule Teen.PaymentSystem.Gateways.MasterPayGatewayTest do
  use ExUnit.Case, async: true

  alias Teen.PaymentSystem.Gateways.MasterPayGateway

  describe "generate_signature/1" do
    test "generates correct signature with valid params" do
      params = %{
        "mchId" => "10236",
        "productId" => "3021",
        "mchOrderNo" => "TEST123",
        "amount" => "10000",
        "clientIp" => "0.0.0.0",
        "notifyUrl" => "http://example.com/notify"
      }

      # Expected signature based on the algorithm:
      # 1. Sort params by key: amount=10000&clientIp=0.0.0.0&mchId=10236&mchOrderNo=TEST123&notifyUrl=http://example.com/notify&productId=3021
      # 2. Append secretKey: &secretKey=ZNH2GYTGVP54ZMM9WSXOLVCKKS9EOBWM
      # 3. MD5 and uppercase

      signature = MasterPayGateway.generate_signature(params)

      assert is_binary(signature)
      # MD5 produces 32 character hex string
      assert String.length(signature) == 32
      # Should be uppercase
      assert signature == String.upcase(signature)
    end

    test "excludes empty values from signature" do
      params = %{
        "mchId" => "10236",
        "productId" => "3021",
        "mchOrderNo" => "TEST123",
        "amount" => "10000",
        "clientIp" => "0.0.0.0",
        "notifyUrl" => "http://example.com/notify",
        # Empty value should be excluded
        "param1" => "",
        # Nil value should be excluded
        "param2" => nil
      }

      signature = MasterPayGateway.generate_signature(params)

      # Should produce same signature as without empty params
      params_without_empty = Map.drop(params, ["param1", "param2"])
      expected_signature = MasterPayGateway.generate_signature(params_without_empty)

      assert signature == expected_signature
    end

    test "excludes sign field from signature" do
      params = %{
        "mchId" => "10236",
        "productId" => "3021",
        "mchOrderNo" => "TEST123",
        "amount" => "10000",
        "clientIp" => "0.0.0.0",
        "notifyUrl" => "http://example.com/notify",
        # Should be excluded
        "sign" => "EXISTING_SIGNATURE"
      }

      signature = MasterPayGateway.generate_signature(params)

      # Should produce same signature as without sign field
      params_without_sign = Map.drop(params, ["sign"])
      expected_signature = MasterPayGateway.generate_signature(params_without_sign)

      assert signature == expected_signature
    end
  end

  describe "verify_callback_signature/1" do
    test "verifies valid signature" do
      params = %{
        "payOrderId" => "MP88123456",
        "mchOrderNo" => "TEST123",
        "amount" => "10000",
        "status" => "1"
      }

      # Generate correct signature
      correct_sign = MasterPayGateway.generate_signature(params)
      params_with_sign = Map.put(params, "sign", correct_sign)

      assert :ok == MasterPayGateway.verify_callback_signature(params_with_sign)
    end

    test "rejects invalid signature" do
      params = %{
        "payOrderId" => "MP88123456",
        "mchOrderNo" => "TEST123",
        "amount" => "10000",
        "status" => "1",
        "sign" => "INVALID_SIGNATURE"
      }

      assert {:error, :signature_mismatch} == MasterPayGateway.verify_callback_signature(params)
    end
  end

  describe "verify_callback_ip/1" do
    test "accepts valid callback IP" do
      assert :ok == MasterPayGateway.verify_callback_ip("*************")
    end

    test "rejects invalid callback IP" do
      assert {:error, :ip_mismatch} == MasterPayGateway.verify_callback_ip("***********")
    end
  end

  describe "get_gateway_config/0" do
    test "returns gateway configuration" do
      config = MasterPayGateway.get_gateway_config()

      assert config.merchant_id == "10236"
      assert config.recharge_channel == "3021"
      assert config.withdrawal_channel == "3020"
      assert config.callback_ip == "*************"
      assert "INR" in config.supported_currencies
      assert config.min_amount == 100
      assert config.max_amount == 10_000_000
      assert config.fee_rate == 0.02
    end
  end
end
