defmodule Teen.PaymentSystem.WithdrawalReactorTest do
  use ExUnit.Case, async: false
  
  alias Teen.PaymentSystem.CreateWithdrawalReactor
  alias Teen.PaymentSystem.WithdrawalRecord
  alias Cypridina.Accounts.User
  alias Cypridina.Ledger
  
  setup do
    # Start the test transaction
    Ecto.Adapters.SQL.Sandbox.checkout(Cypridina.Repo)
    Ecto.Adapters.SQL.Sandbox.mode(Cypridina.Repo, {:shared, self()})
    
    # Create a test user
    {:ok, user} = User.create(%{
      account: "test_user_#{System.unique_integer()}",
      nickname: "Test User",
      password: "password123",
      status: "active",
      payment_banned: false
    })
    
    # Setup user balance
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user.id, :XAA)
    {:ok, _} = Cypridina.Ledger.credit(
      user_identifier,
      100_000,  # 1000 yuan
      transaction_type: :test_deposit,
      description: "Test deposit"
    )
    
    {:ok, user: user}
  end
  
  describe "CreateWithdrawalReactor" do
    test "successfully creates withdrawal with valid params", %{user: user} do
      params = %{
        user_id: user.id,
        withdrawal_amount: Decimal.new("50000"),  # 500 yuan
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "Test Bank",
          account_number: "**********",
          account_name: "Test User"
        }),
        ip_address: "***********"
      }
      
      assert {:ok, result} = CreateWithdrawalReactor.run(params)
      
      # Verify withdrawal record was created
      assert result.user_id == user.id
      assert result.withdrawal_amount == Decimal.new("50000")
      assert result.payment_method == "bank_card"
      assert result.audit_status == 0
      assert result.progress_status == 0
      assert String.starts_with?(result.order_id, "WD")
      
      # Verify balance was deducted
      user_identifier = Cypridina.Ledger.AccountIdentifier.user(user.id, :XAA)
      {:ok, balance} = Cypridina.Ledger.BalanceCache.get_balance(user_identifier)
      assert balance == 50_000  # 1000 - 500 = 500 yuan remaining
    end
    
    test "fails when user has insufficient balance", %{user: user} do
      params = %{
        user_id: user.id,
        withdrawal_amount: Decimal.new("200000"),  # 2000 yuan (more than balance)
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "Test Bank",
          account_number: "**********",
          account_name: "Test User"
        }),
        ip_address: "***********"
      }
      
      assert {:error, reason} = CreateWithdrawalReactor.run(params)
      assert reason =~ "余额不足"
    end
    
    test "fails when payment method info is missing", %{user: user} do
      params = %{
        user_id: user.id,
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: nil,  # Missing bank info
        ip_address: "***********"
      }
      
      assert {:error, _reason} = CreateWithdrawalReactor.run(params)
    end
  end
end