defmodule Teen.Services.WithdrawalIntegrationTest do
  use ExUnit.Case, async: false
  use Cypridina.Case

  alias Teen.Services.WithdrawalService
  alias Teen.Services.TurnoverService
  alias Teen.PaymentSystem.WithdrawalRecord
  alias Teen.PaymentSystem.WithdrawalConfig
  alias Teen.PaymentSystem.Gateways.MasterPayGateway
  alias Teen.Controllers.PaymentCallbackController
  alias <PERSON><PERSON>ridina.Accounts.User
  alias Cypridina.Ledger
  alias Cypridina.Ledger.AccountIdentifier

  @moduletag :integration_test

  # ============================================
  # 集成测试用例
  # ============================================

  describe "完整业务流程集成测试" do
    test "端到端提现流程 - 银行卡成功场景" do
      # 1. 准备测试数据
      user_id = create_test_user()
      admin_id = create_admin_user()
      
      setup_user_balance(user_id, 200_000)
      setup_withdrawal_config("bank_card", 5_000, 100_000)
      setup_user_turnover_complete(user_id)
      
      # 2. 用户发起提现申请
      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "中国银行",
          account_number: "****************",
          account_name: "测试用户",
          ifsc_code: "BKID0006016"
        }),
        ip_address: "*************"
      }

      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      
      # 验证申请创建成功
      assert withdrawal.audit_status == 0
      assert withdrawal.progress_status == 0
      assert withdrawal.user_id == user_id
      
      # 3. 管理员审核通过
      {:ok, audited_withdrawal} = WithdrawalService.audit_withdrawal(
        admin_id,
        withdrawal.id,
        %{audit_status: 1, audit_feedback: "审核通过"}
      )
      
      # 验证审核状态
      assert audited_withdrawal.audit_status == 1
      assert audited_withdrawal.audit_user_id == admin_id
      
      # 4. 系统处理提现（发送到支付网关）
      {:ok, processing_withdrawal} = WithdrawalService.process_withdrawal(audited_withdrawal.id)
      
      # 验证处理状态
      assert processing_withdrawal.progress_status == 1
      assert processing_withdrawal.gateway_order_id != nil
      
      # 5. 模拟支付网关回调成功
      callback_params = %{
        "order_id" => processing_withdrawal.gateway_order_id,
        "status" => "success",
        "amount" => "50000",
        "transaction_id" => "TXN_SUCCESS_123456",
        "timestamp" => DateTime.utc_now() |> DateTime.to_unix()
      }
      
      # 添加签名
      signed_params = MasterPayGateway.sign_callback_params(callback_params)
      
      # 发送回调
      {:ok, completed_withdrawal} = PaymentCallbackController.handle_withdrawal_callback(signed_params)
      
      # 验证最终状态
      assert completed_withdrawal.progress_status == 2  # 支付成功
      assert completed_withdrawal.result_status == 1    # 成功
      assert completed_withdrawal.gateway_transaction_id == "TXN_SUCCESS_123456"
      assert completed_withdrawal.completed_time != nil
      
      # 6. 验证用户余额变化
      final_balance = get_user_balance(user_id)
      expected_balance = Decimal.sub(Decimal.new(200_000), processing_withdrawal.withdrawal_amount)
      assert final_balance == expected_balance
    end

    test "端到端提现流程 - 支付网关失败场景" do
      # 1. 准备测试数据
      user_id = create_test_user()
      admin_id = create_admin_user()
      
      setup_user_balance(user_id, 200_000)
      setup_withdrawal_config("bank_card", 5_000, 100_000)
      setup_user_turnover_complete(user_id)
      
      # 2. 创建并审核通过提现申请
      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "中国银行",
          account_number: "****************",
          account_name: "测试用户"
        }),
        ip_address: "*************"
      }

      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      
      {:ok, audited_withdrawal} = WithdrawalService.audit_withdrawal(
        admin_id,
        withdrawal.id,
        %{audit_status: 1, audit_feedback: "审核通过"}
      )
      
      # 3. 处理提现
      {:ok, processing_withdrawal} = WithdrawalService.process_withdrawal(audited_withdrawal.id)
      
      # 4. 模拟支付网关回调失败
      callback_params = %{
        "order_id" => processing_withdrawal.gateway_order_id,
        "status" => "failed",
        "error_code" => "INSUFFICIENT_FUNDS",
        "error_message" => "银行账户余额不足",
        "timestamp" => DateTime.utc_now() |> DateTime.to_unix()
      }
      
      signed_params = MasterPayGateway.sign_callback_params(callback_params)
      
      {:ok, failed_withdrawal} = PaymentCallbackController.handle_withdrawal_callback(signed_params)
      
      # 验证失败状态
      assert failed_withdrawal.progress_status == 3  # 支付失败
      assert failed_withdrawal.result_status == 2    # 失败
      assert failed_withdrawal.feedback =~ "银行账户余额不足"
      
      # 5. 验证用户余额未变化（失败时应退还）
      final_balance = get_user_balance(user_id)
      assert final_balance == Decimal.new(200_000)  # 余额应该恢复
    end

    test "多支付方式集成测试" do
      user_id = create_test_user()
      admin_id = create_admin_user()
      
      setup_user_balance(user_id, 300_000)
      setup_user_turnover_complete(user_id)
      
      # 设置多种支付方式配置
      setup_withdrawal_config("bank_card", 5_000, 100_000)
      setup_withdrawal_config("alipay", 1_000, 80_000)
      setup_withdrawal_config("upi", 500, 60_000)
      
      payment_methods = [
        {
          "bank_card",
          %{
            bank_name: "中国银行",
            account_number: "****************",
            account_name: "测试用户"
          }
        },
        {
          "alipay",
          %{
            account: "<EMAIL>",
            real_name: "测试用户"
          }
        },
        {
          "upi",
          %{
            upi_id: "testuser@paytm",
            mobile: "**********"
          }
        }
      ]
      
      # 测试每种支付方式
      Enum.each(payment_methods, fn {method, payment_info} ->
        params = %{
          withdrawal_amount: Decimal.new("30000"),
          payment_method: method,
          ip_address: "*************"
        }
        
        # 根据支付方式添加相应的支付信息
        params = case method do
          "bank_card" -> Map.put(params, :bank_info, Jason.encode!(payment_info))
          "alipay" -> Map.put(params, :alipay_info, Jason.encode!(payment_info))
          "upi" -> Map.put(params, :upi_info, Jason.encode!(payment_info))
        end
        
        # 创建提现申请
        {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
        
        # 审核通过
        {:ok, audited} = WithdrawalService.audit_withdrawal(
          admin_id,
          withdrawal.id,
          %{audit_status: 1, audit_feedback: "#{method}审核通过"}
        )
        
        # 处理提现
        {:ok, processed} = WithdrawalService.process_withdrawal(audited.id)
        
        # 验证支付方式特定字段
        assert processed.payment_method == method
        
        case method do
          "bank_card" -> assert processed.bank_info != nil
          "alipay" -> assert processed.alipay_info != nil
          "upi" -> assert processed.upi_info != nil
        end
      end)
    end

    test "并发审核和处理集成测试" do
      # 创建多个提现申请
      user_id = create_test_user()
      admin_id = create_admin_user()
      
      setup_user_balance(user_id, 500_000)
      setup_withdrawal_config("bank_card", 5_000, 100_000)
      setup_user_turnover_complete(user_id)
      
      # 创建多个提现申请
      withdrawals = Enum.map(1..5, fn i ->
        params = %{
          withdrawal_amount: Decimal.new("#{30000 + i * 1000}"),
          payment_method: "bank_card",
          bank_info: Jason.encode!(%{
            bank_name: "测试银行",
            account_number: "622202#{i |> to_string() |> String.pad_leading(10, "0")}"
          }),
          ip_address: "*************"
        }
        
        {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
        withdrawal
      end)
      
      # 并发审核
      audit_tasks = Enum.map(withdrawals, fn withdrawal ->
        Task.async(fn ->
          WithdrawalService.audit_withdrawal(
            admin_id,
            withdrawal.id,
            %{audit_status: 1, audit_feedback: "并发审核通过"}
          )
        end)
      end)
      
      audited_results = Task.await_many(audit_tasks, 10_000)
      
      # 验证所有审核都成功
      assert Enum.all?(audited_results, fn
        {:ok, _} -> true
        _ -> false
      end)
      
      # 并发处理
      audited_withdrawals = Enum.map(audited_results, fn {:ok, withdrawal} -> withdrawal end)
      
      process_tasks = Enum.map(audited_withdrawals, fn withdrawal ->
        Task.async(fn ->
          WithdrawalService.process_withdrawal(withdrawal.id)
        end)
      end)
      
      processed_results = Task.await_many(process_tasks, 10_000)
      
      # 验证所有处理都成功
      assert Enum.all?(processed_results, fn
        {:ok, _} -> true
        _ -> false
      end)
    end
  end

  describe "流水验证集成测试" do
    test "流水不足阻止提现" do
      user_id = create_test_user()
      setup_user_balance(user_id, 200_000)
      setup_withdrawal_config("bank_card", 5_000, 100_000)
      
      # 设置流水不足
      setup_user_turnover_insufficient(user_id, 10_000, 100_000)
      
      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "测试银行",
          account_number: "****************"
        }),
        ip_address: "*************"
      }
      
      {:error, reason} = WithdrawalService.create_withdrawal(user_id, params)
      assert reason =~ "流水不足"
    end

    test "流水完成后允许提现" do
      user_id = create_test_user()
      setup_user_balance(user_id, 200_000)
      setup_withdrawal_config("bank_card", 5_000, 100_000)
      
      # 初始流水不足
      setup_user_turnover_insufficient(user_id, 10_000, 100_000)
      
      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "测试银行",
          account_number: "****************"
        }),
        ip_address: "*************"
      }
      
      # 第一次提现失败
      {:error, _reason} = WithdrawalService.create_withdrawal(user_id, params)
      
      # 补充流水
      setup_user_turnover_complete(user_id)
      
      # 第二次提现成功
      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      assert withdrawal.user_id == user_id
    end
  end

  describe "错误处理集成测试" do
    test "网关通信失败处理" do
      user_id = create_test_user()
      admin_id = create_admin_user()
      
      setup_user_balance(user_id, 200_000)
      setup_withdrawal_config("bank_card", 5_000, 100_000)
      setup_user_turnover_complete(user_id)
      
      # 模拟网关配置错误
      Application.put_env(:cypridina, :master_pay_gateway, [
        endpoint: "http://invalid-gateway.com",
        timeout: 1000
      ])
      
      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "测试银行",
          account_number: "****************"
        }),
        ip_address: "*************"
      }
      
      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      
      {:ok, audited} = WithdrawalService.audit_withdrawal(
        admin_id,
        withdrawal.id,
        %{audit_status: 1, audit_feedback: "审核通过"}
      )
      
      # 处理提现时应该失败
      {:error, reason} = WithdrawalService.process_withdrawal(audited.id)
      assert reason =~ "网关通信失败"
      
      # 恢复正常配置
      Application.put_env(:cypridina, :master_pay_gateway, [
        endpoint: "http://localhost:4000/mock",
        timeout: 5000
      ])
    end

    test "回调签名验证失败" do
      user_id = create_test_user()
      admin_id = create_admin_user()
      
      setup_user_balance(user_id, 200_000)
      setup_withdrawal_config("bank_card", 5_000, 100_000)
      setup_user_turnover_complete(user_id)
      
      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "测试银行",
          account_number: "****************"
        }),
        ip_address: "*************"
      })
      
      {:ok, audited} = WithdrawalService.audit_withdrawal(
        admin_id,
        withdrawal.id,
        %{audit_status: 1, audit_feedback: "审核通过"}
      )
      
      {:ok, processing} = WithdrawalService.process_withdrawal(audited.id)
      
      # 使用错误的签名
      invalid_callback_params = %{
        "order_id" => processing.gateway_order_id,
        "status" => "success",
        "amount" => "50000",
        "signature" => "invalid_signature",
        "timestamp" => DateTime.utc_now() |> DateTime.to_unix()
      }
      
      # 回调应该失败
      {:error, reason} = PaymentCallbackController.handle_withdrawal_callback(invalid_callback_params)
      assert reason =~ "签名验证失败"
    end
  end

  # ============================================
  # 辅助函数
  # ============================================

  defp create_test_user do
    {:ok, user} = User.create(%{
      username: "integ_user_#{System.unique_integer([:positive])}",
      email: "integ#{System.unique_integer([:positive])}@example.com",
      phone_number: "138#{:rand.uniform(********) |> to_string() |> String.pad_leading(8, "0")}",
      password: "password123",
      vip_level: 1
    })
    user.id
  end

  defp create_admin_user do
    {:ok, user} = User.create(%{
      username: "admin_user_#{System.unique_integer([:positive])}",
      email: "admin#{System.unique_integer([:positive])}@example.com",
      phone_number: "139#{:rand.uniform(********) |> to_string() |> String.pad_leading(8, "0")}",
      password: "password123",
      vip_level: 10,
      role: "admin"
    })
    user.id
  end

  defp setup_user_balance(user_id, amount) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    system_identifier = AccountIdentifier.system(:deposit, :XAA)
    
    {:ok, _transfer} = Ledger.transfer(
      system_identifier,
      user_identifier,
      amount,
      transaction_type: :deposit,
      description: "集成测试充值"
    )
  end

  defp setup_withdrawal_config(payment_method, min_amount, max_amount) do
    {:ok, _config} = WithdrawalConfig.create(%{
      config_name: "集成测试配置_#{payment_method}_#{System.unique_integer([:positive])}",
      payment_method: payment_method,
      min_amount: Decimal.new(min_amount),
      max_amount: Decimal.new(max_amount),
      fee_rate: Decimal.new("2.5"),
      tax_rate: Decimal.new("0"),
      status: 1,
      vip_level: 1,
      turnover_multiple: Decimal.new("1")
    })
  end

  defp setup_user_turnover_complete(user_id) do
    # 模拟完成足够的流水
    TurnoverService.update_user_turnover(user_id, %{
      completed_turnover: Decimal.new("200000"),
      required_turnover: Decimal.new("100000")
    })
  end

  defp setup_user_turnover_insufficient(user_id, completed, required) do
    # 模拟流水不足
    TurnoverService.update_user_turnover(user_id, %{
      completed_turnover: Decimal.new(completed),
      required_turnover: Decimal.new(required)
    })
  end

  defp get_user_balance(user_id) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    Ledger.get_balance(user_identifier)
  end
end