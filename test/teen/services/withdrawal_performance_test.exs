defmodule Teen.Services.WithdrawalPerformanceTest do
  use ExUnit.Case, async: false
  use Cypridina.Case

  alias Teen.Services.WithdrawalService
  alias Teen.PaymentSystem.WithdrawalRecord
  alias Teen.PaymentSystem.WithdrawalConfig
  alias Cypridina.Accounts.User
  alias <PERSON><PERSON>ridina.Ledger
  alias <PERSON><PERSON>ridina.Ledger.AccountIdentifier

  @moduletag :performance_test
  @moduletag timeout: 300_000  # 5分钟超时

  # ============================================
  # 性能测试用例
  # ============================================

  describe "并发性能测试" do
    test "并发提现申请处理" do
      # 创建测试数据
      user_count = 10
      concurrent_requests = 5
      
      users = Enum.map(1..user_count, fn _ ->
        user_id = create_test_user()
        setup_user_balance(user_id, 200_000)
        user_id
      end)
      
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      # 并发测试
      start_time = System.monotonic_time(:millisecond)
      
      tasks = Enum.map(users, fn user_id ->
        Task.async(fn ->
          Enum.map(1..concurrent_requests, fn i ->
            params = %{
              withdrawal_amount: Decimal.new("#{10000 + i * 1000}"),
              payment_method: "bank_card",
              bank_info: Jason.encode!(%{
                bank_name: "测试银行",
                account_number: "622202#{:rand.uniform(**********) |> to_string() |> String.pad_leading(10, "0")}"
              }),
              ip_address: "192.168.1.#{:rand.uniform(254)}"
            }
            
            case WithdrawalService.create_withdrawal(user_id, params) do
              {:ok, withdrawal} -> {:success, withdrawal.id}
              {:error, reason} -> {:error, reason}
            end
          end)
        end)
      end)
      
      results = Task.await_many(tasks, 60_000)
      end_time = System.monotonic_time(:millisecond)
      
      # 分析结果
      total_requests = user_count * concurrent_requests
      successful_requests = results |> List.flatten() |> Enum.count(&match?({:success, _}, &1))
      failed_requests = total_requests - successful_requests
      total_time = end_time - start_time
      
      # 性能指标
      avg_response_time = total_time / total_requests
      requests_per_second = total_requests / (total_time / 1000)
      
      IO.puts("=== 并发性能测试结果 ===")
      IO.puts("总请求数: #{total_requests}")
      IO.puts("成功请求数: #{successful_requests}")
      IO.puts("失败请求数: #{failed_requests}")
      IO.puts("总耗时: #{total_time}ms")
      IO.puts("平均响应时间: #{Float.round(avg_response_time, 2)}ms")
      IO.puts("每秒处理请求数: #{Float.round(requests_per_second, 2)}")
      
      # 断言性能要求
      assert avg_response_time < 1000, "平均响应时间应小于1秒"
      assert requests_per_second > 10, "每秒应能处理10个以上请求"
      assert successful_requests / total_requests > 0.95, "成功率应大于95%"
    end

    test "高并发审核处理" do
      # 创建待审核的提现记录
      withdrawal_count = 20
      withdrawals = Enum.map(1..withdrawal_count, fn _ ->
        user_id = create_test_user()
        setup_user_balance(user_id, 100_000)
        create_pending_withdrawal(user_id, 50_000)
      end)
      
      admin_count = 3
      admins = Enum.map(1..admin_count, fn _ -> create_admin_user() end)
      
      # 并发审核
      start_time = System.monotonic_time(:millisecond)
      
      tasks = Enum.with_index(withdrawals) |> Enum.map(fn {withdrawal, index} ->
        admin_id = Enum.at(admins, rem(index, admin_count))
        
        Task.async(fn ->
          case WithdrawalService.audit_withdrawal(admin_id, withdrawal.id, %{
            audit_status: 1,
            audit_feedback: "批量审核通过"
          }) do
            {:ok, audited} -> {:success, audited.id}
            {:error, reason} -> {:error, reason}
          end
        end)
      end)
      
      results = Task.await_many(tasks, 30_000)
      end_time = System.monotonic_time(:millisecond)
      
      # 分析结果
      successful_audits = Enum.count(results, &match?({:success, _}, &1))
      total_time = end_time - start_time
      
      IO.puts("=== 并发审核性能测试结果 ===")
      IO.puts("审核成功数: #{successful_audits}/#{withdrawal_count}")
      IO.puts("总耗时: #{total_time}ms")
      IO.puts("平均审核时间: #{Float.round(total_time / withdrawal_count, 2)}ms")
      
      assert successful_audits == withdrawal_count, "所有审核都应成功"
      assert total_time < 10_000, "总审核时间应小于10秒"
    end

    test "批量查询性能" do
      # 创建大量提现记录
      user_id = create_test_user()
      setup_user_balance(user_id, 1_000_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)
      
      record_count = 100
      Enum.each(1..record_count, fn i ->
        params = %{
          withdrawal_amount: Decimal.new("#{1000 + i * 100}"),
          payment_method: "bank_card",
          bank_info: Jason.encode!(%{
            bank_name: "测试银行",
            account_number: "622202#{i |> to_string() |> String.pad_leading(10, "0")}"
          }),
          ip_address: "*************"
        }
        
        WithdrawalService.create_withdrawal(user_id, params)
      end)
      
      # 测试查询性能
      start_time = System.monotonic_time(:millisecond)
      
      # 分页查询
      page_size = 10
      pages = div(record_count, page_size)
      
      Enum.each(1..pages, fn page ->
        WithdrawalService.get_user_withdrawals(user_id, %{
          page: page,
          page_size: page_size
        })
      end)
      
      end_time = System.monotonic_time(:millisecond)
      query_time = end_time - start_time
      
      IO.puts("=== 批量查询性能测试结果 ===")
      IO.puts("查询#{record_count}条记录，分#{pages}页")
      IO.puts("总查询时间: #{query_time}ms")
      IO.puts("平均单页查询时间: #{Float.round(query_time / pages, 2)}ms")
      
      assert query_time < 5_000, "查询时间应小于5秒"
    end
  end

  describe "压力测试" do
    test "系统极限压力测试" do
      # 创建大量用户
      user_count = 50
      users = Enum.map(1..user_count, fn _ ->
        user_id = create_test_user()
        setup_user_balance(user_id, 500_000)
        user_id
      end)
      
      setup_withdrawal_config("bank_card", 1_000, 100_000)
      
      # 每个用户发起多个提现请求
      requests_per_user = 3
      
      start_time = System.monotonic_time(:millisecond)
      
      # 分批次处理，避免系统过载
      batch_size = 10
      batches = Enum.chunk_every(users, batch_size)
      
      all_results = Enum.reduce(batches, [], fn batch, acc ->
        batch_tasks = Enum.map(batch, fn user_id ->
          Task.async(fn ->
            Enum.map(1..requests_per_user, fn i ->
              params = %{
                withdrawal_amount: Decimal.new("#{20000 + i * 5000}"),
                payment_method: "bank_card",
                bank_info: Jason.encode!(%{
                  bank_name: "测试银行",
                  account_number: "622202#{:rand.uniform(**********) |> to_string() |> String.pad_leading(10, "0")}"
                }),
                ip_address: "192.168.1.#{:rand.uniform(254)}"
              }
              
              case WithdrawalService.create_withdrawal(user_id, params) do
                {:ok, withdrawal} -> {:success, withdrawal.id}
                {:error, reason} -> {:error, reason}
              end
            end)
          end)
        end)
        
        batch_results = Task.await_many(batch_tasks, 30_000)
        
        # 批次间短暂休息
        Process.sleep(100)
        
        acc ++ batch_results
      end)
      
      end_time = System.monotonic_time(:millisecond)
      
      # 分析结果
      all_requests = List.flatten(all_results)
      total_requests = length(all_requests)
      successful_requests = Enum.count(all_requests, &match?({:success, _}, &1))
      failed_requests = total_requests - successful_requests
      total_time = end_time - start_time
      
      IO.puts("=== 系统极限压力测试结果 ===")
      IO.puts("总请求数: #{total_requests}")
      IO.puts("成功请求数: #{successful_requests}")
      IO.puts("失败请求数: #{failed_requests}")
      IO.puts("成功率: #{Float.round(successful_requests / total_requests * 100, 2)}%")
      IO.puts("总耗时: #{total_time}ms")
      IO.puts("平均响应时间: #{Float.round(total_time / total_requests, 2)}ms")
      
      # 在压力测试中，允许适当的失败率
      success_rate = successful_requests / total_requests
      assert success_rate > 0.90, "压力测试成功率应大于90%"
    end

    test "内存使用监控" do
      # 监控内存使用
      initial_memory = :erlang.memory(:total)
      
      user_id = create_test_user()
      setup_user_balance(user_id, 1_000_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)
      
      # 创建大量提现记录
      record_count = 200
      Enum.each(1..record_count, fn i ->
        params = %{
          withdrawal_amount: Decimal.new("#{1000 + i * 100}"),
          payment_method: "bank_card",
          bank_info: Jason.encode!(%{
            bank_name: "测试银行#{i}",
            account_number: "622202#{i |> to_string() |> String.pad_leading(10, "0")}"
          }),
          ip_address: "*************"
        }
        
        WithdrawalService.create_withdrawal(user_id, params)
        
        # 每50个记录检查一次内存
        if rem(i, 50) == 0 do
          current_memory = :erlang.memory(:total)
          memory_increase = current_memory - initial_memory
          
          IO.puts("创建#{i}条记录后，内存增加: #{Float.round(memory_increase / 1024 / 1024, 2)}MB")
        end
      end)
      
      final_memory = :erlang.memory(:total)
      total_memory_increase = final_memory - initial_memory
      
      IO.puts("=== 内存使用监控结果 ===")
      IO.puts("初始内存: #{Float.round(initial_memory / 1024 / 1024, 2)}MB")
      IO.puts("最终内存: #{Float.round(final_memory / 1024 / 1024, 2)}MB")
      IO.puts("内存增长: #{Float.round(total_memory_increase / 1024 / 1024, 2)}MB")
      IO.puts("平均每条记录内存消耗: #{Float.round(total_memory_increase / record_count / 1024, 2)}KB")
      
      # 内存增长不应超过合理范围
      assert total_memory_increase < 100 * 1024 * 1024, "内存增长应小于100MB"
    end
  end

  describe "数据库性能测试" do
    test "数据库连接池压力测试" do
      # 创建大量并发数据库操作
      concurrent_operations = 50
      
      start_time = System.monotonic_time(:millisecond)
      
      tasks = Enum.map(1..concurrent_operations, fn i ->
        Task.async(fn ->
          user_id = create_test_user()
          setup_user_balance(user_id, 100_000)
          
          # 执行多个数据库操作
          operations = [
            fn -> WithdrawalConfig.read_all!() end,
            fn -> WithdrawalRecord.read_all!(user_id: user_id) end,
            fn -> User.read!(user_id) end
          ]
          
          Enum.map(operations, fn operation ->
            try do
              operation.()
              :success
            rescue
              error -> {:error, error}
            end
          end)
        end)
      end)
      
      results = Task.await_many(tasks, 30_000)
      end_time = System.monotonic_time(:millisecond)
      
      # 分析结果
      all_operations = List.flatten(results)
      successful_operations = Enum.count(all_operations, &(&1 == :success))
      total_operations = length(all_operations)
      total_time = end_time - start_time
      
      IO.puts("=== 数据库连接池压力测试结果 ===")
      IO.puts("总操作数: #{total_operations}")
      IO.puts("成功操作数: #{successful_operations}")
      IO.puts("成功率: #{Float.round(successful_operations / total_operations * 100, 2)}%")
      IO.puts("总耗时: #{total_time}ms")
      
      assert successful_operations / total_operations > 0.95, "数据库操作成功率应大于95%"
    end

    test "大数据量查询性能" do
      # 创建大量数据
      user_id = create_test_user()
      setup_user_balance(user_id, 2_000_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)
      
      large_data_count = 500
      IO.puts("创建#{large_data_count}条提现记录...")
      
      creation_start = System.monotonic_time(:millisecond)
      
      # 批量创建数据
      batch_size = 50
      batches = Enum.chunk_every(1..large_data_count, batch_size)
      
      Enum.each(batches, fn batch ->
        Enum.each(batch, fn i ->
          params = %{
            withdrawal_amount: Decimal.new("#{1000 + rem(i, 50) * 100}"),
            payment_method: "bank_card",
            bank_info: Jason.encode!(%{
              bank_name: "测试银行",
              account_number: "622202#{i |> to_string() |> String.pad_leading(10, "0")}"
            }),
            ip_address: "*************"
          }
          
          WithdrawalService.create_withdrawal(user_id, params)
        end)
      end)
      
      creation_end = System.monotonic_time(:millisecond)
      creation_time = creation_end - creation_start
      
      IO.puts("数据创建完成，耗时: #{creation_time}ms")
      
      # 测试各种查询性能
      queries = [
        {"全量查询", fn -> WithdrawalRecord.read_all!(user_id: user_id) end},
        {"分页查询", fn -> WithdrawalRecord.read_all!(user_id: user_id, page: [limit: 20, offset: 0]) end},
        {"条件查询", fn -> WithdrawalRecord.read_all!(user_id: user_id, payment_method: "bank_card") end},
        {"排序查询", fn -> WithdrawalRecord.read_all!(user_id: user_id, sort: [inserted_at: :desc]) end}
      ]
      
      Enum.each(queries, fn {query_name, query_func} ->
        query_start = System.monotonic_time(:millisecond)
        result = query_func.()
        query_end = System.monotonic_time(:millisecond)
        query_time = query_end - query_start
        
        IO.puts("#{query_name}: #{query_time}ms, 返回#{length(result)}条记录")
        
        # 查询时间不应超过3秒
        assert query_time < 3_000, "#{query_name}耗时应小于3秒"
      end)
    end
  end

  # ============================================
  # 辅助函数
  # ============================================

  defp create_test_user do
    {:ok, user} = User.create(%{
      username: "perf_user_#{System.unique_integer([:positive])}",
      email: "perf#{System.unique_integer([:positive])}@example.com",
      phone_number: "138#{:rand.uniform(********) |> to_string() |> String.pad_leading(8, "0")}",
      password: "password123",
      vip_level: 1
    })
    user.id
  end

  defp create_admin_user do
    {:ok, user} = User.create(%{
      username: "admin_user_#{System.unique_integer([:positive])}",
      email: "admin#{System.unique_integer([:positive])}@example.com",
      phone_number: "139#{:rand.uniform(********) |> to_string() |> String.pad_leading(8, "0")}",
      password: "password123",
      vip_level: 10,
      role: "admin"
    })
    user.id
  end

  defp setup_user_balance(user_id, amount) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    system_identifier = AccountIdentifier.system(:deposit, :XAA)
    
    {:ok, _transfer} = Ledger.transfer(
      system_identifier,
      user_identifier,
      amount,
      transaction_type: :deposit,
      description: "性能测试充值"
    )
  end

  defp setup_withdrawal_config(payment_method, min_amount, max_amount) do
    {:ok, _config} = WithdrawalConfig.create(%{
      config_name: "性能测试配置_#{payment_method}_#{System.unique_integer([:positive])}",
      payment_method: payment_method,
      min_amount: Decimal.new(min_amount),
      max_amount: Decimal.new(max_amount),
      fee_rate: Decimal.new("0"),
      tax_rate: Decimal.new("0"),
      status: 1,
      vip_level: 1,
      turnover_multiple: Decimal.new("1")
    })
  end

  defp create_pending_withdrawal(user_id, amount) do
    {:ok, withdrawal} = WithdrawalRecord.create(%{
      user_id: user_id,
      withdrawal_amount: Decimal.new(amount),
      payment_method: "bank_card",
      bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
      current_balance: Decimal.new(100_000),
      withdrawable_balance: Decimal.new(100_000),
      required_turnover: Decimal.new(0),
      completed_turnover: Decimal.new(0),
      fee_amount: Decimal.new(0),
      tax_amount: Decimal.new(0),
      actual_amount: Decimal.new(amount),
      audit_status: 0,  # 待审核
      progress_status: 0,  # 待处理
      ip_address: "*************"
    })
    withdrawal
  end
end