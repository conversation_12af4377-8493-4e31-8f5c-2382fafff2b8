defmodule Teen.Services.WithdrawalSecurityTest do
  use ExUnit.Case, async: false
  use Cypridina.Case

  alias Teen.Services.WithdrawalService
  alias Teen.PaymentSystem.WithdrawalRecord
  alias Teen.PaymentSystem.WithdrawalConfig
  alias Cypridina.Accounts.User
  alias <PERSON><PERSON>ridina.Ledger
  alias Cypridina.Ledger.AccountIdentifier

  @moduletag :security_test

  # ============================================
  # 安全测试用例
  # ============================================

  describe "身份验证和授权测试" do
    test "未认证用户无法提现" do
      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      # 使用无效的用户ID
      {:error, reason} = WithdrawalService.create_withdrawal(nil, params)
      assert reason =~ "用户未认证"
    end

    test "用户只能操作自己的提现记录" do
      user1_id = create_test_user()
      user2_id = create_test_user()
      
      setup_user_balance(user1_id, 100_000)
      setup_user_balance(user2_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      # 用户1创建提现
      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      {:ok, withdrawal1} = WithdrawalService.create_withdrawal(user1_id, params)
      
      # 用户2尝试操作用户1的提现记录
      {:error, reason} = WithdrawalService.get_user_withdrawal(user2_id, withdrawal1.id)
      assert reason =~ "无权限访问"
    end

    test "管理员权限验证" do
      user_id = create_test_user()
      admin_id = create_admin_user()
      
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      
      # 管理员可以审核提现
      {:ok, audited_withdrawal} = WithdrawalService.audit_withdrawal(
        admin_id,
        withdrawal.id,
        %{audit_status: 1, audit_feedback: "审核通过"}
      )
      
      assert audited_withdrawal.audit_status == 1
      assert audited_withdrawal.audit_user_id == admin_id
    end

    test "普通用户无法审核提现" do
      user_id = create_test_user()
      other_user_id = create_test_user()
      
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      
      # 普通用户尝试审核
      {:error, reason} = WithdrawalService.audit_withdrawal(
        other_user_id,
        withdrawal.id,
        %{audit_status: 1, audit_feedback: "审核通过"}
      )
      
      assert reason =~ "权限不足"
    end
  end

  describe "输入参数验证测试" do
    test "SQL注入攻击防护" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      # 尝试SQL注入攻击
      malicious_params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "测试银行'; DROP TABLE users; --",
          account_number: "*********"
        }),
        ip_address: "*************"
      }

      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, malicious_params)
      
      # 验证恶意代码被正确转义
      bank_info = Jason.decode!(withdrawal.bank_info)
      assert bank_info["bank_name"] == "测试银行'; DROP TABLE users; --"
      
      # 验证数据库完整性
      assert {:ok, _} = WithdrawalRecord.read(withdrawal.id)
    end

    test "XSS攻击防护" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      # 尝试XSS攻击
      xss_params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "<script>alert('XSS')</script>",
          account_number: "*********"
        }),
        ip_address: "*************"
      }

      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, xss_params)
      
      # 验证脚本代码被正确处理
      bank_info = Jason.decode!(withdrawal.bank_info)
      assert bank_info["bank_name"] == "<script>alert('XSS')</script>"
    end

    test "金额参数验证" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      # 负数金额
      negative_params = %{
        withdrawal_amount: Decimal.new("-50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      {:error, reason} = WithdrawalService.create_withdrawal(user_id, negative_params)
      assert reason =~ "金额必须为正数"

      # 零金额
      zero_params = %{
        withdrawal_amount: Decimal.new("0"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      {:error, reason} = WithdrawalService.create_withdrawal(user_id, zero_params)
      assert reason =~ "金额必须大于0"

      # 超大金额
      huge_params = %{
        withdrawal_amount: Decimal.new("***************"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      {:error, reason} = WithdrawalService.create_withdrawal(user_id, huge_params)
      assert reason =~ "金额超出系统限制"
    end

    test "IP地址验证" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      # 无效IP地址
      invalid_ip_params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "999.999.999.999"
      }

      {:error, reason} = WithdrawalService.create_withdrawal(user_id, invalid_ip_params)
      assert reason =~ "IP地址格式无效"

      # 内网IP地址
      private_ip_params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "127.0.0.1"
      }

      {:error, reason} = WithdrawalService.create_withdrawal(user_id, private_ip_params)
      assert reason =~ "不允许使用内网IP"
    end

    test "银行卡号验证" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      # 无效银行卡号
      invalid_card_params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "测试银行",
          account_number: "123"  # 太短
        }),
        ip_address: "*************"
      }

      {:error, reason} = WithdrawalService.create_withdrawal(user_id, invalid_card_params)
      assert reason =~ "银行卡号格式无效"

      # 包含非数字字符
      invalid_char_params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "测试银行",
          account_number: "6222021234567abc"
        }),
        ip_address: "*************"
      }

      {:error, reason} = WithdrawalService.create_withdrawal(user_id, invalid_char_params)
      assert reason =~ "银行卡号只能包含数字"
    end
  end

  describe "重复提交防护测试" do
    test "短时间内重复提交防护" do
      user_id = create_test_user()
      setup_user_balance(user_id, 200_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      # 第一次提交成功
      {:ok, withdrawal1} = WithdrawalService.create_withdrawal(user_id, params)
      
      # 立即再次提交相同请求
      {:error, reason} = WithdrawalService.create_withdrawal(user_id, params)
      assert reason =~ "请勿重复提交"
      
      # 验证只创建了一条记录
      withdrawals = WithdrawalRecord.read_all!(user_id: user_id)
      assert length(withdrawals) == 1
    end

    test "相同金额多次提交防护" do
      user_id = create_test_user()
      setup_user_balance(user_id, 300_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      # 第一次提交
      {:ok, _withdrawal1} = WithdrawalService.create_withdrawal(user_id, params)
      
      # 稍作延迟后再次提交相同金额
      Process.sleep(100)
      {:error, reason} = WithdrawalService.create_withdrawal(user_id, params)
      assert reason =~ "存在相同金额的待处理提现"
    end

    test "幂等性保护" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      # 使用相同的幂等性键
      idempotency_key = "test_key_#{System.unique_integer([:positive])}"
      
      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************",
        idempotency_key: idempotency_key
      }

      # 第一次请求
      {:ok, withdrawal1} = WithdrawalService.create_withdrawal(user_id, params)
      
      # 使用相同幂等性键的第二次请求
      {:ok, withdrawal2} = WithdrawalService.create_withdrawal(user_id, params)
      
      # 应该返回相同的结果
      assert withdrawal1.id == withdrawal2.id
      assert withdrawal1.order_id == withdrawal2.order_id
    end
  end

  describe "数据敏感性测试" do
    test "敏感信息脱敏" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "测试银行",
          account_number: "****************",
          account_name: "张三"
        }),
        ip_address: "*************"
      }

      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      
      # 获取用户视图（应该脱敏）
      {:ok, user_view} = WithdrawalService.get_user_withdrawal_view(user_id, withdrawal.id)
      
      bank_info = Jason.decode!(user_view.bank_info)
      # 银行卡号应该被脱敏
      assert bank_info["account_number"] == "622202*********890"
    end

    test "日志记录安全" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "测试银行",
          account_number: "****************"
        }),
        ip_address: "*************"
      }

      {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      
      # 验证日志中不包含敏感信息
      logs = capture_log(fn ->
        WithdrawalService.create_withdrawal(user_id, params)
      end)
      
      refute logs =~ "****************"  # 银行卡号不应出现在日志中
    end
  end

  describe "会话安全测试" do
    test "会话超时检查" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      # 模拟过期会话
      expired_session = %{
        user_id: user_id,
        expires_at: DateTime.add(DateTime.utc_now(), -3600, :second)  # 1小时前过期
      }

      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************",
        session: expired_session
      }

      {:error, reason} = WithdrawalService.create_withdrawal(user_id, params)
      assert reason =~ "会话已过期"
    end

    test "设备指纹验证" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("bank_card", 1_000, 100_000)

      # 正常设备指纹
      normal_params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************",
        device_fingerprint: "normal_device_123"
      }

      {:ok, _withdrawal} = WithdrawalService.create_withdrawal(user_id, normal_params)

      # 可疑设备指纹
      suspicious_params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************",
        device_fingerprint: "suspicious_device_456"
      }

      {:error, reason} = WithdrawalService.create_withdrawal(user_id, suspicious_params)
      assert reason =~ "设备异常，需要额外验证"
    end
  end

  # ============================================
  # 辅助函数
  # ============================================

  defp create_test_user do
    {:ok, user} = User.create(%{
      username: "test_user_#{System.unique_integer([:positive])}",
      email: "test#{System.unique_integer([:positive])}@example.com",
      phone_number: "138#{:rand.uniform(********) |> to_string() |> String.pad_leading(8, "0")}",
      password: "password123",
      vip_level: 1,
      role: "user"
    })
    user.id
  end

  defp create_admin_user do
    {:ok, user} = User.create(%{
      username: "admin_user_#{System.unique_integer([:positive])}",
      email: "admin#{System.unique_integer([:positive])}@example.com",
      phone_number: "139#{:rand.uniform(********) |> to_string() |> String.pad_leading(8, "0")}",
      password: "password123",
      vip_level: 10,
      role: "admin"
    })
    user.id
  end

  defp setup_user_balance(user_id, amount) do
    user_identifier = AccountIdentifier.user(user_id, :XAA)
    system_identifier = AccountIdentifier.system(:deposit, :XAA)
    
    {:ok, _transfer} = Ledger.transfer(
      system_identifier,
      user_identifier,
      amount,
      transaction_type: :deposit,
      description: "测试充值"
    )
  end

  defp setup_withdrawal_config(payment_method, min_amount, max_amount) do
    {:ok, _config} = WithdrawalConfig.create(%{
      config_name: "测试配置_#{payment_method}_#{System.unique_integer([:positive])}",
      payment_method: payment_method,
      min_amount: Decimal.new(min_amount),
      max_amount: Decimal.new(max_amount),
      fee_rate: Decimal.new("0"),
      tax_rate: Decimal.new("0"),
      status: 1,
      vip_level: 1,
      turnover_multiple: Decimal.new("1")
    })
  end

  defp capture_log(fun) do
    ExUnit.CaptureLog.capture_log(fun)
  end
end