defmodule Teen.Services.WithdrawalServiceTest do
  use ExUnit.Case, async: false
  use Cypridina.DataCase

  alias Teen.Services.WithdrawalService
  alias Teen.PaymentSystem.WithdrawalRecord
  alias Teen.PaymentSystem.WithdrawalConfig
  alias Cypridina.Ledger

  describe "create_withdrawal/2" do
    test "正常提现流程" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)  # 1000元
      
      params = %{
        withdrawal_amount: Decimal.new("50000"),  # 500元
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{
          bank_name: "中国银行",
          account_number: "****************",
          account_name: "测试用户"
        }),
        ip_address: "*************"
      }

      assert {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      assert withdrawal.user_id == user_id
      assert withdrawal.withdrawal_amount == Decimal.new("50000")
      assert withdrawal.audit_status == 0
      assert withdrawal.progress_status == 0
      assert String.starts_with?(withdrawal.order_id, "WD")
    end

    test "边界值测试 - 最小金额" do
      user_id = create_test_user()
      setup_user_balance(user_id, 50_000)
      setup_withdrawal_config("bank_card", 1000, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("1000"),  # 最小金额
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      assert {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      assert withdrawal.withdrawal_amount == Decimal.new("1000")
    end

    test "边界值测试 - 最大金额" do
      user_id = create_test_user()
      setup_user_balance(user_id, 200_000)
      setup_withdrawal_config("bank_card", 1000, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("100000"),  # 最大金额
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      assert {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      assert withdrawal.withdrawal_amount == Decimal.new("100000")
    end

    test "异常场景 - 余额不足" do
      user_id = create_test_user()
      setup_user_balance(user_id, 10_000)  # 100元余额

      params = %{
        withdrawal_amount: Decimal.new("50000"),  # 尝试提现500元
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      assert {:error, reason} = WithdrawalService.create_withdrawal(user_id, params)
      assert reason =~ "余额不足"
    end

    test "异常场景 - 金额超出限制" do
      user_id = create_test_user()
      setup_user_balance(user_id, 200_000)
      setup_withdrawal_config("bank_card", 1000, 50_000)  # 最大5万

      params = %{
        withdrawal_amount: Decimal.new("60000"),  # 超出限制
        payment_method: "bank_card",
        bank_info: Jason.encode!(%{bank_name: "测试银行", account_number: "*********"}),
        ip_address: "*************"
      }

      assert {:error, reason} = WithdrawalService.create_withdrawal(user_id, params)
      assert reason =~ "超过最大限额"
    end

    test "异常场景 - 不支持的支付方式" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "invalid_method",  # 无效支付方式
        ip_address: "*************"
      }

      assert {:error, reason} = WithdrawalService.create_withdrawal(user_id, params)
      assert reason =~ "不支持的支付方式"
    end

    test "支付宝提现" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("alipay", 1000, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "alipay",
        alipay_info: Jason.encode!(%{
          account: "<EMAIL>",
          real_name: "测试用户"
        }),
        ip_address: "*************"
      }

      assert {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      assert withdrawal.payment_method == "alipay"
      assert withdrawal.alipay_info != nil
    end

    test "UPI提现" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      setup_withdrawal_config("upi", 1000, 100_000)

      params = %{
        withdrawal_amount: Decimal.new("50000"),
        payment_method: "upi",
        upi_info: Jason.encode!(%{
          upi_id: "test@paytm",
          mobile: "**********"
        }),
        ip_address: "*************"
      }

      assert {:ok, withdrawal} = WithdrawalService.create_withdrawal(user_id, params)
      assert withdrawal.payment_method == "upi"
      assert withdrawal.upi_info != nil
    end
  end

  describe "process_withdrawal/1" do
    test "审核通过后的处理流程" do
      user_id = create_test_user()
      setup_user_balance(user_id, 100_000)
      withdrawal = create_approved_withdrawal(user_id, 50_000)

      assert {:ok, updated_withdrawal} = WithdrawalService.process_withdrawal(withdrawal.id)
      assert updated_withdrawal.progress_status == 1  # 处理中
      assert updated_withdrawal.gateway_order_id != nil
    end

    test "未审核的提现不能处理" do
      user_id = create_test_user()
      withdrawal = create_pending_withdrawal(user_id, 50_000)

      assert {:error, reason} = WithdrawalService.process_withdrawal(withdrawal.id)
      assert reason =~ "未通过审核"
    end
  end

  describe "complete_withdrawal/2" do
    test "成功完成提现" do
      withdrawal = create_processing_withdrawal()
      
      callback_result = %{
        status: :success,
        transaction_id: "TXN123456",
        amount: withdrawal.withdrawal_amount
      }

      assert {:ok, completed_withdrawal} = WithdrawalService.complete_withdrawal(
        withdrawal.order_id, 
        callback_result
      )
      
      assert completed_withdrawal.progress_status == 2  # 支付成功
      assert completed_withdrawal.result_status == 1    # 成功
      assert completed_withdrawal.completed_time != nil
    end

    test "提现失败处理" do
      withdrawal = create_processing_withdrawal()
      
      callback_result = %{
        status: :failed,
        error_code: "INSUFFICIENT_FUNDS",
        error_message: "银行账户余额不足"
      }

      assert {:ok, failed_withdrawal} = WithdrawalService.complete_withdrawal(
        withdrawal.order_id, 
        callback_result
      )
      
      assert failed_withdrawal.progress_status == 3  # 支付失败
      assert failed_withdrawal.result_status == 2    # 失败
      assert failed_withdrawal.feedback =~ "Gateway payment failed"
    end
  end

  # 辅助函数
  defp create_test_user do
    {:ok, user} = Cypridina.Accounts.User.create(%{
      username: "test_user_#{System.unique_integer()}",
      email: "test#{System.unique_integer()}@example.com",
      phone_number: "138#{:rand.uniform(********) |> to_string() |> String.pad_leading(8, "0")}"
    })
    user.id
  end

  defp setup_user_balance(user_id, amount) do
    user_identifier = Cypridina.Ledger.AccountIdentifier.user(user_id, :XAA)
    system_identifier = Cypridina.Ledger.AccountIdentifier.system(:deposit, :XAA)
    
    {:ok, _transfer} = Ledger.transfer(
      system_identifier,
      user_identifier,
      amount,
      transaction_type: :deposit,
      description: "测试充值"
    )
  end

  defp setup_withdrawal_config(payment_method, min_amount, max_amount) do
    {:ok, _config} = WithdrawalConfig.create(%{
      config_name: "测试配置_#{payment_method}",
      payment_method: payment_method,
      min_amount: Decimal.new(min_amount),
      max_amount: Decimal.new(max_amount),
      fee_rate: Decimal.new("2.5"),
      status: 1
    })
  end

  defp create_approved_withdrawal(user_id, amount) do
    {:ok, withdrawal} = WithdrawalRecord.create(%{
      user_id: user_id,
      withdrawal_amount: Decimal.new(amount),
      payment_method: "bank_card",
      bank_info: Jason.encode!(%{bank_name: "测试银行"}),
      current_balance: Decimal.new(100_000),
      withdrawable_balance: Decimal.new(100_000),
      required_turnover: Decimal.new(0),
      completed_turnover: Decimal.new(0),
      fee_amount: Decimal.new(0),
      tax_amount: Decimal.new(0),
      actual_amount: Decimal.new(amount),
      audit_status: 1,  # 已审核
      progress_status: 0  # 待处理
    })
    withdrawal
  end

  defp create_pending_withdrawal(user_id, amount) do
    {:ok, withdrawal} = WithdrawalRecord.create(%{
      user_id: user_id,
      withdrawal_amount: Decimal.new(amount),
      payment_method: "bank_card",
      current_balance: Decimal.new(100_000),
      withdrawable_balance: Decimal.new(100_000),
      required_turnover: Decimal.new(0),
      completed_turnover: Decimal.new(0),
      fee_amount: Decimal.new(0),
      tax_amount: Decimal.new(0),
      actual_amount: Decimal.new(amount),
      audit_status: 0  # 待审核
    })
    withdrawal
  end

  defp create_processing_withdrawal do
    user_id = create_test_user()
    {:ok, withdrawal} = WithdrawalRecord.create(%{
      user_id: user_id,
      withdrawal_amount: Decimal.new(50_000),
      payment_method: "bank_card",
      current_balance: Decimal.new(100_000),
      withdrawable_balance: Decimal.new(100_000),
      required_turnover: Decimal.new(0),
      completed_turnover: Decimal.new(0),
      fee_amount: Decimal.new(0),
      tax_amount: Decimal.new(0),
      actual_amount: Decimal.new(50_000),
      audit_status: 1,     # 已审核
      progress_status: 1   # 处理中
    })
    withdrawal
  end
end