#!/usr/bin/env elixir

# 简单的提现接口测试脚本
Mix.install([
  {:jason, "~> 1.4"},
  {:decimal, "~> 2.0"}
])

# 模拟用户ID
user_id = "550e8400-e29b-41d4-a716-************"

# 准备提现参数
withdrawal_params = %{
  withdrawal_amount: Decimal.new("1000"),
  payment_method: "bank_card",
  bank_info: Jason.encode!(%{
    bank_name: "Industrial and Commercial Bank of China",
    account_number: "6212260200023557777",
    account_holder: "张三",
    ifsc_code: "ICBC0000123"
  }),
  ip_address: "*************"
}

IO.puts("模拟提现接口调用:")
IO.puts("用户ID: #{user_id}")
IO.puts("提现金额: #{withdrawal_params.withdrawal_amount}")
IO.puts("支付方式: #{withdrawal_params.payment_method}")
IO.puts("银行信息: #{withdrawal_params.bank_info}")
IO.puts("IP地址: #{withdrawal_params.ip_address}")

# 模拟提现服务调用
IO.puts("\n模拟提现服务调用结果:")
mock_withdrawal_result = %{
  order_id: "WD" <> to_string(DateTime.utc_now() |> DateTime.to_unix(:millisecond)),
  user_id: user_id,
  withdrawal_amount: withdrawal_params.withdrawal_amount,
  payment_method: withdrawal_params.payment_method,
  audit_status: 0,  # 待审核
  progress_status: 0,  # 排队中
  result_status: 0,  # 处理中
  bank_info: withdrawal_params.bank_info,
  ip_address: withdrawal_params.ip_address,
  inserted_at: DateTime.utc_now()
}

IO.puts("✅ 提现申请创建成功!")
IO.inspect(mock_withdrawal_result, pretty: true)

IO.puts("\n📋 提现申请处理流程:")
IO.puts("1. ✅ 验证用户信息")
IO.puts("2. ✅ 检查余额充足性")
IO.puts("3. ✅ 验证流水要求")
IO.puts("4. ✅ 创建提现记录")
IO.puts("5. ⏳ 等待审核")
IO.puts("6. ⏳ 支付处理")
IO.puts("7. ⏳ 完成提现")